package com.faw.sa0214.tak.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.AIScoreFeignClient;
import com.faw.sa0214.tak.client.OpenApiGatewayService;
import com.faw.sa0214.tak.common.util.HttpUtil;
import com.faw.sa0214.tak.common.util.JsonUtil;
import com.faw.sa0214.tak.config.UcgConfig;
import com.faw.sa0214.tak.mapper.*;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.request.AIOBackInfo;
import com.faw.sa0214.tak.model.request.AIOCustomField;
import com.faw.sa0214.tak.model.request.AIOTaskResult;
import com.faw.sa0214.tak.model.request.AsyncAIRequest;
import com.faw.sa0214.tak.po.*;
import com.faw.sa0214.tak.service.AiScoreService;
import com.faw.sa0214.tak.service.AiUtilService;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
@Slf4j
@Component
public class AiScoreServiceImpl extends ServiceImpl<AIScoreMapper, AIScorePO> implements AiScoreService {
    @Value("${score.deliverableIds}")
    private String deliverableIds;
    private final static String AI_CODE_CALLBACK = "/JT/BA/BA-0208/007/DEFAULT/codeResultSave?access_token=";

    private final UcgConfig ucgConfig;
    private final AIScoreFeignClient aiScoreFeignClient;
    private final OpenApiGatewayService openApiGatewayService;
    private final AiUtilService aiUtilService;
    private final AIScoreMapper aiScoreMapper;
    private final ScoreRuleMapper scoreRuleMapper;
    private final AIScoreLogMapper aiScoreLogMapper;
    private final AIServiceLogMapper aiServiceLogMapper;
    private final DeliverableItemMapper deliverableItemMapper;
    private final AiCodeEvaluationMapper aiCodeEvaluationMapper;

    @Override
    public Result<Boolean> aiScore(List<AIFileScoreDTO> aiFileScoreDTOList) {
        log.info("aiScore start AIFileScoreDTOList is {}", JsonUtil.toJsonString(aiFileScoreDTOList));

        // 校验参数
        if (CollectionUtils.isEmpty(aiFileScoreDTOList)) {
            return Result.failed("待打分列表不能为空");
        }

        aiFileScoreDTOList.forEach(aiScoreDTO -> {

            // 查询交付物信息
            DeliverableItemPO deliverable = deliverableItemMapper.selectByPrimaryKey(aiScoreDTO.getDeliverableId());

            // 记录AI评分日志
            AIScoreLog aiScoreLog = new AIScoreLog();
            BeanUtils.copyProperties(aiScoreDTO, aiScoreLog);
            aiScoreLog.setTaskId(Long.parseLong(aiScoreDTO.getTaskId()));   // taskId 类型不同，单独处理
            aiScoreLog.setVersion(aiScoreDTO.getDocVersion());   // 文件版本号
            aiScoreLog.setBizType(deliverable.getDeliverableBizType());   // 交付物评分规则
            aiScoreLogMapper.insertAIScoreLog(aiScoreLog);

            // 异步进行文档解析
            aiUtilService.getDocFromDevops(aiScoreLog, deliverable);

        });

        return Result.success(true);
    }

    /**
     * 交付物评分补偿
     * @return
     */
    @Override
    public String delAiCompensation(AiCompensationDTO dto) {
        log.info("delAiCompensation scoreIdList:{}", JsonUtil.toJsonString(dto));

        List<AIScoreLog> scoreList;
        if(dto != null && CollectionUtil.isNotEmpty(dto.getScoreIdList())){
            scoreList = aiScoreLogMapper.selectScoreLogList(dto.getScoreIdList());
        } else {
            // 筛选 3天前 到 1个小时前 的待评分数据
            scoreList = aiScoreLogMapper.getCompensationLogs();
        }

        if(CollectionUtil.isNotEmpty(scoreList)){
            // 查询交付物信息
            List<Integer> deliverableIdList = scoreList.stream().map(AIScoreLog::getDeliverableId).toList();
            List<DeliverableItemPO> deliverableList = deliverableItemMapper.selectByPrimaryKeys(deliverableIdList);

            for(AIScoreLog aiScoreLog : scoreList){
                log.info("delAiCompensation aiScoreLog:{}", JsonUtil.toJsonString(aiScoreLog));
                DeliverableItemPO deliverable = deliverableList.stream()
                        .filter(deliverableItemPO -> deliverableItemPO.getId().equals(aiScoreLog.getDeliverableId()))
                        .findFirst().orElse(null);
                // 异步进行文档解析
                aiUtilService.getDocFromDevops(aiScoreLog, deliverable);
            }
        }

        return "success!";
    }

    @Override
    public Result<Boolean> score(List<AIFileScoreDTO> aiFileScoreDTOList) {
        log.info("score start AIFileScoreDTOList is {}", JsonUtil.toJsonString(aiFileScoreDTOList));
        if (CollectionUtils.isEmpty(aiFileScoreDTOList)) {
            return Result.failed("待打分列表不能为空");
        }

        aiFileScoreDTOList.forEach(aiFileScoreDTO -> {
            AIScorePO aiScorePO = aiScoreMapper.getByTaskIdAndFileId(aiFileScoreDTO.getTaskId(), aiFileScoreDTO.getFileId());
            if (aiScorePO == null) {
                aiScorePO = new AIScorePO();
            }

            BeanUtils.copyProperties(aiFileScoreDTO, aiScorePO);
            aiScorePO.setCreateTime(new Date());
            aiScorePO.setUpdateTime(new Date());
            aiScorePO.setStatus(0);
            aiScorePO.setCallbackStatus(0);
            if (StringUtils.isEmpty(aiFileScoreDTO.getRelateType())) {
                aiScorePO.setRelateType("delivery_project");
            }
            if (!StringUtils.isEmpty(aiFileScoreDTO.getDocVersion())) {
                aiScorePO.setVersion(aiFileScoreDTO.getDocVersion());
            }

            if (aiFileScoreDTO.getDeliverableId() != null) {
                DeliverableItemPO deliverablePO = deliverableItemMapper.selectByPrimaryKey(aiFileScoreDTO.getDeliverableId());
                if (deliverablePO != null) {
                    aiScorePO.setType(deliverablePO.getDeliverableBizType());
                    // 异步进行文档解析
                    aiUtilService.getDataFromDevops(aiScorePO, deliverablePO.getDeliverableFormatType());
                }
            }
        });

        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> goToScore() {

        // 查询待评分的任务
        AIScorePO aiScorePO = aiScoreMapper.selectOneToScore();
        if (aiScorePO == null) {
            return Result.failed("不存在符合打分的文档");
        }

        AIScoreDTO aiScoreDTO = new AIScoreDTO();
        if (!StringUtils.isEmpty(aiScorePO.getPaper())) {
            log.info("goToScore taskId = {}, fileId = {}, fileName = {}", aiScorePO.getTaskId(), aiScorePO.getFileId(), aiScorePO.getFileName());

            // 判断内容长度
            if (aiScorePO.getPaper().length() > 30000) {
                AIScorePO update = new AIScorePO();
                update.setFileId(aiScorePO.getFileId());
                update.setTaskId(aiScorePO.getTaskId());
                update.setStatus(2);
                update.setScore(80.0);
                update.setScoreDesc("文档内容过长");
                aiScoreMapper.updateAiScoreByfileIdAndTaskId(update);
                // 调用接口通知
                aiUtilService.callBackDevops(aiScorePO.getTaskId());
                return Result.success(true);
            }

            // 判断是否进行过历史评分
            boolean isExsit = aiUtilService.checkDocExists(aiScorePO);
            if (!isExsit) {
                aiScoreDTO.setContent(aiScorePO.getPaper().replace("\\n", ""));
                aiScoreDTO.setScoreType(aiScorePO.getType());
                AIScoreResultDTO score;
                try {
                    List<String> deliverableIdList = Arrays.asList(deliverableIds.split(","));
                    if (deliverableIdList.contains(aiScorePO.getDeliverableId().toString())) {
                        return prdAiScore(aiScorePO);
                    } else {
                        log.info("--- goToScore 调用打分接口 taskId = {}, fileId = {}, fileName = {} --- param: {}", aiScorePO.getTaskId(), aiScorePO.getFileId(), aiScorePO.getFileName(), JsonUtil.toJsonString(aiScoreDTO));
                        score = aiScoreFeignClient.score(aiScoreDTO);
                        saveAiServiceLog(null, aiScorePO.getTaskId(), aiScorePO.getFileId().toString(), aiScorePO.getDeliverableId().toString(),
                                "/score", JsonUtil.toJsonString(aiScoreDTO), JsonUtil.toJsonString(score));
                    }
                } catch (Exception e) {
                    log.error("goToScore 调用打分接口异常 Exception {}", e.getMessage());
                    recordScoreNumber(aiScorePO);
                    return Result.failed("goToScore 调用打分接口异常");
                }

                if (score.getSuccess() != null && score.getSuccess().equals(Boolean.TRUE)) {
                    // 计算分数，更新评分结果
                    recordCorrectScore(aiScorePO, aiScorePO.getType(), score.getData());
                } else {
                    // 如果该文档打分失败超过3次则认为已评分且评分为0，防止一直打分该文档
                    recordScoreNumber(aiScorePO);
                }
            }

        } else {
            // 不符合打分条件，直接赋值0分
            AIScorePO update = new AIScorePO();
            update.setFileId(aiScorePO.getFileId());
            update.setTaskId(aiScorePO.getTaskId());
            update.setStatus(2);
            update.setScore(0.0);
            update.setScoreDesc("交付物解析异常，请检查交付物内容！");
            aiScoreMapper.updateAiScoreByfileIdAndTaskId(update);
            // 调用接口通知
            aiUtilService.callBackDevops(aiScorePO.getTaskId());
        }

        return Result.success(true);
    }

    // prd 文档评分
    private Result<Boolean> prdAiScore(AIScorePO aiScore) {
        LambdaQueryWrapper<AIScorePO> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper1.eq(AIScorePO::getFileId, aiScore.getFileId());
        lambdaQueryWrapper1.eq(AIScorePO::getTaskId, aiScore.getTaskId());

        String traceId = UUID.randomUUID().toString();

        // 请求内容
        Map<String, String> givenInfo = new HashMap<>();
        givenInfo.put("taskId", aiScore.getTaskId());
        givenInfo.put("fileId", aiScore.getFileId().toString());
        givenInfo.put("type", aiScore.getType().toString());
        givenInfo.put("data", aiScore.getPaper());

        // 文件信息
        String version = StringUtils.isBlank(aiScore.getVersion()) ? "1.0" : aiScore.getVersion();
        List<BeCheckFileDTO> beCheckFileList = new ArrayList<>();
        BeCheckFileDTO beCheckFileDTO = BeCheckFileDTO.builder()
                .fileUrl(ucgConfig.getUcgHost())
                .fileDesc("交付物AI评分：" + aiScore.getFileName())
                .fileIndex(Integer.toString(0))
                .fileContentType("0")
                .version(version)
                .build();
        beCheckFileList.add(beCheckFileDTO);

        // 组装参数
        AiRequestDTO aiRequestDTO = AiRequestDTO.builder()
                .systemId("RWYYZX")
                .bizId(aiScore.getTaskId())
                .bizType(aiScore.getFileId().toString())
                .contents(beCheckFileList)
                .taskType(aiScore.getType().toString())
                .givenInfoJson(JsonUtil.toJsonString(givenInfo))
                .givenInfoJsonDesc("交付物AI评分：" + aiScore.getFileName())
                .callbackUrl(ucgConfig.getAiCallBackUrl())
                .callbackType(1)
                .traceId(traceId)
                .version(version)
                .build();
        try {
            log.info("--- 请求AI服务--- prdAiScore.params: {}", JSON.toJSONString(aiRequestDTO));
            Result<String> response = aiScoreFeignClient.prdAIscore(aiRequestDTO);
            saveAiServiceLog(null, aiScore.getTaskId(), aiScore.getFileId().toString(), aiScore.getDeliverableId().toString(),
                    "/dataReviewRwyyzx", JsonUtil.toJsonString(aiRequestDTO), JsonUtil.toJsonString(response));

            if (response.isSuccess() && StringUtils.isNotBlank(response.getData())) {
                AIScorePO aiScoreUpdate = new AIScorePO();
                aiScoreUpdate.setStatus(1);
                aiScoreUpdate.setScoreNum(1);
                aiScoreMapper.update(aiScoreUpdate, lambdaQueryWrapper1);
            } else {
                // 如果该文档打分失败超过3次则认为已评分且评分为0，防止一直打分该文档
                recordScoreNumber(aiScore);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("--- 请求AI服务异常--- fileName：{}, e:{}", aiScore.getFileName(), e);
            recordScoreNumber(aiScore);
        }

        return Result.success(true);
    }

    @Override
    public String getAIScoreResult(AsyncAIRequest request) {
        log.info("AI请求了回调地址,入参：" + JSON.toJSONString(request));

        // 查询评分数据
        String bizidStr = request.getBizId();
        Long bizId = Long.parseLong(bizidStr);
        AIScoreLog aiScoreLog = aiScoreLogMapper.getScoreLogById(bizId);
        if (aiScoreLog == null) {
            return "bizId is wrong";
        }
        // 保存日志
        saveAiServiceLog(request.getBizId(), aiScoreLog.getTaskId().toString(), aiScoreLog.getFileId().toString(),
                aiScoreLog.getDeliverableId().toString(), "/ai/aiCallback", null, JsonUtil.toJsonString(request));

        try {
            if(request.isSuccess() && StringUtils.isNotBlank(request.getTaskResult())){
                AIOBackInfo aioBackInfo = new Gson().fromJson(request.getTaskResult(), AIOBackInfo.class);
                if(aioBackInfo.isT() && StringUtils.isNotBlank(aioBackInfo.getCustomField())){
                    List<AIOTaskResult> taskResults = new Gson().fromJson(aioBackInfo.getCustomField(), new TypeToken<List<AIOTaskResult>>(){}.getType());

                    // 判断是否全部都符合规则
                    boolean allQualified = taskResults.stream().allMatch(AIOTaskResult::isQualified);

                    // 获取分数规则列表
                    String rule = scoreRuleMapper.getRuleByType(aiScoreLog.getBizType());
                    List<String> ruleList = Arrays.stream(rule.split(",")).toList();

                    double score = 0.0;
                    StringBuilder scoreDesc = new StringBuilder();
                    for (int i=0; i<taskResults.size(); i++){
                        scoreDesc.append(taskResults.get(i).getExplain()).append(";\n");  // 拼接描述
                        if (taskResults.get(i).isQualified() && i < ruleList.size()) {
                            score += Double.parseDouble(String.valueOf(ruleList.get(i)));
                        }
                    }
                    if(allQualified || score > 100){
                        score = 100.0;
                    }

                    // 解析描述超长截断
                    if (scoreDesc.length() > 1000){
                        scoreDesc.setLength(999);
                    }

                    // 更新结果并回调
                    aiUtilService.updateAndCallBack(aiScoreLog.getId(), aiScoreLog.getTaskId(), score, scoreDesc.toString(), "-");
                    return "success";
                } else {
                    aiUtilService.updateAndCallBack(aiScoreLog.getId(), aiScoreLog.getTaskId(), 0.0, "AIO评分失败： " + aioBackInfo.getExplanation(), "-");
                    return "AIO评分回调失败!";
                }
            } else {
                aiUtilService.updateAndCallBack(aiScoreLog.getId(), aiScoreLog.getTaskId(), 0.0, "AIO评分失败： " + request.getMessage(), "-");
                return "AIO评分回调失败!";
            }
        } catch (Exception e) {
            log.warn("getAIScoreResult error! e: {}", e.getMessage());
            aiUtilService.updateAndCallBack(aiScoreLog.getId(), aiScoreLog.getTaskId(), 0.0, "AIO评分回调数据异常!", "-");
            return "AIO评分回调数据解析失败!";
        }
    }

    /**
     * AI 对外服务回调接口
     */
    @Override
    public String aiCallback(AiCallbackRequest param) {
        log.info("AI请求了回调地址,入参：" + JSON.toJSONString(param));

        // 保存日志
        saveAiServiceLog(null, param.getBizId(), param.getBizType(), null, "/ai/aiCallback", null, JsonUtil.toJsonString(param));

        // 取分数
        JsonObject jsonObject = JsonParser.parseString(param.getTaskResult()).getAsJsonObject();
        double score = jsonObject.get("score").getAsDouble();

        // 取描述
        StringBuilder desc = new StringBuilder();
        try {
            // 去除多余格式
            String message = param.getMessage()
                    .replaceAll("json", "")
                    .replaceAll("```", "")
                    .replaceAll("\n", "")
                    .trim();
            if (JsonParser.parseString(message).isJsonArray()) {
                JsonArray jsonArray = JsonParser.parseString(message).getAsJsonArray();
                for (JsonElement element : jsonArray) {
                    JsonObject obj = element.getAsJsonObject();
                    String explain = obj.get("explain").getAsString();
                    desc.append(explain).append("\n");
                }
            } else if (JsonParser.parseString(message).isJsonObject()) {
                JsonObject obj = JsonParser.parseString(message).getAsJsonObject();
                String explain = obj.get("explain").getAsString();
                desc.append(explain);
            } else {
                String stringValue = JsonParser.parseString(message).getAsString();
                desc.append(stringValue);
            }
            if (StringUtils.isBlank(desc)) {
                desc.append("-");
            }
        } catch (Exception e) {
            log.error("--- AI 对外服务回调接口 --- message 解析异常！入参：" + JSON.toJSONString(param.getMessage()));
            desc.append(param.getMessage());
        }

        // 处理描述过长
        if (desc.length() > 1000) {
            desc.setLength(999);
        }

        // 查询任务 docHash 值
        AIScorePO docHashData = aiScoreMapper.getDocHash(param.getBizId(), Long.parseLong(param.getBizType()));
        // 查询 交付物相同，未评分得记录
        List<AIScorePO> aiScoreList = aiScoreMapper.getListByDocHash(docHashData.getDocHash(), docHashData.getDeliverableId());
        for(AIScorePO aiScore : aiScoreList){
            AIScorePO aiScoreUpdate = new AIScorePO();
            aiScoreUpdate.setTaskId(aiScore.getTaskId());
            aiScoreUpdate.setFileId(aiScore.getFileId());
            aiScoreUpdate.setScore(score);
            aiScoreUpdate.setScoreDesc(desc.toString());
            aiScoreUpdate.setStatus(2);
            aiScoreUpdate.setScoreNum(1);
            aiScoreMapper.updateAiScoreByfileIdAndTaskId(aiScoreUpdate);

            // 调用接口通知
            aiUtilService.callBackDevops(aiScore.getTaskId());
        }

        return "success！";
    }

    @Override
    public Result<Boolean> scoreCallback(ScoreResultDTO scoreResultDTO) {
        log.info("scoreCallback scoreResultDTO {}", scoreResultDTO);
        Long fileId = scoreResultDTO.getFileId();
        String message = scoreResultDTO.getMessage();
        String data = scoreResultDTO.getData();
        if (fileId != null && message != null && data != null) {
            LambdaQueryWrapper<AIScorePO> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
            lambdaQueryWrapper2.eq(AIScorePO::getFileId, fileId);
            lambdaQueryWrapper2.last("limit 1");
            AIScorePO aiScorePO2 = aiScoreMapper.selectOne(lambdaQueryWrapper2);
            AIScorePO aiScorePOUpdate1 = new AIScorePO();
            aiScorePOUpdate1.setScoreDesc(message);
            aiScorePOUpdate1.setStatus(2);
            if (data.contains("{")) {
                //说明是json表达式 解析data关联规则表算出来实际分值
                recordCorrectScore(aiScorePO2, aiScorePO2.getType(), data);
            } else {
                // 说明没有评分，按0计算
                aiScorePOUpdate1.setScore(0.0);
                LambdaQueryWrapper<AIScorePO> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
                lambdaQueryWrapper1.eq(AIScorePO::getFileId, fileId);
                log.info("goToScore end success update result{}", aiScorePOUpdate1);
                aiScoreMapper.update(aiScorePOUpdate1, lambdaQueryWrapper1);
                log.info("scoreCallback end aiScorePOUpdate1{}", aiScorePOUpdate1);
                aiUtilService.callBackDevops(aiScorePO2.getTaskId());

            }
            return Result.success(true);
        } else {
            log.error("scoreCallback end failed");
            return Result.failed("回调数据有问题");
        }
    }

    @Override
    public Result<Boolean> goToCallbackDevops() {
        log.info("goToCallbackDevops start");
        List<String> taskIds = aiScoreMapper.selectUnCallbackTaskIds();
        if (CollectionUtils.isEmpty(taskIds)) {
            return Result.failed("没有需要回调给Devops的评分数据");
        }

        log.info("goToCallbackDevops aiScorePOList size:{}", taskIds.size());
        for(String taskId : taskIds){
            aiUtilService.callBackDevops(taskId);
        }

        log.info("goToCallbackDevops end");
        return Result.success(true);
    }

    /**
     * ai代码评审的回调接口
     *
     * @param aiCodeDto AI代码DTO
     * @return {@link String}
     */
    @Override
    public String codeCallback(List<AiCodeCallbackDTO> aiCodeDto) {
        log.info("--代码评审-- 请求了回调地址,入参：" + JSON.toJSONString(aiCodeDto));

        // 本地存储代码而定历史评分记录
        if (CollectionUtil.isEmpty(aiCodeDto)) {
            return "新增评分记录条数：0";
        }
        List<AiCodeEvaluation> aiCodeEvaluationList = new ArrayList<>();
        log.info("length:{}", aiCodeDto.get(0).getComment().length());
        aiCodeDto.forEach(dto -> {
            AiCodeEvaluation aiCodeEvaluation = new AiCodeEvaluation();
            aiCodeEvaluation.setScrumItemId(dto.getTaskId());
            aiCodeEvaluation.setAiScore(dto.getScore());
            log.info("comment length:{}", dto.getComment().length());
            aiCodeEvaluation.setAiEvaluation(dto.getComment().length() > 2048 ? dto.getComment().substring(0, 2048) : dto.getComment());
            aiCodeEvaluation.setMergeId(dto.getMergerId());
            aiCodeEvaluationList.add(aiCodeEvaluation);
        });
        int count = aiCodeEvaluationMapper.batchInsert(aiCodeEvaluationList);

        // 封装dto调取devops发布的回调接口
        Boolean result = codeCallbackDevops(aiCodeEvaluationList);

        return "新增评分记录条数：" + count + "，回调devops代码评分接口结果：" + result;
    }

    /**
     * 代码评审回调DevOps
     *
     * @param aiCodeEvaluationList AI代码评估列表
     * @return {@link Boolean}
     */
    private Boolean codeCallbackDevops(List<AiCodeEvaluation> aiCodeEvaluationList) {

        List<Long> taskIds = aiCodeEvaluationList.stream().map(AiCodeEvaluation::getScrumItemId).collect(Collectors.toList());

        // 调用devops接口的dto封装
        List<CodeResultDetailDTO> dtoList = new ArrayList<>();
        aiCodeEvaluationList.forEach(codeEvaluation -> {
                    CodeResultDetailDTO dto = new CodeResultDetailDTO();
                    dto.setRelatedItemTaskId(codeEvaluation.getScrumItemId());
                    dto.setAiScore(codeEvaluation.getAiScore().doubleValue());
                    dto.setAiEvaluation(codeEvaluation.getAiEvaluation());
                    dtoList.add(dto);
                }
        );

        // 获取RPC请求token
        String token = openApiGatewayService.getToken();

        // 调用接口
        log.info("codeCallbackDevops.params: {}", JsonUtil.toJsonString(dtoList));
        String response = "";
        try {
            response = HttpUtil.sendPost(ucgConfig.getUcgHost() + AI_CODE_CALLBACK + token,
                    JsonUtil.toJsonString(dtoList), null);
            log.info("codeCallbackDevops.response: {}", JsonUtil.toJsonString(response));
            if (StringUtils.isNotBlank(response)) {
                JSONObject jsonObject = new JSONObject(response);
                boolean ret = jsonObject.getBoolean("success");
                if (ret) {
                    JSONArray dataArray = jsonObject.getJSONArray("data");
                    List<Long> ackTaskIds = new ArrayList<>();
                    for (int i = 0; i < dataArray.length(); i++) {
                        ackTaskIds.add(dataArray.getLong(i));
                    }
                    List<Long> unAckTaskIds = taskIds.stream().filter(id -> !ackTaskIds.contains(id)).collect(Collectors.toList());
                    updateCodeAck(ackTaskIds, unAckTaskIds);
                    return Boolean.TRUE;
                } else {
                    updateCodeAck(null, taskIds);
                    return Boolean.FALSE;
                }
            }
            updateCodeAck(null, taskIds);
            return Boolean.FALSE;
        } catch (Exception e) {
            log.error("codeCallbackDevops exception{}", e.getMessage());
            updateCodeAck(null, taskIds);
            return Boolean.FALSE;
        }
    }

    /**
     * 更新代码确认
     *
     * @param ackTaskIds   devops更新的任务ID
     * @param unAckTaskIds devops未更新的任务ID
     */
    private void updateCodeAck(List<Long> ackTaskIds, List<Long> unAckTaskIds) {

        // 更新devops回调同步的任务ack状态
        if (CollectionUtil.isNotEmpty(ackTaskIds)) {
            aiCodeEvaluationMapper.updateAckStatus(ackTaskIds, 1);
        }

        // 更新devops未回调同步的任务ack状态
        if (CollectionUtil.isNotEmpty(unAckTaskIds)) {
            aiCodeEvaluationMapper.updateAckStatus(unAckTaskIds, 2);
        }
    }

    private void recordScoreNumber(AIScorePO aiScorePO) {
        // 如果该文档打分失败超过3次则认为已评分且评分为0，防止一直打分该文档
        LambdaQueryWrapper<AIScorePO> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper1.eq(AIScorePO::getFileId, aiScorePO.getFileId());
        lambdaQueryWrapper1.eq(AIScorePO::getTaskId, aiScorePO.getTaskId());
        int scoreNum = 0;
        if (aiScorePO.getScoreNum() != null) {
            scoreNum = aiScorePO.getScoreNum();
        }
        scoreNum++;
        AIScorePO aiScorePOUpdate1 = new AIScorePO();
        aiScorePOUpdate1.setScoreNum(scoreNum);
        if (scoreNum >= 3) {
            aiScorePOUpdate1.setStatus(2);
            aiScorePOUpdate1.setScore(80.0);
            aiScorePOUpdate1.setScoreDesc("-");
            aiUtilService.callBackDevops(aiScorePO.getTaskId());
        }
        log.info("goToScore failed result{}", aiScorePOUpdate1);
        aiScoreMapper.update(aiScorePOUpdate1, lambdaQueryWrapper1);
    }

    private void recordCorrectScore(AIScorePO aiScorePO, Integer type, String data) {

        try {
            JSONArray dataJsonArray = new JSONArray(data);
            List<Boolean> dataRet = new ArrayList<>();
            StringBuilder desc = new StringBuilder();
            for (int i = 0; i < dataJsonArray.length(); i++) {
                JSONObject dataTmp = dataJsonArray.getJSONObject(i);
                if (dataTmp.get("isQualified") != null) {
                    dataRet.add(dataTmp.getBoolean("isQualified"));
                } else {
                    dataRet.add(false);
                }
                desc.append(dataTmp.getString("explain"));
                desc.append(";");
            }

            // 处理描述过长
            if (desc.length() > 1000) {
                desc.setLength(999);
            }

            // 说明是json表达式 解析data关联规则表算出来实际分值
            LambdaQueryWrapper<ScoreRulePO> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
            lambdaQueryWrapper2.eq(ScoreRulePO::getScoreType, type);
            lambdaQueryWrapper2.last("limit 1");
            ScoreRulePO scoreRulePO = scoreRuleMapper.selectOne(lambdaQueryWrapper2);

            String rule = scoreRulePO.getRule();
            String[] rules = rule.split(",");
            double scoreRet = 0.0;
            int scoreRuleSize = Math.min(rules.length, dataRet.size());

            // isAllTrueRule 判断是否所有的规则都合格，如果都合格，给满分
            boolean isAllTrueRule = true;
            for (int i = 0; i < scoreRuleSize; i++) {
                if (Boolean.TRUE.equals(dataRet.get(i))) {
                    scoreRet += Double.parseDouble(rules[i]);
                } else {
                    isAllTrueRule = false;
                }
            }
            log.info("goToScore 根据规则给的分数为{}", scoreRet);
            if (isAllTrueRule || scoreRet > 100.0) {
                scoreRet = 100.0;
            }
            log.info("goToScore 最终给的分数为{}", scoreRet);

            List<AIScorePO> aiScoreList = new ArrayList<>();
            aiScoreList.add(aiScorePO);

            // 查询任务 docHash 值
            AIScorePO docHashData = aiScoreMapper.getDocHash(aiScorePO.getTaskId(), aiScorePO.getFileId());
            if(StringUtils.isNotBlank(docHashData.getDocHash())){
                // 查询 交付物相同，未评分得记录
                aiScoreList = aiScoreMapper.getListByDocHash(docHashData.getDocHash(), aiScorePO.getDeliverableId());
            }

            for(AIScorePO aiScore : aiScoreList){
                AIScorePO aiScoreUpdate = new AIScorePO();
                aiScoreUpdate.setTaskId(aiScore.getTaskId());
                aiScoreUpdate.setFileId(aiScore.getFileId());
                aiScoreUpdate.setScore(scoreRet);
                aiScoreUpdate.setScoreDesc(desc.toString());
                aiScoreUpdate.setResultDetail("与 " + docHashData.getTaskId() + " 任务交付物相同");
                aiScoreUpdate.setStatus(2);
                aiScoreUpdate.setScoreNum(1);
                aiScoreMapper.updateAiScoreByfileIdAndTaskId(aiScoreUpdate);

                // 调用接口通知
                aiUtilService.callBackDevops(aiScore.getTaskId());
            }
        } catch (Exception e) {
            log.error("taskId:{} goToScore json parse error:{}", aiScorePO.getTaskId(), e.getMessage());
            AIScorePO aiScoreUpdate = new AIScorePO();
            aiScoreUpdate.setTaskId(aiScorePO.getTaskId());
            aiScoreUpdate.setFileId(aiScorePO.getFileId());
            aiScoreUpdate.setScore(80D);
            aiScoreUpdate.setScoreDesc("-");
            aiScoreUpdate.setStatus(2);
            aiScoreUpdate.setScoreNum(1);
            aiScoreMapper.updateAiScoreByfileIdAndTaskId(aiScoreUpdate);
            // 调用接口通知
            aiUtilService.callBackDevops(aiScorePO.getTaskId());
        }
    }

    /**
     * 记录AI请求日志
     */
    private void saveAiServiceLog(String scoreId, String taskId, String fileId, String deliverableId, String serviceUrl, String request, String response) {
        try {
            AIServiceLog aiLog = new AIServiceLog();
            aiLog.setScoreId(scoreId);
            aiLog.setTaskId(taskId);
            aiLog.setFileId(fileId);
            aiLog.setDeliverableId(deliverableId);
            aiLog.setServiceUrl(serviceUrl);
            aiLog.setRequest(request);
            aiLog.setResponse(response);
            aiServiceLogMapper.insert(aiLog);
        } catch (Exception e) {
            log.error("记录AI评分服务调用日志失败。", e);
        }
    }
}
