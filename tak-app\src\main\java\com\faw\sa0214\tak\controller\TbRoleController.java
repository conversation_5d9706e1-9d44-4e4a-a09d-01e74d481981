package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.user.UserCheck;
import com.faw.sa0214.tak.model.bo.DingDingMsgBo;
import com.faw.sa0214.tak.model.request.SearchRequest;
import com.faw.sa0214.tak.model.response.RoleHoursResponse;
import com.faw.sa0214.tak.po.TbHours;
import com.faw.sa0214.tak.po.TbHoursVo;
import com.faw.sa0214.tak.service.DingDingMsgService;
import com.faw.sa0214.tak.service.TbRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 图表数据接口
 *
 * <AUTHOR>
 * @version $ Id: bMidAttendanceController, v 0.1 2023/09/15 15:28 Lsj Exp $
 * @date 2023/09/15
 */
@Tag(name = "图表数据接口", description = "图表数据接口")
@RequestMapping("/tb")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TbRoleController {

    private final TbRoleService tbService;

    private final DingDingMsgService sendDingMessage;

    /**
     * 角色工时大图数据
     *
     * @param searchRequest 请求条件
     * @return {@code Result<List<RoleHoursResponse>> }
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "角色工时大图数据", description = "[author:10027705]")
    @PostMapping("/getRoleHoursData")
    public Result<List<RoleHoursResponse>> getRoleHoursData(@Validated @RequestBody SearchRequest searchRequest) {

        return Result.success(tbService.getRoleHoursChartData(searchRequest));
    }

    /**
     * 业务单元工时大图数据
     *
     * @param searchRequest 请求条件
     * @return {@code Result<LinkedHashMap<Integer, List<TbHours>>> }
     */
    @Operation(summary = "业务单元工时大图数据", description = "[author:10027705]")
    @PostMapping("/getBusinessHours")
    public Result<LinkedHashMap<Integer, List<TbHours>>> getBusinessHours(
            @Validated @RequestBody SearchRequest searchRequest) {
        if (StringUtils.isEmpty(searchRequest.getRole())) {
            Result.failed("角色不能为空");
        }
        LinkedHashMap<Integer, List<TbHours>> map = tbService.getBusinessUnitHoursChartData(searchRequest);
        return Result.success(map);
    }

    /**
     * 业务单元工时大图数据
     *
     * @param searchRequest 请求条件
     * @return {@code Result<List<TbHoursVo>> }
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "业务单元工时大图数据", description = "[author:10027705]")
    @PostMapping("/getBusinessHoursNew")
    public Result<List<TbHoursVo>> getBusinessHoursNew(@Validated @RequestBody SearchRequest searchRequest) {
        List<TbHoursVo> result = new ArrayList<>();
        if (StringUtils.isEmpty(searchRequest.getSubRole())) {
            return Result.failed("角色不能为空");
        }
        LinkedHashMap<Integer, List<TbHours>> map = tbService.getBusinessUnitHoursChartData(searchRequest);
        map.forEach((k, v) -> {
            TbHoursVo vo = new TbHoursVo();
            vo.setWeek(k);
            vo.setTbHours(v);
            result.add(vo);
        });
        return Result.success(result);
    }

    /**
     * 推送消息
     *
     * @param searchRequest 请求条件
     * @return {@code Result<String> }
     */
    @Operation(summary = "推送消息", description = "[author:10027705]")
    @PostMapping("/sendInterviewMsg")
    public Result<String> sendInterviewMsg(
            @Validated @RequestBody(required = false) SearchRequest searchRequest) {
        List<String> list = new ArrayList<>();
        list.add("***********");
        List<DingDingMsgBo> dingDingMsgBoList = sendDingMessage.getDingMsgBoList(list);
        sendDingMessage.pushMsg(dingDingMsgBoList);
        return Result.success("1");
    }
}
