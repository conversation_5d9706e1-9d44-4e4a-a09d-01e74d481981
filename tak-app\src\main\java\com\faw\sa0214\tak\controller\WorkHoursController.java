package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.model.request.PersonalHoursRequest;
import com.faw.sa0214.tak.model.request.TeamHoursRequest;
import com.faw.sa0214.tak.model.response.PersonalHoursResponse;
import com.faw.sa0214.tak.model.response.TeamHoursResponse;
import com.faw.sa0214.tak.service.WorkHoursService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/02
 */
@Tag(name = "用户工时统计对外接口(对外开放接口)", description = "用户工时统计(对外开放接口)")
@Slf4j
@RestController
@RequestMapping("/api/workHours")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WorkHoursController {

    private final WorkHoursService workHoursService;

    @Operation(summary = "查询战队下项目工时", description = "查询战队下项目工时[author:********]")
    @PostMapping("/team")
    public Result<List<TeamHoursResponse>> teamWorkHours(@Validated @RequestBody TeamHoursRequest request) {

        return Result.success(workHoursService.teamWorkHours(request));
    }

    @Operation(summary = "查询个人的项目工时", description = "查询个人的项目工时[author:********]")
    @PostMapping("/account")
    public Result<List<PersonalHoursResponse>> accountWorkHours(@Validated @RequestBody PersonalHoursRequest request) {

        return Result.success(workHoursService.accountWorkHours(request));
    }
}
