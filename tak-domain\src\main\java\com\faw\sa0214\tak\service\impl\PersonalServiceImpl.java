package com.faw.sa0214.tak.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.EnvTransFeignClient;
import com.faw.sa0214.tak.client.ItPmFeignClient;
import com.faw.sa0214.tak.client.UserCenterFeignClient;
import com.faw.sa0214.tak.client.dto.bpm.StartProcessDTO;
import com.faw.sa0214.tak.client.dto.itPm.ItPmResponseDTO;
import com.faw.sa0214.tak.client.dto.itPm.ItPmSearchDTO;
import com.faw.sa0214.tak.client.dto.user.CloudNativeUserDTO;
import com.faw.sa0214.tak.client.dto.user.UserByRoleQueryDTO;
import com.faw.sa0214.tak.client.dto.user.UserResponseDTO;
import com.faw.sa0214.tak.common.constant.BizException;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.common.constant.enums.LockTypeEnum;
import com.faw.sa0214.tak.common.constant.enums.itpm.ItPmRoleTransferEnum;
import com.faw.sa0214.tak.common.constant.enums.itpm.ItPmTeamTransferEnum;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.JsonUtil;
import com.faw.sa0214.tak.common.util.RedisService;
import com.faw.sa0214.tak.common.util.UserThreadLocalUtil;
import com.faw.sa0214.tak.mapper.OnlinePlanInfoMapper;
import com.faw.sa0214.tak.mapper.PersonalMapper;
import com.faw.sa0214.tak.mapper.SysTeamUserMapper;
import com.faw.sa0214.tak.mapper.UserChanageHistoryMapper;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.dto.baseCenter.*;
import com.faw.sa0214.tak.model.request.*;
import com.faw.sa0214.tak.model.response.ProductManagerResponse;
import com.faw.sa0214.tak.model.response.TeamUserResponse;
import com.faw.sa0214.tak.po.*;
import com.faw.sa0214.tak.service.*;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 人员管理
 *
 * <AUTHOR>
 * @date 2024/1/23
 */
@Service
@Slf4j
public class PersonalServiceImpl implements PersonalService {

    @Autowired
    private ProcessService processService;
    @Autowired
    private RedisService redisService;
    @Autowired
    TeamRoleService teamRoleService;
    @Autowired
    TeamInfoService teamInfoService;
    @Autowired
    private PersonalMapper personalMapper;
    @Autowired
    private SysTeamUserMapper sysTeamUserMapper;
    @Autowired
    private UserChanageHistoryMapper userChanageHistoryMapper;
    @Autowired
    private OnlinePlanInfoMapper onlinePlanInfoMapper;
    @Autowired
    private UserCenterFeignClient userCenterFeignClient;
    @Autowired
    private EnvTransFeignClient envTransFeignClient;
    @Autowired
    private ItPmFeignClient itPmFeignClient;
    @Autowired
    private AiTaskScoreEvaluationService aiTaskScoreEvaluationService;

    public final static String SPECIAL_ROLE_1 = "13";
    public final static String SPECIAL_ROLE_2 = "15";
    private final static String ORG_CODE = "10039059";
    private final static String ORG_TYPE = "R02";

    /**
     * 查找自有人员信息
     *
     * @param searchRequest 搜索请求
     * @return {@link PageResult}<{@link SysTeamUserDTO}>
     */
    @Override
    public PageResult<SysTeamUserDTO> findIbmPersonalInfo(SearchRequest searchRequest) {
        //获取用户中心数据，feign调用
        UserCenterSearchRequest request = new UserCenterSearchRequest();
        request.setCurrentPage(searchRequest.getPageNum());
        request.setPageSize(searchRequest.getPageSize());
        request.setUserName(searchRequest.getUserName());
        request.setOrgFlag("0");
        PageResult<SysTeamUserDTO> idmUserList = getUserCenterList(request);
        if (idmUserList == null) {
            return null;
        }
        List<SysTeamUserDTO> data = idmUserList.getData();
        if (CollectionUtil.isNotEmpty(data)) {
            data.forEach(user -> user.setUserType(0));
            idmUserList.setData(data);
        }
        return idmUserList;
    }

    /**
     * 查找 IT PM 人员信息
     *
     * @param request 搜索请求
     * @return {@link PageResult}<{@link SysTeamUserDTO}>
     */
    @Override
    public PageResult<SysTeamUserDTO> findItPmPersonalInfo(SearchRequest request) {

        Integer pageNum = request.getPageNum();
        Integer pageSize = request.getPageSize();
        //模糊搜索人员
        String userName = request.getUserName();

        // 请求参数构建
        ItPmPersonSearchDTO dto = new ItPmPersonSearchDTO();
        dto.setPageNo(pageNum);
        dto.setPageSize(pageSize);
        dto.setTaskStatus(Arrays.asList(3, 4, 5, 6, 7, 8));
        if (StringUtils.isNotBlank(userName)) {
            dto.setEmpName(userName);
        }

        // 请求 IT PM 接口数据
        PageResult<SysTeamUserDTO> itPmUserList = envTransFeignClient.getItPmUserList(dto).getData();
        if (itPmUserList == null) {
            return null;
        }
        itPmUserList.setPageNum(pageNum);
        itPmUserList.setPageSize(pageSize);

        return itPmUserList;
    }

    @Override
    public List<String> findRoleByTeam(Integer teamId, String team) {
        SysTeamUser user = new SysTeamUser();
        user.setTeamId(teamId);
        user.setTeam(team);
        List<SysTeamUser> userList = personalMapper.selectByCondition(user);
        return userList.stream().map(SysTeamUser::getRoleName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    /**
     * 新增用户
     *
     * @param saveRequestList 保存请求列表
     * @return {@link Result}<{@link String}>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> insertSysTeamUser(List<UserSaveRequest> saveRequestList) {

        String randomId = UUID.randomUUID().toString();

        boolean lockSuccess = redisService.lock(LockTypeEnum.INSERT_PERSON_LOCK.getPrefix() + randomId,
                LockTypeEnum.INSERT_PERSON_LOCK.getExpireTime());

        if (!lockSuccess) {
            log.info("insertSysTeamUser.lock.failed");
            return Result.failed("新增人员操作中，请耐心等待！");
        }

        try {
            // 校验并提示不可重复添加
            Map<String, String> requestUserMap = saveRequestList.stream().collect(Collectors.toMap(UserSaveRequest::getLoginAccount, UserSaveRequest::getUserName));
            List<String> loginAccountList = new ArrayList<>(requestUserMap.keySet());

            List<SysTeamUser> userList = personalMapper.getUserList(loginAccountList);
            // 自有人员离开部门可以重复添加
            List<SysTeamUser> filterUserListTemp = userList.stream().filter(user -> !(user.getUserType() == 0 && user.getIsDelete() == 1)).collect(Collectors.toList());
            // 三方人员已出项可以重复添加
            List<SysTeamUser> filterUserList = filterUserListTemp.stream().filter(user -> !(user.getUserType() == 1 && user.getIsDelete() == 1)).collect(Collectors.toList());
            List<String> userAccountList = filterUserList.stream().map(SysTeamUser::getLoginAccount).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(userAccountList)) {
                List<String> duplicateAccountList = loginAccountList.stream().filter(userAccountList::contains).collect(Collectors.toList());
                List<String> duplicateAccountNameList = duplicateAccountList.stream().map(requestUserMap::get).collect(Collectors.toList());
                String nameList = String.join("、", duplicateAccountNameList);
                return Result.failed(nameList + "-不可重复添加");
            }

            // 补充数据
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            String createDate = currentDate.format(formatter);
            LocalDateTime currentDateTime = LocalDateTime.now();
            DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
            String userCreateTime = currentDateTime.format(formatter1);

            List<SysTeamUser> sysTeamUserList = new ArrayList<>();
            saveRequestList.forEach(request -> {
                if (request.getUserType() == 1) {
                    return;
                }
                SysTeamUser user = new SysTeamUser();
                BeanUtils.copyProperties(request, user);
                user.setCreateTime(createDate);
                user.setUserCreateTime(userCreateTime);
                user.setRecordableDays(7);
                user.setIsDelete(0);
                user.setPosition(request.getPost());
                user.setCompany(request.getDepartment());
                user.setRoleName(request.getApplicationRole());
                user.setRoleId(request.getApplicationRoleId());
                if (request.getEntryTime() != null) {
                    user.setEntryTime(DateUtils.formatDate(request.getEntryTime(), DateUtils.FORMAT_DATE_TIME));
                }
                if (request.getOutTime() != null) {
                    if (request.getOutTime().before(new Date())) {
                        user.setIsDelete(1);
                    }
                    user.setOutTime(DateUtils.formatDate(request.getOutTime(), DateUtils.FORMAT_DATE_TIME));
                }
                user.setLevel(request.getRank());
                sysTeamUserList.add(user);
            });

            // 持久化数据
            if (CollectionUtil.isNotEmpty(sysTeamUserList)) {
                personalMapper.insertBatch(sysTeamUserList);
            }

            return Result.success("保存成功");

        } finally {
            redisService.unlock(LockTypeEnum.INSERT_PERSON_LOCK.getPrefix() + randomId);
        }

    }

    /**
     * 条件查询人员列表
     *
     * @param request 请求
     * @return {@link PageResult}<{@link SysTeamUserDTO}>
     */
    @Override
    public PageResult<SysTeamUserDTO> findPersonalInfo(UserSearchRequest request) {

        // 基础数据查询
        UserSearchDTO dto = new UserSearchDTO();
        BeanUtils.copyProperties(request, dto);
        List<SysTeamUser> sysTeamUserList = personalMapper.selectBySearchCondition(dto);

        // 过滤人员状态
        List<Integer> stateList = request.getState();
        if (CollectionUtil.isNotEmpty(stateList)) {
            sysTeamUserList = sysTeamUserList.stream()
                    .filter(user -> {
                        Integer userType = user.getUserType();
                        Integer isDelete = user.getIsDelete();
                        return stateList.contains(getState(userType, isDelete));
                    })
                    .toList();
        }

        // 更新用户部门、职级、岗位数据（查询平台用户中心数据）
        List<SysTeamUserDTO> userDTOList = updateUserInfo(sysTeamUserList);

        // 补充返回值状态
        userDTOList.forEach(user -> {
            Integer userType = user.getUserType();
            user.setState(String.valueOf(userType == 0 ? user.getIsDelete() : user.getIsDelete() + 2));
        });

        // 过滤部门、职级、岗位数据
        List<String> rankDesc = request.getRank();
        List<String> positionDesc = request.getPost();
        String department = request.getDepartment();
        List<SysTeamUserDTO> filterUserInfo = filterUserInfo(userDTOList, rankDesc, positionDesc, department);

        // 分页及数据封装
        return generatePageResult(filterUserInfo, request.getPageNum(), request.getPageSize());
    }

    /**
     * 过滤用户部门、职级、岗位信息
     *
     * @param userDTOList 用户列表
     * @param rankDesc 职级
     * @param positionDesc 职位
     * @param department 部门
     * @return {@link List }<{@link SysTeamUserDTO }>
     */
    private List<SysTeamUserDTO> filterUserInfo(List<SysTeamUserDTO> userDTOList, List<String> rankDesc, List<String> positionDesc, String department) {
        HashSet<String> ranks = new HashSet<>(rankDesc == null ? Collections.emptyList() : rankDesc);
        HashSet<String> positions = new HashSet<>(positionDesc == null ? Collections.emptyList() : positionDesc);

        boolean rankDescIsEmpty = ranks.isEmpty();
        boolean positionDescIsEmpty = positions.isEmpty();
        boolean departmentIsEmpty = StringUtils.isEmpty(department);

        return userDTOList.stream()
                .filter(dto -> (positionDescIsEmpty || positions.contains(dto.getPost())) &&
                        (rankDescIsEmpty || ranks.contains(dto.getRank())) &&
                        (departmentIsEmpty || department.equals(dto.getDepartment())))
                .collect(Collectors.toList());
    }

    /**
     * 根据平台实时数据
     * 更新用户信息
     *
     * @param sysTeamUserList 用户列表
     * @return {@link List }<{@link SysTeamUserDTO }>
     */
    private List<SysTeamUserDTO> updateUserInfo(List<SysTeamUser> sysTeamUserList) {
        List<String> accounts = sysTeamUserList.stream().map(SysTeamUser::getLoginAccount).collect(Collectors.toList());
        //获取用户中心数据
        UserCenterSearchRequest request = new UserCenterSearchRequest();
        request.setCurrentPage(1);
        request.setPageSize(accounts.size());
        request.setLoginNames(accounts);
        request.setOrgFlag("0");
        //获取用户中心数据，feign调用
        PageResult<SysTeamUserDTO> userCenterList = getUserCenterList(request);
        List<SysTeamUserDTO> userCenterListData = userCenterList.getData();
        Map<String, SysTeamUserDTO> userCenterListDataDTO = userCenterListData.stream()
                .collect(Collectors.toMap(SysTeamUserDTO::getLoginAccount, item -> item, (existing, replacement) -> existing
                ));
        List<SysTeamUserDTO> userList = new ArrayList<>();

        sysTeamUserList.forEach(item -> {
            SysTeamUserDTO dto = transferUserToDto(item);
            SysTeamUserDTO sysTeamUserDTO = userCenterListDataDTO.get(dto.getLoginAccount());
            if (sysTeamUserDTO != null) {
                dto.setPost(sysTeamUserDTO.getApplicationRole());
                dto.setRank(sysTeamUserDTO.getRank());
                dto.setDepartment(sysTeamUserDTO.getDepartment());
            }
            userList.add(dto);
        });

        return userList;
    }

    /**
     * 状态转换
     * 数据库状态 →→→ 显示状态
     *
     * @param userType 用户类型
     * @param isDelete 是否删除
     * @return {@link Integer }
     */
    private Integer getState(Integer userType, Integer isDelete) {
        if (userType == 0) {
            return isDelete == 0 ? 0 : 1;
        } else if (userType == 1) {
            return isDelete == 0 ? 2 : 3;
        }
        return null;
    }

    /**
     * 封装分页结果
     *
     * @param userDTOList 用户列表
     * @param pageNum     第几页
     * @param pageSize    页面大小
     * @return {@link PageResult}<{@link SysTeamUserDTO}>
     */
    private PageResult<SysTeamUserDTO> generatePageResult(List<SysTeamUserDTO> userDTOList, Integer pageNum, Integer pageSize) {

        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, userDTOList.size());
        List<SysTeamUserDTO> userList = Collections.emptyList();
        if (startIndex < userDTOList.size()) {
            userList = userDTOList.subList(startIndex, endIndex);
        }

        return PageResult.<SysTeamUserDTO>builder()
                .total((long) userDTOList.size())
                .data(userList)
                .pageNum(pageNum)
                .pageSize(pageSize).build();
    }

    /**
     * 将用户 PO 转换成 DTO
     *
     * @param item 项目
     * @return {@link SysTeamUserDTO}
     */
    private SysTeamUserDTO transferUserToDto(SysTeamUser item) {
        SysTeamUserDTO dto = new SysTeamUserDTO();
        BeanUtils.copyProperties(item, dto);
        dto.setId(String.valueOf(item.getId()));
        dto.setApplicationRole(item.getRoleName());
        dto.setDepartment(item.getCompany());
        dto.setPost(item.getPosition());
        dto.setRank(item.getLevel());
        dto.setTeamId(String.valueOf(item.getTeamId()));
        dto.setApplicationRoleId(String.valueOf(item.getRoleId()));
        return dto;
    }

    @Override
    public Result<String> deletePersonal(SysTeamUserRequest sysTeamUserRequest) throws Throwable {

        //查询该用户有没有提交过删除
        String recordId = personalMapper.findHistoryIdByUserId(sysTeamUserRequest.getUserId());
        if (StringUtils.isNotEmpty(recordId)) {
            log.info("[deletePersonal]该用户查出修改记录:{}", recordId);
            return Result.failed("已经提交过删除请求,请勿重复操作");
        }
        log.info("[deletePersonal]参数：[{}]", sysTeamUserRequest);
        log.info("[deletePersonal]操作人：[{}]", sysTeamUserRequest.getCurrentLoginAccount());
        //插入修改记录
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        sysTeamUserRequest.setCreateTime(currentDate.format(formatter));
        String loginId = "";
        loginId = UserThreadLocalUtil.getCurrentUserCode();
        //审批 配置参数
        log.info("[deletePersonal]开始进入审批,发送审批");
        String creatorCode = sysTeamUserRequest.getCurrentLoginAccount();
        String approveUserCode = personalMapper.findTeamManagerByTeam(sysTeamUserRequest.getTeamId());
        String approveUserName = personalMapper.findTeamManagerNameByTeam(sysTeamUserRequest.getTeamId());
        log.info("[deletePersonal]查询站队长[]" + approveUserCode);

        log.info("[deletePersonal]开始插入修改记录(删除),操作人[]" + loginId);
        personalMapper.insertChangeHistory(sysTeamUserRequest, loginId, approveUserCode, approveUserName);
        log.info("[deletePersonal]结束插入修改记录(删除)");

        String taskInstanceCode = personalMapper.findHistoryIdByUserId(sysTeamUserRequest.getUserId());
        log.info("[deletePersonal]查询当前修改记录id[]" + taskInstanceCode);

        //配置模板
        Map<String, String> map = new HashMap<>();
        map.put("titleName", sysTeamUserRequest.getUserName() + "-离职申请");
        map.put("userName", sysTeamUserRequest.getUserName());
        map.put("userId", sysTeamUserRequest.getUserId());
        map.put("code", taskInstanceCode);
        StartProcessDTO.BizModel bizModel = new StartProcessDTO.BizModel();
        bizModel.setApplyHeader(map);

        //配置审批节点（单节点）
        Map<String, String> assignees = new HashMap<>(8);
        assignees.put("审批", approveUserCode);

        String processInstanceId = processService.startSingleNodeWorkflow(creatorCode, assignees,
                bizModel, taskInstanceCode);

        return Result.success(processInstanceId, "删除已提交审核,请耐心等待");
    }

    @Override
    public SearchOptionsInfo findSearchOptionsInfo() {
        SearchOptionsInfo searchOptionsInfo = new SearchOptionsInfo();
        String redisInfo = redisService.get(CommonConstant.USER_CENTER_INFO_KEY);
        log.info("redisInfo：{}", redisInfo);
        if (StringUtils.isNotEmpty(redisInfo)) {
            searchOptionsInfo = JSONObject.parseObject(redisInfo, SearchOptionsInfo.class);
            log.info("searchOptionsInfo：{}", JSONObject.toJSONString(searchOptionsInfo));
            return searchOptionsInfo;
        }
        List<String> sysTeamUserList = personalMapper.selectUserDistinct();

        //获取用户中心数据
        List<SysTeamUserDTO> userCenterListData = new ArrayList<>();

        // 分割 sysTeamUserList 为多个子列表
        for (int i = 0; i < sysTeamUserList.size(); i += CommonConstant.INTEGER_1000) {
            int end = Math.min(i + CommonConstant.INTEGER_1000, sysTeamUserList.size());
            List<String> subList = sysTeamUserList.subList(i, end);

            // 创建请求对象
            UserCenterSearchRequest request = new UserCenterSearchRequest();
            request.setCurrentPage(1);
            request.setPageSize(subList.size());
            request.setLoginNames(subList);
            request.setOrgFlag("0");

            //获取用户中心数据，feign调用
            PageResult<SysTeamUserDTO> userCenterList = getUserCenterList(request);

            // 检查返回结果并合并数据
            if (userCenterList != null && userCenterList.getData() != null) {
                userCenterListData.addAll(userCenterList.getData());
            }
        }

        //获取部门信息
        List<String> departmentList = userCenterListData.stream().map(SysTeamUserDTO::getDepartment).collect(Collectors.toList());
        List<String> companyList = personalMapper.findCompanyList();
        List<String> orgList = new ArrayList<>();
        orgList.addAll(departmentList);
        orgList.addAll(companyList);
        orgList = orgList.stream().distinct().collect(Collectors.toList());
        orgList = orgList.stream().filter(a -> a != null && !a.isEmpty()).collect(Collectors.toList());

        //获取岗位信息
        List<String> postList = userCenterListData.stream().map(SysTeamUserDTO::getPost).collect(Collectors.toList());
        //默认将岗位"员工"加入到列表里
        postList.add("员工");
        postList = postList.stream().distinct().collect(Collectors.toList());
        postList = postList.stream().filter(a -> a != null && !a.isEmpty()).collect(Collectors.toList());
        //获取职级信息
        List<String> rankList = userCenterListData.stream().map(SysTeamUserDTO::getRank).collect(Collectors.toList());
        rankList = rankList.stream().distinct().collect(Collectors.toList());
        rankList = rankList.stream().filter(a -> a != null && !a.isEmpty()).collect(Collectors.toList());
        //获取战队信息
        List<String> teamList = personalMapper.findTeamList();
        //获取角色信息
        List<String> roleList = personalMapper.findRoleList();

        searchOptionsInfo.setDepartmentList(orgList);
        searchOptionsInfo.setPostList(postList);
        searchOptionsInfo.setRankList(rankList);
        searchOptionsInfo.setTeamList(teamList);
        searchOptionsInfo.setRoleList(roleList);

        // 放入缓存，过期时间12小时
        redisService.set(CommonConstant.USER_CENTER_INFO_KEY, JSONObject.toJSONString(searchOptionsInfo), 43200L);

        return searchOptionsInfo;
    }

    @Override
    public List<String> findChildRoleNameByRole(String role) {
        // 假设childRoleName是包含逗号分隔的字符串
        String childRoleName = personalMapper.findChildRoleNameByRole(role);

        // 根据逗号分隔字符串 转换为List<String>

        return Optional.ofNullable(childRoleName).map(s -> s.split("、")).map(Arrays::asList).orElse(Collections.emptyList());
    }

    @Override
    public List<String> findHomeDevTeamByTeam(String team) {
        // 假设childRoleName是包含逗号分隔的字符串
        String homeDevTeam = personalMapper.findHomeDevTeamByTeam(team);

        // 根据逗号分隔字符串
        String[] teamNameArray = homeDevTeam.split("、");

        // 转换为List<String>

        return Arrays.asList(teamNameArray);
    }

    @Override
    public List<UserChangeHistroy> findHistoryInfoByUserId(String userId) {
        List<UserChangeHistroy> userChangeHistoryList = userChanageHistoryMapper.getUserChangeHistoryUserId(userId);
        for (UserChangeHistroy userChangeHistroy : userChangeHistoryList) {
            SysTeamUserDTO personal = personalMapper.selectUserInfoByUserId(userId);
            userChangeHistroy.setUserName(personal.getUserName());
        }
        return userChangeHistoryList;
    }

    @Override
    public SysTeamUserDTO findPersonalByLoginAccount(SysTeamUserRequest sysTeamUserRequest) {

        SysTeamUserDTO personal = personalMapper.findPersonalByLoginAccount(sysTeamUserRequest);
        if (personal == null) {
            return new SysTeamUserDTO();
        }

        SysTeamUserDTO personalRpc = new SysTeamUserDTO();
        if (StringUtils.isNotEmpty(sysTeamUserRequest.getLoginAccount())) {
            //获取用户中心数据
            List<String> accounts = new ArrayList<>();
            accounts.add(sysTeamUserRequest.getLoginAccount());
            UserCenterSearchRequest request = new UserCenterSearchRequest();
            request.setCurrentPage(1);
            request.setPageSize(accounts.size());
            request.setLoginNames(accounts);
            request.setOrgFlag("0");
            //获取用户中心数据，feign调用
            PageResult<SysTeamUserDTO> userCenterList = getUserCenterList(request);
            List<SysTeamUserDTO> userCenterListData = userCenterList.getData();
            Map<String, SysTeamUserDTO> userCenterListDataDTO = userCenterListData.stream()
                    .collect(Collectors.toMap(SysTeamUserDTO::getLoginAccount, item -> item));
            personalRpc = userCenterListDataDTO.get(sysTeamUserRequest.getLoginAccount());
        }
        // 1.自有人员，更新RPC的职级，部门信息
        // 2.三方人员，RPC接口查不到，延用数据库信息
        if (personalRpc != null) {
            personal.setPost(personalRpc.getApplicationRole());
            personal.setRank(personalRpc.getRank());
            personal.setDepartment(personalRpc.getDepartment());
        }

        try {
            List<UserChangeHistroy> userChangeHistoryList = userChanageHistoryMapper.getUserChangeHistoryLoginAccount(sysTeamUserRequest.getLoginAccount());
            if (CollectionUtil.isNotEmpty(userChangeHistoryList) && userChangeHistoryList.get(0) != null && userChangeHistoryList.get(0).getProcessInstanceId() != null) {
                personal.setBusinessId(String.valueOf(userChangeHistoryList.get(0).getId()));
                personal.setBusinessState((userChangeHistoryList.get(0).getIsCompleted() != null && userChangeHistoryList.get(0).getIsCompleted().equals("true")) ? "已审批" : "待审核");
            }
            //查询角色战队是不是被删除
            handleRoleAndTeam(personal);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return personal;
    }

    /**
     * 查询角色战队是不是被删除,删除的置为空
     *
     * @param dto DTO
     */
    private void handleRoleAndTeam(SysTeamUserDTO dto) {
        String roleId = dto.getApplicationRoleId();
        if (StringUtils.isNotBlank(roleId)) {
            TeamRole teamRole = teamRoleService.getById(roleId);
            if (teamRole != null && teamRole.getIsDelete() != null && teamRole.getIsDelete().equals(1)) {
                dto.setSubdividedRole("");
                dto.setApplicationRoleId("");
            }
        }
        String teamId = dto.getTeamId();
        if (StringUtils.isNotBlank(teamId)) {
            TeamInfo teamInfo = teamInfoService.getById(teamId);
            if (teamInfo != null && teamInfo.getIsDelete() != null && teamInfo.getIsDelete().equals(1)) {
                dto.setTeamId("");
                dto.setHomeDevTeam("");
            }
        }
    }

    @Override
    public void updateTeamRole(SysTeamUserRequest sysTeamUserRequest) {
        //审批
        personalMapper.updateTeamRole(sysTeamUserRequest);
    }

    @Override
    public List<SysTeamUserDTO> getPersonalByLoginAccount(String loginAccount) {

        List<SysTeamUserDTO> result = new ArrayList<>();

        if (StringUtils.isBlank(loginAccount)) {
            // 查询所有人员信息
            List<String> teamIdList = personalMapper.findTeamIdList();
            for (String id : teamIdList) {
                result.addAll(personalMapper.findPersonalByTeamId(id));
            }
        } else {
            // 根据登陆人账号查询所在战队信息
            SysTeamUserDTO teamInfo = personalMapper.findTeamIdByLoginAccount(loginAccount);

            // 根据teamId查询人员信息
            if (StringUtils.isNotEmpty(teamInfo.getTeamId())) {
                result = personalMapper.findPersonalByTeamId(teamInfo.getTeamId());
            }
        }

        return result;
    }

    /**
     * 更新 IT PM 用户信息
     *
     * @return {@link Integer}
     */
    @Override
    public Integer updateItPmUserInfo() {
        // 查询旗效人员表中 IT PM 人员未出项人员
        List<SysTeamUser> sysTeamUserList = personalMapper.selectEntryUsers();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        LocalDateTime now = LocalDateTime.now();
        List<SysTeamUser> userList = sysTeamUserList.stream()
                .filter(item -> (item.getIsDelete() == null || item.getIsDelete() == 0))
                .filter(item -> {
                    String outTimeStr = item.getOutTime();
                    if (outTimeStr == null) {
                        return false;
                    }
                    LocalDateTime outTime = LocalDateTime.parse(outTimeStr, formatter);
                    return outTime.isBefore(now);
                })
                .toList();

        if (CollectionUtil.isEmpty(userList)) {
            return 0;
        } else {
            return personalMapper.updateItPmStatus(userList);
        }
    }

    /**
     * 获取 IT PM 用户列表
     *
     * @param searchRequest 查询入参
     * @return {@link List}<{@link SysTeamUserDTO}>
     */
    @Override
    public PageResult<SysTeamUserDTO> getItPmUserList(ItPmPersonSearchDTO searchRequest) {

        ItPmSearchDTO dto = new ItPmSearchDTO();
        BeanUtils.copyProperties(searchRequest, dto);

        // RPC 请求
        log.info("search it pm user list, PRC request:{}", JsonUtil.toJsonString(dto));
        Result<ItPmResponseDTO> result = itPmFeignClient.queryTaskTalentExtendPage(dto);
        log.info("search it pm user list, PRC response:{}", JsonUtil.toJsonString(result));
        if (result.getCode() != 0) {
            throw new BizException("查询 IT PM 人员信息失败，调用 IT PM 接口失败！");
        }
        ItPmResponseDTO response = result.getData();
        long total = response.getTotal();
        List<ItPmPersonDTO> itPmUserList = response.getDataList();

        // 类转换
        List<SysTeamUserDTO> userDTOList = transferItPmUserList(itPmUserList);
        return PageResult.<SysTeamUserDTO>builder()
                .data(userDTOList)
                .total(total)
                .build();
    }

    private List<SysTeamUserDTO> transferItPmUserList(List<ItPmPersonDTO> itPmUserList) {
        List<TeamInfo> teamInfoList = teamInfoService.getAllTeamInfo();
        Map<String, Integer> teamMap = teamInfoList.stream().collect(Collectors.toMap(TeamInfo::getTeamName, TeamInfo::getTeamId));
        List<TeamRole> teamRoleList = teamRoleService.getAllRoleInfo();
        Map<String, Integer> roleMap = teamRoleList.stream().collect(Collectors.toMap(TeamRole::getRoleName, TeamRole::getRoleId));

        List<SysTeamUserDTO> userList = new ArrayList<>();
        itPmUserList.forEach(user -> {
            SysTeamUserDTO dto = new SysTeamUserDTO();
            dto.setPost("员工");
            Set<String> teamSet = teamMap.keySet();
            String team;
            if (teamSet.contains(user.getBelongTeam())) {
                team = user.getBelongTeam();
            } else {
                team = ItPmTeamTransferEnum.getTeamFromItPmDesc(user.getBelongTeam());
            }
            dto.setTeam(team);
            dto.setTeamId(String.valueOf(teamMap.get(team)));
            String role = ItPmRoleTransferEnum.getRoleFromItPmDesc(user.getPostName());
            dto.setApplicationRole(role);
            dto.setApplicationRoleId(String.valueOf(roleMap.get(role)));
            dto.setUserId(user.getThirdEmpId());
            dto.setUserName(user.getEmpName());
            dto.setLoginAccount(user.getEmpWebNo());
            dto.setRank(user.getRankName());
            dto.setDepartment(user.getSupplierName());
            dto.setUserType(1);
            dto.setEntryTime(user.getEntryDate());
            dto.setOutTime(user.getEntryoutDate());
            userList.add(dto);
        });
        return userList;
    }

    /**
     * 获取用户中心数据
     *
     * @param request 搜索请求
     * @return {@link PageResult}<{@link SysTeamUserDTO}>
     */
    @Override
    public PageResult<SysTeamUserDTO> getUserCenterList(UserCenterSearchRequest request) {
        List<UserCenterInfo> userCenterList = new ArrayList<>();
        String failReason = "";
        int total = 0;
        int pageSize = 0;
        int pageNum = 0;
        try {

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("currentPage", request.getCurrentPage());
            jsonObject.put("pageSize", request.getPageSize());
            jsonObject.put("orgCode", ORG_CODE);
            jsonObject.put("type", ORG_TYPE);
            jsonObject.put("loginNames", request.getLoginNames());
            jsonObject.put("orgFlag", request.getOrgFlag());
            if (StringUtils.isNotEmpty(request.getUserName())) {
                //模糊搜索人员
                if (isChinese(request.getUserName())) {
                    //是中文，姓名模糊搜索
                    jsonObject.put("name", request.getUserName());
                } else {
                    //不是中文，登录账户模糊搜索
                    jsonObject.put("loginName", request.getUserName());
                }
            }
            log.info("获取用户中心数据请求参数：{}", jsonObject.toJSONString());
            BCResult<UserCenterInfo> result = userCenterFeignClient.queryUserCenterList(jsonObject);
            log.info("获取用户中心数据请求结果：{}", JSONObject.toJSONString(result));
            if ("200".equals(result.getCode())) {
                userCenterList = result.getData().getRecords();
                total = result.getData().getTotal();
                pageSize = result.getData().getSize();
                pageNum = result.getData().getCurrent();
            } else {
                failReason = result.getMessage();
                log.info("获取用户中心数据failReason，{}", failReason);
            }
        } catch (Exception e) {
            log.warn("获取用户中心数据错误，{}", e.getMessage());
        }

        // 类转换
        List<SysTeamUserDTO> userDTOList = transferItPmUserList11(userCenterList);
        return PageResult.<SysTeamUserDTO>builder()
                .data(userDTOList)
                .total((long) total)
                .pageSize(pageSize)
                .pageNum(pageNum)
                .build();
    }

    /**
     * 判断是否中文
     *
     * @param str
     * @return
     */
    public static boolean isChinese(String str) {
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c < '\u4e00' || c > '\u9fa5') {
                return false;
            }
        }
        return true;
    }

    private List<SysTeamUserDTO> transferItPmUserList11(List<UserCenterInfo> userCenterList) {
        // 查询职级信息
        Map<String, String> positionMap = getPositionInfo();

        List<TeamRole> teamRoleList = teamRoleService.getAllRoleInfo();
        Map<String, Integer> roleMap = teamRoleList.stream().collect(Collectors.toMap(TeamRole::getRoleName, TeamRole::getRoleId));

        List<SysTeamUserDTO> userList = new ArrayList<>();
        for (UserCenterInfo dto : userCenterList) {
            SysTeamUserDTO sysTeamUserDTO = new SysTeamUserDTO();
            sysTeamUserDTO.setUserId(dto.getCode());
            sysTeamUserDTO.setUserName(dto.getUserName());
            sysTeamUserDTO.setLoginAccount(dto.getLoginName());
            if (CollectionUtil.isNotEmpty(dto.getPositionList())) {
                List<UserPosition> positionList = dto.getPositionList().stream().filter(s -> ORG_TYPE.equals(s.getOrgTypeCode())).toList();
                if (CollectionUtil.isNotEmpty(positionList)) {
                    sysTeamUserDTO.setApplicationRole(positionList.get(0).getPositionName());
                    sysTeamUserDTO.setApplicationRoleId(String.valueOf(roleMap.getOrDefault(sysTeamUserDTO.getApplicationRole(), null)));
                    sysTeamUserDTO.setPost(positionList.get(0).getPositionName());
                    sysTeamUserDTO.setDepartment(positionList.get(0).getOrgName());
                    sysTeamUserDTO.setRank(positionMap.get(positionList.get(0).getPositionLevel()));
                }
            }

            userList.add(sysTeamUserDTO);
        }
        return userList;
    }

    /**
     * 查询职级信息
     */
    public Map<String, String> getPositionInfo(){
        // 从缓存中取数
        String key = CommonConstant.BASE_CENTER_POSITION_KEY;
        String response = redisService.get(key);
        if (StringUtils.isNotBlank(response)) {
            return new Gson().fromJson(response, new TypeToken<Map<String, String>>() {
            }.getType());
        }

        // 请求接口查询职级信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("currentPage", 1);
        jsonObject.put("pageSize", 100);
        jsonObject.put("type", "positionLevel");
        log.info("获取用户中心职级信息 - 请求参数：{}", jsonObject.toJSONString());
        BCResult<PositionInfo> result = userCenterFeignClient.getPositionInfos(jsonObject);
        log.info("获取用户中心职级信息 - 请求结果：{}", JSONObject.toJSONString(result));
        List<PositionInfo> positionInfoList = new ArrayList<>();
        if ("200".equals(result.getCode())) {
            positionInfoList = result.getData().getRecords();
        } else {
            String failReason = result.getMessage();
            log.info("获取用户中心数据failReason，{}", failReason);
        }

        // 处理返回数据
        Map<String, String> positionMap = positionInfoList.stream()
                .collect(Collectors.toMap(
                        PositionInfo::getCode,
                        positionInfo -> {
                            String name = positionInfo.getName();
                            // 如果 name 包含 "|"，则截取后面的部分；否则使用原值
                            return name.contains("|")
                                    ? name.substring(name.indexOf("|") + 1)
                                    : name;
                        },
                        (existingValue, newValue) -> newValue
                ));

        redisService.set(key, JSON.toJSONString(positionMap), 24 * 60 * 60L);
        return positionMap;
    }

    @Override
    public List<SysTeamUserDTO> getDetailByLoginAccount(UserDetailRequest request) {
        List<String> loginAccountList = request.getLoginAccountList();
        if (CollectionUtil.isEmpty(loginAccountList)) {
            return Collections.emptyList();
        }
        return personalMapper.getPersonDetail(loginAccountList, request.getQueryRole());
    }

    @Override
    public Integer checkTaskGeneratePermission(String loginAccount) {
        // 域账号为空，返回可录入
        if (StringUtils.isBlank(loginAccount)) {
            return CommonConstant.INTEGER_1;
        }

        SysTeamUserRequest request = new SysTeamUserRequest();
        request.setLoginAccount(loginAccount);
        SysTeamUserDTO personal = personalMapper.findPersonalByLoginAccount(request);

        // 如果人员未查到，返回可录入
        if (personal == null) {
            return CommonConstant.INTEGER_1;
        }
        String applicationRoleId = personal.getApplicationRoleId();
        String applicationMinorRoleId = personal.getMinorRoleId();
        String subdividedRole = personal.getSubdividedRole();
        String minorSubdividedRole = personal.getMinorSubdividedRole();


        // 主辅子角色均为空，按不在范围处理，返回可录入
        if (StringUtils.isBlank(subdividedRole) && StringUtils.isBlank(minorSubdividedRole)) {
            return CommonConstant.INTEGER_1;
        }

        // 主辅子角色全部不在7个子角色中，返回可录入
        if (validRole(subdividedRole) && validRole(minorSubdividedRole)) {
            return CommonConstant.INTEGER_1;
        }

        // 主辅角色均为空，返回可录入
        if (StringUtils.isBlank(applicationRoleId) && StringUtils.isBlank(applicationMinorRoleId)) {
            return CommonConstant.INTEGER_1;
        }

        // 只要主辅角色的有一个不是前后端或质量测试，都返回可录入
        if (isNotSpecialRole(applicationRoleId) || isNotSpecialRole(applicationMinorRoleId)) {
            return CommonConstant.INTEGER_1;
        }

        // 其他返回不可录入
        return CommonConstant.INTEGER_0;
    }

    private boolean validRole(String role) {
        return StringUtils.isBlank(role) || !CommonConstant.ROLE_ORDER_STATIC.contains(role);
    }

    private boolean isNotSpecialRole(String roleId) {
        return StringUtils.isNotBlank(roleId) && !roleId.equals(SPECIAL_ROLE_1) && !roleId.equals(SPECIAL_ROLE_2);
    }

    @Override
    public List<CloudNativeUserDTO> getUserByRole(UserByRoleQueryDTO request) {
        try {
            String key = CommonConstant.REQUIRE_USER_ROLE_REDIS_KEY_PREFIX + ":" + request.getCode();
            log.info("角色查询用户信息roleCode:{}，redis-key:{}", request.getCode(), key);
            String response = redisService.get(key);
            if (StringUtils.isNotBlank(response)) {
                log.info("角色:{},查询用户信息接口直接从 缓存 返回", request.getCode());
                return new Gson().fromJson(response, new TypeToken<List<CloudNativeUserDTO>>() {
                }.getType());
            }
            UserResponseDTO response1 = userCenterFeignClient.getUserByRole(request);
            log.info("getUserByRole  roleCode:{}  response: {}", request.getCode(), JsonUtil.toJsonString(response1));
            List<CloudNativeUserDTO> dataList = response1.getData();
            if (CollectionUtil.isEmpty(dataList)) {
                log.info("角色查询用户信息接口返回为空!");
            }
            //N个小时后失效
            redisService.set(key, JSON.toJSONString(dataList), 60 * 60L);
            return dataList;
        } catch (Exception e) {
            // 发生异常，返回空列表
            return new ArrayList<>();
        }
    }

    @Override
    public SysTeamUser getUserByLoginAccount(String loginAccount) {
        try {
            String key = CommonConstant.USER_CODE_REDIS_KEY_PREFIX + ":" + loginAccount;
            String response = redisService.get(key);
            if (StringUtils.isNotBlank(response)) {
                return new Gson().fromJson(response, SysTeamUser.class);
            }
            SysTeamUser user = sysTeamUserMapper.selectByLoginAccount(loginAccount, null);
            redisService.set(key, JSON.toJSONString(user), 60 * 60L);
            return user;
        } catch (Exception e) {
            return new SysTeamUser();
        }
    }

    @Override
    public Result<String> asyncUserCenterData() {

        // 查询自有人员信息
        SysTeamUser param = new SysTeamUser();
        param.setUserType(CommonConstant.INTEGER_0);
        List<SysTeamUser> userList = sysTeamUserMapper.getTeamUserList(param);

        List<String> accounts = new ArrayList<>();
        for (SysTeamUser item : userList) {
            accounts.add(item.getLoginAccount());

            if (accounts.size() >= 50) {
                dealUserCenterData(accounts);
            }
        }

        // 同步剩余用户
        if (CollectionUtil.isNotEmpty(accounts)) {
            dealUserCenterData(accounts);
        }

        return Result.success("success!");
    }

    @Override
    public List<ProductManagerResponse> getPeriodReqProductManagerByTeam(ProductManagerRequest request) {

        // 1.获取并筛选战队下产品经理列表 (含离职及出项的)
        List<SysTeamUserDTO> userList = personalMapper.getUserListByTeam(request.getTeam());

        Set<String> seenLoginAccounts = new HashSet<>();
        List<SysTeamUserDTO> productManagerList = userList.stream()
                .filter(item -> CommonConstant.ROLE_PRODUCT_MANAGER.equals(item.getSubdividedRole()))
                .filter(item -> seenLoginAccounts.add(item.getLoginAccount()))
                .toList();
        Set<String> pmLoginAccountList = productManagerList.stream().map(SysTeamUserDTO::getLoginAccount).collect(Collectors.toSet());
        log.info("team product manager list：{}", JsonUtil.toJsonString(pmLoginAccountList));

        // 2.查询时间段内进行中的需求对应的产品经理, 查询日期的 起始日期不大于流程结束日期 且 终止日期不小于流程创建日期
        Date startDate = DateUtils.parseDate(request.getStartDate(), DateUtils.FORMAT_DATE);
        Date endDate = DateUtils.parseDate(request.getEndDate(), DateUtils.FORMAT_DATE);
        List<String> reqManagerList = onlinePlanInfoMapper.selectProductManagerByPeriod(startDate, endDate);
        log.info("req team product manager list：{}", JsonUtil.toJsonString(reqManagerList));

        // 3.前两者取交集
        List<SysTeamUserDTO> filterList = productManagerList.stream()
                .filter(item -> reqManagerList.contains(item.getLoginAccount()))
                .toList();
        Set<String> filterPmLoginAccountList = filterList.stream().map(SysTeamUserDTO::getLoginAccount).collect(Collectors.toSet());
        log.info("filter team product manager list：{}", JsonUtil.toJsonString(filterPmLoginAccountList));

        // 日志 ———— 对称差集 = (A - B) ∪ (B - A)
        Set<String> reqManagerSet = new HashSet<>(reqManagerList);
        Set<String> pickLoginAccountList = Stream.concat(
                // A 中存在但 B 中不存在的元素
                reqManagerList.stream()
                        .filter(e -> !pmLoginAccountList.contains(e)),
                // B 中存在但 A 中不存在的元素
                pmLoginAccountList.stream()
                        .filter(e -> !reqManagerSet.contains(e))
        ).collect(Collectors.toSet());
        log.info("pick out team product manager list：{}", JsonUtil.toJsonString(pickLoginAccountList));

        // 4.排序
        List<SysTeamUserDTO> mutableList = new ArrayList<>(filterList);
        CollUtil.sort(mutableList, (dto1, dto2) -> {
            String p1 = PinyinUtil.getPinyin(dto1.getUserName(), "");
            String p2 = PinyinUtil.getPinyin(dto2.getUserName(), "");
            return p1.compareToIgnoreCase(p2);
        });

        // 5.包装返回值
        List<ProductManagerResponse> productManagerResponseList = new ArrayList<>();

        mutableList.forEach(item -> {
            ProductManagerResponse response = new ProductManagerResponse();
            BeanUtils.copyProperties(item, response);
            productManagerResponseList.add(response);
        });

        return productManagerResponseList;
    }

    private void dealUserCenterData(List<String> accounts) {

        //获取用户中心数据
        UserCenterSearchRequest request = new UserCenterSearchRequest();
        request.setCurrentPage(1);
        request.setPageSize(accounts.size());
        request.setLoginNames(accounts);
        request.setOrgFlag("0");
        //获取用户中心数据，feign调用
        PageResult<SysTeamUserDTO> userCenterList = getUserCenterList(request);
        List<SysTeamUserDTO> userCenterListData = userCenterList.getData();
        // 更新用户部门，职级，岗位
        for (SysTeamUserDTO userDTO : userCenterListData) {
            SysTeamUser sysTeamUser = new SysTeamUser();
            sysTeamUser.setLoginAccount(userDTO.getLoginAccount());
            sysTeamUser.setCompany(userDTO.getDepartment());
            sysTeamUser.setPosition(userDTO.getPost());
            sysTeamUser.setLevel(userDTO.getRank());
            sysTeamUserMapper.updateSysTeamUser(sysTeamUser);
        }
    }

    @Override
    public List<TeamUserResponse> getTeamUsersByTeamIdAndRole(TeamUserQueryRequest request) {
        return personalMapper.getTeamUsersByTeamIdAndRole(request.getTeamId(), request.getSubdividedRole());
    }

    @Override
    public List<UserDTO> queryUserList(UserDTO user) {
        return personalMapper.queryUserList(user);
    }

    @Override
    public Result<Boolean> syncThirdPartyUser(List<ThirdPartyUserRequest> userList) {
        // Step 1: Early return if the input list is empty
        if (CollectionUtil.isEmpty(userList)) {
            return Result.success(Boolean.TRUE);
        }

        // Step 2: Validate dates and collect task bill/user codes
        Set<String> taskBillUserCodeSet = new HashSet<>();
        for (ThirdPartyUserRequest user : userList) {
            validateUserRequestDates(user);
            taskBillUserCodeSet.add(user.getTaskBillCode() + ":" + user.getUserCode());
        }

        // Step 3: Fetch existing users based on task bill and user codes
        List<SysTeamUser> existingUsers = personalMapper.selectByTaskBillUserCodes(new ArrayList<>(taskBillUserCodeSet));
        Map<String, SysTeamUser> userMap = existingUsers.stream()
                .collect(Collectors.toMap(
                        user -> user.getTaskBillCode() + ":" + user.getLoginAccount(),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        List<SysTeamUser> users = personalMapper.getUserList(userList.stream()
                .map(ThirdPartyUserRequest::getUserCode)
                .toList());
        Set<String> onlineUsers = users.stream().filter(item -> item.getIsDelete() == 0)
                .map(SysTeamUser::getLoginAccount)
                .collect(Collectors.toSet());

        // Step 4: Create a set for existing user codes for quick lookup
        Set<String> existingTaskBillUserCodes = existingUsers.stream()
                .map(user -> user.getTaskBillCode() + ":" + user.getLoginAccount())
                .collect(Collectors.toSet());

        // Step 5: Prepare lists for updates and inserts
        List<ThirdPartyUserRequest> updateList = new ArrayList<>();
        List<ThirdPartyUserRequest> insertList = new ArrayList<>();
        List<String> unlegalUsers = new ArrayList<>();

        for (ThirdPartyUserRequest userRequest : userList) {
            String uniqueKey = userRequest.getTaskBillCode() + ":" + userRequest.getUserCode();
            if (existingTaskBillUserCodes.contains(uniqueKey)) {
                SysTeamUser sysTeamUser = userMap.get(uniqueKey);
                // Set any null fields to existing values
                updateMissingFields(userRequest, sysTeamUser);
                updateList.add(userRequest);
            } else {
                // 在项人员中的历史任务单不添加（在项、入参任务单不同）
                if (!onlineUsers.contains(userRequest.getUserCode())) {
                    insertList.add(userRequest);
                } else {
                    unlegalUsers.add(uniqueKey);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(unlegalUsers)) {
            return Result.failed("已经存在当前有效的任务单，插入冲突数据如下：" + JsonUtil.toJsonString(unlegalUsers));
        }

        // Step 6: Batch update and insert to handle large datasets in chunks
        int batchSize = CommonConstant.INTEGER_1000;
        if (!updateList.isEmpty()) {
            batchProcessUpdate(updateList, batchSize);
            List<ThirdPartyUserRequest> lockScoreList = updateList.stream()
                    .filter(item -> item.getSettlementStartTime() != null &&
                            item.getSettlementEndTime() != null)
                    .toList();
            if (CollectionUtil.isNotEmpty(lockScoreList)) {
                aiTaskScoreEvaluationService.updateThirdPartyUserScore(lockScoreList);
            }
        }

        if (!insertList.isEmpty()) {
            batchProcessInsert(insertList, batchSize);
        }

        return Result.success(Boolean.TRUE);
    }

    // Separate method for date validation logic
    private void validateUserRequestDates(ThirdPartyUserRequest user) {
        List<String> dateFields = Arrays.asList(
                user.getEntryTime(),
                user.getLeaveTime(),
                user.getSettlementStartTime(),
                user.getSettlementEndTime()
        );

        for (String dateField : dateFields) {
            if (StringUtils.isNotBlank(dateField) && !DateUtils.validateDate(dateField, DateUtils.FORMAT_DATE_TIME)) {
                throw new BizException("日期格式校验不通过!");
            }
        }
    }

    // Separate method for updating missing fields
    private void updateMissingFields(ThirdPartyUserRequest userRequest, SysTeamUser sysTeamUser) {
        if (userRequest.getLeaveTime() == null) {
            userRequest.setLeaveTime(sysTeamUser.getOutTime());
        }
        if (userRequest.getSettlementStartTime() == null) {
            userRequest.setSettlementStartTime(sysTeamUser.getSettlementStartTime());
        }
        if (userRequest.getSettlementEndTime() == null) {
            userRequest.setSettlementEndTime(sysTeamUser.getSettlementEndTime());
        }
    }

    /**
     * 批量更新三方人员出项数据
     *
     * @param updateList 更新列表
     * @param batchSize 批量大小
     */
    private void batchProcessUpdate(List<ThirdPartyUserRequest> updateList, int batchSize) {
        for (int i = 0; i < updateList.size(); i += batchSize) {
            // Get the sublist for the current batch
            List<ThirdPartyUserRequest> batch = updateList.subList(i, Math.min(i + batchSize, updateList.size()));
            List<SysTeamUser> batchUsers = convertUser(batch);
            personalMapper.batchUpdateOutTime(batchUsers);
        }
    }

    /**
     * 批量插入三方人员数据
     *
     * @param insertList 插入列表
     * @param batchSize 批量大小
     */
    private void batchProcessInsert(List<ThirdPartyUserRequest> insertList, int batchSize) {
        for (int i = 0; i < insertList.size(); i += batchSize) {
            // Get the sublist for the current batch
            List<ThirdPartyUserRequest> batch = insertList.subList(i, Math.min(i + batchSize, insertList.size()));
            List<SysTeamUser> batchUsers = convertUser(batch);
            personalMapper.batchInsertThirdPartyUser(batchUsers);
        }
    }

    /**
     * 转换用户
     *
     * @param records 传入的三方用户
     * @return {@link List }<{@link SysTeamUser }>
     */
    private List<SysTeamUser> convertUser(List<ThirdPartyUserRequest> records) {
        List<SysTeamUser> userList = new ArrayList<>();

        // 预加载团队、角色信息、子角色映射
        Map<String, Integer> teamMap = teamInfoService.getAllTeamInfo()
                .stream()
                .collect(Collectors.toMap(TeamInfo::getTeamName, TeamInfo::getTeamId));

        List<TeamRole> roleList = teamRoleService.getAllRoleInfo();
        Map<String, Integer> roleMap = roleList
                .stream()
                .collect(Collectors.toMap(TeamRole::getRoleName, TeamRole::getRoleId));

        Map<String, String> subRoleMap = roleList.stream()
                .filter(role -> StringUtils.isNotBlank(role.getChildRoleName()))
                .flatMap(role -> {
                    String mainRole = role.getRoleName();
                    return Arrays.stream(role.getChildRoleName().split("、"))
                            .map(String::trim)
                            .filter(child -> !child.isEmpty())
                            .map(child -> new AbstractMap.SimpleEntry<>(child, mainRole));
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> replacement,
                        HashMap::new
                ));

        LocalDate currentDate = LocalDate.now();
        LocalDateTime currentDateTime = LocalDateTime.now();

        // 构造日期格式化器
        DateTimeFormatter createDateFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        DateTimeFormatter userCreateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);

        String createDate = currentDate.format(createDateFormatter);
        String userCreateTime = currentDateTime.format(userCreateTimeFormatter);

        for (ThirdPartyUserRequest user : records) {
            SysTeamUser dto = new SysTeamUser();

            dto.setPosition("员工");
            String team = teamMap.containsKey(user.getTeam()) ? user.getTeam() : ItPmTeamTransferEnum.getTeamFromItPmDesc(user.getTeam());
            dto.setTeam(team);
            dto.setTeamId(teamMap.get(team));

            String subRole = user.getSubRole();
            dto.setSubdividedRole(subRole);
            String role = subRoleMap.get(subRole);
            dto.setRoleName(role);
            dto.setRoleId(roleMap.get(role));

            // 设置其他属性
            dto.setUserName(user.getUserName());
            dto.setLoginAccount(user.getUserCode());
            dto.setUserId(user.getThirdEmpId());
            dto.setLevel(user.getRankName());
            dto.setCompany(user.getSupplierName());
            dto.setUserType(1);
            dto.setRecordableDays(7);
            dto.setEntryTime(StringUtils.isNotBlank(user.getEntryTime()) ? user.getEntryTime() : null);
            String leaveTime = user.getLeaveTime();

            if (StringUtils.isNotBlank(leaveTime)) {
                dto.setOutTime(leaveTime);
                Date leaveTimeDate = DateUtils.parseDate(leaveTime, DateUtils.FORMAT_DATE_TIME);
                assert leaveTimeDate != null;

                boolean isAfter = org.apache.commons.lang3.time.DateUtils.truncate(leaveTimeDate, Calendar.DAY_OF_MONTH).after(
                        org.apache.commons.lang3.time.DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH)
                );
                dto.setIsDelete(isAfter ? CommonConstant.INTEGER_0 : CommonConstant.INTEGER_1);
            } else {
                dto.setOutTime(null);
                dto.setIsDelete(CommonConstant.INTEGER_0);
            }

            dto.setTaskBillCode(user.getTaskBillCode());
            dto.setSettlementStartTime(StringUtils.isNotBlank(user.getSettlementStartTime()) ? user.getSettlementStartTime() : null);
            dto.setSettlementEndTime(StringUtils.isNotBlank(user.getSettlementEndTime()) ? user.getSettlementEndTime() : null);

            // 设置创建时间
            dto.setCreateTime(createDate);
            dto.setUserCreateTime(userCreateTime);

            userList.add(dto);
        }

        return userList;
    }
}
