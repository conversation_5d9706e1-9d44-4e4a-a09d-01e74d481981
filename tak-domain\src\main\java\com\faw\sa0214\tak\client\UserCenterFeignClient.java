package com.faw.sa0214.tak.client;

import com.alibaba.fastjson.JSONObject;
import com.faw.sa0214.tak.client.dto.user.*;
import com.faw.sa0214.tak.client.interceptor.FeignRequestInterceptor;
import com.faw.sa0214.tak.model.dto.UserCenterMainDTO;
import com.faw.sa0214.tak.model.dto.baseCenter.BCResult;
import com.faw.sa0214.tak.model.dto.baseCenter.BCPage;
import com.faw.sa0214.tak.model.dto.baseCenter.PositionInfo;
import com.faw.sa0214.tak.model.dto.baseCenter.UserCenterInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.dcp.common.rest.Result;

import java.util.List;

@FeignClient(name = "user-center", url = "${openapi.ucg.host}" , configuration = FeignRequestInterceptor.class)
public interface UserCenterFeignClient {
    /**
     * 获取用户中心列表
     *
     * @param params 参数
     * @return {@link UserCenterMainDTO}
     */
    @PostMapping(value = "/JT/BA/BA-0222/QFC/userCenter/page")
    UserCenterMainDTO getUserCenterList(@RequestBody JSONObject params);

    /**
     * 获取用户信息（平台级）
     *
     * @param loginName 精确查询的用户域账号信息
     * @return {@link Result}<{@link List}<{@link IworkCenterUserDTO}>>
     */
    @PostMapping("/JT/BA/BA-0222/QFC/user/getCenterUserInfo")
    Result<IworkCenterUserDTO> getCenterUserInfo(@RequestBody QueryUserInfoDTO loginName);

    /**
     * 根据角色查询用户
     *
     * @param params 参数
     * @return {@link UserCenterMainDTO}
     */
    @PostMapping("/JT/ITBA/BA-0222/qfc/auth/getUserByRole")
    UserResponseDTO getUserByRole(@RequestBody UserByRoleQueryDTO params);

    /**
     * 获取用户中心列表数据
     */
    @PostMapping(value = "/base-center/user/query/page/searchUser")
    BCResult<UserCenterInfo> queryUserCenterList(@RequestBody JSONObject params);

    /**
     * 获取职级信息
     */
    @PostMapping(value = "/base-center/query/page/getPositionInfos")
    BCResult<PositionInfo> getPositionInfos(@RequestBody JSONObject params);
}
