package com.faw.sa0214.tak.client.dto.codeReview;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 代码审查任务参数
 *
 * <AUTHOR>
 * @since 2024-11-28 18:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "代码审查任务参数")
public class CodeReviewTaskDTO {

    @Schema(description = "任务ID")
    private String bizId;

    @Schema(description = "文件列表")
    private List<CodeReviewFileDTO> fileList;

    @Schema(description = "问题列表")
    private List<CodeReviewProblemDTO> problemList;

    @Schema(description = "问题详情链接")
    private String detailUrl;

}
