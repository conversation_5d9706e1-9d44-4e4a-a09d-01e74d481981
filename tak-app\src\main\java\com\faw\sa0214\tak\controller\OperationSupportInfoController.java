package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.operation.*;
import com.faw.sa0214.tak.model.response.SupportProgressInfo;
import com.faw.sa0214.tak.po.operation.OperationSupportInfo;
import com.faw.sa0214.tak.service.operation.OperationSupportInfoService;
import lombok.RequiredArgsConstructor;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运维支持信息 控制类
 *
 * <AUTHOR>
 * @since 2025-03-06 09:05:45
 */
@Tag(name = "运维支持信息 控制类")
@RestController
@RequestMapping("/operationSupport")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OperationSupportInfoController {
    
    private final OperationSupportInfoService operationSupportInfoService;

    @Operation(summary = "新增或修改运维支持信息表", description = "[author:10236535]")
    @PostMapping("/insertOrUpdate")
    public Result<Integer> insertOrUpdate(@RequestBody OperationSupportInfo request) {
        return operationSupportInfoService.insertOrUpdate(request);
    }

    @Operation(summary = "运维单流转", description = "[author:50012536]")
    @PostMapping("/changeOpsStatus")
    public Result<String> changeOpsStatus(@RequestBody OpsParam request) {
        return operationSupportInfoService.changeOpsStatus(request);
    }

    @Operation(summary = "分页查询运维支持信息", description = "[author:10236535]")
    @PostMapping("/getOperationSupportInfoList")
    public Result<PageResult<OperationSupportInfoDTO>> getOperationSupportInfoList(@RequestBody OperationSupportInfoDTO request) {
        return operationSupportInfoService.getOperationSupportInfoList(request);
    }

    @Operation(summary = "批量标记复发问题", description = "[author:50012536]")
    @PostMapping("/setRecurrenceFlagBatch")
    public Result<String> setRecurrenceFlagBatch(@RequestBody OpsParam request) {
        return operationSupportInfoService.setRecurrenceFlagBatch(request);
    }

    @Operation(summary = "撤回运维单", description = "[author:50012536]")
    @PostMapping("/recallOps")
    public Result<String> recallOps(@RequestBody OpsParam request) {
        return operationSupportInfoService.recallOps(request);
    }

    @Operation(summary = "删除运维单", description = "[author:50012536]")
    @PostMapping("/delSupportInfo")
    public Result<String> delSupportInfo(@RequestBody OpsParam request) {
        return operationSupportInfoService.delSupportInfo(request);
    }

    @Operation(summary = "根据Id查询运维支持信息", description = "[author:50012536]")
    @PostMapping("/getOperationSupportInfoById")
    public Result<OperationSupportInfoDTO> getOperationSupportInfoById(@RequestBody OpsParam request) {
        return operationSupportInfoService.getOperationSupportInfoById(request);
    }

    @Operation(summary = "运维支持时间轴日志", description = "[author:50012536]")
    @PostMapping("/getOpsTimeLine")
    public Result<List<SupportProgressInfo>> getOpsTimeLine(@RequestBody OpsParam request) {
        return operationSupportInfoService.getOpsTimeLine(request);
    }

    /**
     * 旗效平台-运维单定时job5分钟
     */
    @Operation(summary = "全局扫描未完结任务-未关闭的-自动亮灯", description = "[author:10236535]")
    @PostMapping("/overallJob")
    public Result<Integer> overallJob(@RequestBody OperationSupportInfoDTO request) {
        // 超时亮灯预警
        operationSupportInfoService.overallJob(request);
        // 到期自动关闭
        operationSupportInfoService.businessPersonnelJob(request);
        return Result.success(1);
    }

    @Operation(summary = "扫描业务人员未处理任务", description = "[author:10236535]")
    @PostMapping("/businessPersonnelJob")
    public Result<Integer> businessPersonnelJob(@RequestBody OperationSupportInfoDTO request) {
        return operationSupportInfoService.businessPersonnelJob(request);
    }

    @Operation(summary = "查询单体平台列表", description = "[author:10236535]")
    @PostMapping("/getPlatFormList")
    public Result<List<OperationPlatformDTO>> getPlatFormList(@RequestBody OperationPlatformDTO request) {
        return operationSupportInfoService.getPlatFormList(request);
    }

    @Operation(summary = "查询功能模块业务单元列表", description = "[author:10236535]")
    @PostMapping("/getPlatModuleList")
    public Result<List<OperationPlatformDTO>> getPlatModuleList(@RequestBody OperationPlatformDTO request) {
        return operationSupportInfoService.getPlatModuleList(request);
    }

    @Operation(summary = "查询所属团队列表", description = "[author:10236535]")
    @PostMapping("/getGroupList")
    public Result<List<OperationPlatformDTO>> getGroupList(@RequestBody OperationPlatformDTO request) {
        return operationSupportInfoService.getGroupList(request);
    }

    @Operation(summary = "查询所属团队--敏捷教练列表", description = "[author:10236535]")
    @PostMapping("/getGroupPmList")
    public Result<List<OperationPlatformDTO>> getGroupPmList(@RequestBody OperationPlatformDTO request) {
        return operationSupportInfoService.getGroupPmList(request);
    }

    @Operation(summary = "查询产品经理列表", description = "[author:10236535]")
    @PostMapping("/getProductPmList")
    public Result<List<OperationPlatformDTO>> getProductPmList(@RequestBody OperationPlatformDTO request) {
        return operationSupportInfoService.getProductPmList(request);
    }

    @Operation(summary = "查询执行人列表", description = "[author:10236535]")
    @PostMapping("/getCreateUserList")
    public Result<List<OperationPlatformDTO>> getCreateUserList(@RequestBody OperationPlatformDTO request) {
        return operationSupportInfoService.getCreateUserList(request);
    }

    @Operation(summary = "查询所属专项列表", description = "[author:10236535]")
    @PostMapping("/getSpecialProjectList")
    public Result<List<OperationPlatformDTO>> getSpecialProjectList(@RequestBody OperationPlatformDTO request) {
        return operationSupportInfoService.getSpecialProjectList(request);
    }

    @Operation(summary = "新增或修改运维支持信息表 -- 开放api接口", description = "[author:10236535]")
    @PostMapping("/submitMaintenanceRequest")
    public Result<Integer> submitMaintenanceRequest(@RequestBody OperationSupportInfo request) {
        return operationSupportInfoService.submitMaintenanceRequest(request);
    }

}

