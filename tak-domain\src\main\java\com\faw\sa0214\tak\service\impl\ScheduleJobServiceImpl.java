package com.faw.sa0214.tak.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.faw.sa0214.tak.client.dto.devops.SmokeTestRequest;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.common.constant.enums.HistoryDataClassificationEnum;
import com.faw.sa0214.tak.common.constant.enums.RoleRankSecondTypeEnum;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.DecimalUtils;
import com.faw.sa0214.tak.common.util.JsonUtil;
import com.faw.sa0214.tak.config.UcgConfig;
import com.faw.sa0214.tak.mapper.*;
import com.faw.sa0214.tak.model.base.TakException;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.request.ProductManagerRequest;
import com.faw.sa0214.tak.model.request.ReqProductManageRequest;
import com.faw.sa0214.tak.model.response.*;
import com.faw.sa0214.tak.model.vo.*;
import com.faw.sa0214.tak.po.*;
import com.faw.sa0214.tak.service.*;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/03/07
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ScheduleJobServiceImpl implements ScheduleJobService {
    private final AiTaskScoreEvaluationService aiTaskScoreEvaluationService;
    private final AiEvaluationService aiEvaluationService;
    private final PublicReportService publicReportService;
    private final TbSendMessageService sendMessageService;
    private final SysCalendarService sysCalendarService;
    private final TakBusinessService takBusinessService;
    private final DevopsSyncService devopsSyncService;
    private final ReqReportService reqReportService;
    private final PersonalService personalService;
    private final DingTalkService dingTalkService;
    private final MetricsService metricsService;
    private final EaMapService eaMapService;
    private final ItBuService itBuService;

    private final AIScoreMapper aiScoreMapper;
    private final TeamInfoMapper teamInfoMapper;
    private final RequestService requestService;
    private final TbDyHoursMapper tbDyHoursMapper;
    private final SprintInfoMapper sprintInfoMapper;
    private final TbDySprintMapper tbDySprintMapper;
    private final SysTeamUserMapper sysTeamUserMapper;
    private final TbDyProjectMapper tbDyProjectMapper;
    private final TakBusinessMapper takBusinessMapper;
    private final TbDyStandardTimeMapper standardTimeMapper;
    private final StatisticalHistoryDataRecordMapper historyRecordMapper;
    private final AppPerformanceMetricMapper appPerformanceMetricMapper;
    @Autowired
    private UcgConfig ucgConfig;

    @Value(value = "${maintenance.robot.url}")
    private String robotUrl;
    @Value(value = "${maintenance.robot.token}")
    private String robotToken;
    @Value(value = "${maintenance.robot.secret}")
    private String robotSecret;
    @Value(value = "${pushweekly.minor.exclude}")
    private String excludeUsers;


    @Value(value = "${sla.robot.token}")
    private String slaRobotToken;
    @Value(value = "${sla.robot.secret}")
    private String slaRobotSecret;

    /**
     * 获取 AI 任务信息列表
     * 同步时间截止点：凌晨6点
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Override
    public int getAiTaskInfoList(String startDate, String endDate) {

        // 时间转换，赋予兜底默认值
        String startDateString;
        String endDateString;

        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endLocalDate = now.withHour(6).withMinute(0).withSecond(0).withNano(0);
            endDateString = DateUtils.formatDate(endLocalDate, DateUtils.FORMAT_DATE_TIME);

            LocalDateTime sevenDaysAgo = now.minusDays(7);
            LocalDateTime startLocalDate = sevenDaysAgo.withHour(6).withMinute(0).withSecond(0).withNano(0);
            startDateString = DateUtils.formatDate(startLocalDate, DateUtils.FORMAT_DATE_TIME);
        } else {
            startDateString = startDate + " 06:00:00";
            endDateString = endDate + " 06:00:00";
        }

        log.info("【ScheduleTask】 #getAiTaskInfoList  Start date:{}, End date:{}", startDateString, endDateString);

        return devopsSyncService.getAiTaskInfoList(startDateString, endDateString);
    }

    /**
     * 获取大禹任务
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void getIworkProject(String startDate, String endDate) {

        log.info("【ScheduleTask】 #getIworkProject  Start date:{}, End date:{}", startDate, endDate);
        List<String> days = getDateListOrDefault(startDate, endDate);
        log.info("【ScheduleTask】 #getIworkProject  sync days:{}", JSON.toJSONString(days));

        days.forEach(date -> {
            devopsSyncService.recordDevopsScrum(date, date);
        });

    }


    /**
     * 同步大禹已删除项目
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void syncDeletedItems(String startDate, String endDate) {
        log.info("【ScheduleTask】 #syncDeletedItems  Start date:{}, End date:{}", startDate, endDate);
        List<String> days = getDateListOrDefault(startDate, endDate);

        log.info("【ScheduleTask】 #syncDeletedItems  sync days:{}", JSON.toJSONString(days));
        days.forEach(devopsSyncService::syncDeletedItems);
        log.info("【ScheduleTask】 #syncDeletedItems  task end!");

    }

    /**
     * 获取大禹工时
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void getIworkHour(String startDate, String endDate) {

        log.info("【ScheduleTask】 #getIworkHour  Start date:{}, End date:{}", startDate, endDate);
        List<String> days = getDateListOrDefault(startDate, endDate);
        log.info("【ScheduleTask】 #getIworkHour  sync days:{}", JSON.toJSONString(days));

        days.forEach(date -> {
            devopsSyncService.recordDevopsHours(date, date);
        });
    }

    /**
     * 获取大禹冲刺
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void getIworkSprint(String startDate, String endDate) {

        log.info("【ScheduleTask】 #getIworkSprint  Start date:{}, End date:{}", startDate, endDate);
        List<String> days = getDateListOrDefault(startDate, endDate);
        log.info("【ScheduleTask】 #getIworkSprint  sync days:{}", JSON.toJSONString(days));

        days.forEach(date -> {
            devopsSyncService.recordDevopsSprint(date, date);
        });

        // 更新标准迭代的起始和终止时间
        updateStandardSprintDates();
    }

    /**
     * 更新标准迭代(bus_id=4655970186)的起始和终止时间
     * 只处理创建时间在2025年1月后的迭代
     */
    private void updateStandardSprintDates() {
        LocalDate startTime = LocalDate.of(2025, 1, 1);
        List<TbDySprintPO> sprintsToUpdate = tbDySprintMapper.selectByBusIdAndCreateTimeAfter(CommonConstant.STANDARD_SPRINT_BUSINESS_ID, startTime);
        if (CollectionUtil.isEmpty(sprintsToUpdate)) {
            return;
        }

        // 收集所有需要查询的sprintName
        List<String> sprintNames = sprintsToUpdate.stream()
                .map(TbDySprintPO::getSprintName)
                .collect(Collectors.toList());

        // 批量查询已存在的sprint
        List<SprintInfoPO> existingSprints = sprintInfoMapper.getSprintsByNames(sprintNames);

        // 构建已存在sprint的名称集合
        Set<String> existingSprintNames = existingSprints.stream()
                .map(SprintInfoPO::getSprintName)
                .collect(Collectors.toSet());

        // 区分需要更新和新增的sprint
        Map<Boolean, List<TbDySprintPO>> partitionedSprints = sprintsToUpdate.stream()
                .collect(Collectors.partitioningBy(sprint -> existingSprintNames.contains(sprint.getSprintName())));

        List<TbDySprintPO> sprintsToUpdateOnly = partitionedSprints.get(true);
        List<TbDySprintPO> sprintsToInsert = partitionedSprints.get(false);

        // 批量更新已存在的sprint
        if (CollectionUtil.isNotEmpty(sprintsToUpdateOnly)) {
            sprintsToUpdateOnly.forEach(sprint -> {
                SprintInfoPO record = new SprintInfoPO();
                BeanUtils.copyProperties(sprint, record);
                sprintInfoMapper.updateSprint(record);
            });
        }

        // 批量新增不存在的sprint
        if (CollectionUtil.isNotEmpty(sprintsToInsert)) {
            List<SprintInfoPO> newSprints = sprintsToInsert.stream()
                    .map(sprint -> {
                        SprintInfoPO newSprint = new SprintInfoPO();
                        newSprint.setSprintName(sprint.getSprintName());
                        newSprint.setStartTime(sprint.getStartTime());
                        newSprint.setEndTime(sprint.getEndTime());
                        newSprint.setCreatedTime(new Date());
                        newSprint.setCreatedBy("system");
                        newSprint.setUpdatedTime(new Date());
                        newSprint.setUpdatedBy("system");
                        newSprint.setIsDelete(0);
                        return newSprint;
                    })
                    .collect(Collectors.toList());
            sprintInfoMapper.batchInsert(newSprints);
        }
    }

    /**
     * 获取接口自动化测试用例
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void getIworkAutoCase(String startDate, String endDate) {
        log.info("【ScheduleTask】 #getIworkAutoCase  Start date:{}, End date:{}", startDate, endDate);

        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            LocalDateTime yesterday = LocalDateTime.now();
            endDate = DateUtils.formatDate(yesterday, DateUtils.FORMAT_DATE_TIME);

            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            startDate = DateUtils.formatDate(sevenDaysAgo, DateUtils.FORMAT_DATE_TIME);
        }
        // 查询待同步的项目信息
        List<TakBusiness> businessList = takBusinessService.getBusinessData();
        List<Long> busIdList = businessList.stream().map(TakBusiness::getBusid).distinct().toList();
        // 同步数据
        devopsSyncService.syncTbDyAutoCase(busIdList, startDate, endDate);
        log.info("schedule task------------------ record devops auto case success, startDate:{}, endDate:{}", startDate, endDate);
    }

    /**
     * 获取测试用例
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void getIworkTestCase(String startDate, String endDate) {
        log.info("【ScheduleTask】 #getIworkTestCase  Start date:{}, End date:{}", startDate, endDate);

        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            LocalDateTime yesterday = LocalDateTime.now();
            endDate = DateUtils.formatDate(yesterday, DateUtils.FORMAT_DATE_TIME);

            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            startDate = DateUtils.formatDate(sevenDaysAgo, DateUtils.FORMAT_DATE_TIME);
        }
        // 查询待同步的项目信息
        List<TakBusiness> businessList = takBusinessService.getBusinessData();
        List<Long> busIdList = businessList.stream().map(TakBusiness::getBusid).distinct().toList();
        // 同步数据
        devopsSyncService.syncTbDyTestCase(busIdList, startDate, endDate);
        log.info("schedule task------------------ record devops test case success, startDate:{}, endDate:{}", startDate, endDate);
    }

    @Override
    public void getIworkSmokeTest(SmokeTestRequest request) {
        log.info("【ScheduleTask】 #getIworkSmokeTest  request:{}", JsonUtil.toJsonString(request));
        if (request == null) {
            request = new SmokeTestRequest();
        }
        if (StringUtils.isBlank(request.getStartDate()) || StringUtils.isBlank(request.getEndDate())) {
            LocalDateTime yesterday = LocalDateTime.now();
            request.setEndDate(DateUtils.formatDate(yesterday, DateUtils.FORMAT_DATE_TIME));

            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            request.setStartDate(DateUtils.formatDate(sevenDaysAgo, DateUtils.FORMAT_DATE_TIME));
        }
        // 同步数据
        devopsSyncService.syncTbDySmokeTest(request);
    }

    @Override
    public int getPlatformIssues(String startDate, String endDate) {

        return devopsSyncService.getPlatformIssues(startDate, endDate);
    }

    /**
     * 需求池周报(新)-产品运营封板
     *
     * @param freshDateStr 新鲜枣 str
     * @return {@link List }<{@link ProductManageResponse }>
     */
    @Override
    public List<ProductManageResponse> getRequirementProductManageReport(String freshDateStr) {
        Date freshDate;
        if (StringUtils.isBlank(freshDateStr)) {
            freshDate = new Date();
        } else {
            freshDate = DateUtils.parseDate(freshDateStr, DateUtils.FORMAT_DATE_COMPACT);
        }
        List<String> loginAccounts = getAccounts(freshDate);
        log.info("【ScheduleTask】 #getRequirementProductManageReport  loginAccounts:{}", JSON.toJSONString(loginAccounts));
        List<ProductManageResponse> result = new ArrayList<>();
        loginAccounts.forEach(account -> {
            ReqProductManageRequest request = new ReqProductManageRequest();
            request.setProductManagerCode(account);
            request.setRefreshFlag(CommonConstant.INTEGER_1);
            request.setFreshDate(freshDate);
            request.setStartTime(freshDate);
            List<ProductManageResponse> productManageReport = reqReportService.getProductManageReport(request);
            result.addAll(productManageReport);
            log.info("【ScheduleTask】 #getRequirementProductManageReport handle task complete, loginAccounts:{}", JSON.toJSONString(account));
        });

        return result;
    }

    @NotNull
    private List<String> getAccounts(Date freshDate) {
        String firstDay = DateUtils.getFirstDay(freshDate);

        List<TeamInfo> allTeamInfo = teamInfoMapper.getAllTeamInfo();
        List<String> loginAccounts = new ArrayList<>();
        allTeamInfo.forEach(
                teamInfo -> {
                    ProductManagerRequest request = new ProductManagerRequest();
                    request.setTeam(teamInfo.getTeamName());
                    request.setStartDate(firstDay);
                    request.setEndDate(DateUtils.formatDate(freshDate, DateUtils.FORMAT_DATE_TIME));
                    List<ProductManagerResponse> periodReqProductManagerByTeam = personalService.getPeriodReqProductManagerByTeam(request);
                    periodReqProductManagerByTeam.forEach(
                            productManagerResponse -> {
                                if (!loginAccounts.contains(productManagerResponse.getLoginAccount())) {
                                    loginAccounts.add(productManagerResponse.getLoginAccount());
                                }
                            }
                    );
                }
        );
        return loginAccounts;
    }

    /**
     * 获取钉钉考勤率
     *
     * @param attendanceDay 拉取日期
     * @param flag          整月与单日的标识
     */
    @Override
    public void getDingTalkAttendance(String attendanceDay, Integer flag) {
        log.info("【ScheduleTask】 #getDingTalkAttendance, input date:{}, period flag:{}", attendanceDay, flag);

        LocalDate syncDate;
        if (StringUtils.isBlank(attendanceDay)) {
            syncDate = LocalDate.now().minusDays(1);
        } else {
            syncDate = LocalDate.parse(attendanceDay);
        }

        Integer year = syncDate.getYear();
        Integer month = syncDate.getMonthValue();
        Integer day;
        if (flag != null && flag == 0) {
            day = null;
        } else {
            day = syncDate.getDayOfMonth();
        }

        log.info("【ScheduleTask】 #getDingTalkAttendance.getDDMessage,year:{},month:{},day:{}", year, month, day);

        dingTalkService.getAttendance(year, month, day);
    }

    /**
     * 获取钉钉考勤信息
     *
     * @param date 拉取日期
     * @return {@link Integer}
     */
    @Override
    public Integer getDingTalkLeave(String date) {

        if (StringUtils.isBlank(date)) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime yesterday = now.minusDays(1);
            date = DateUtils.formatDate(yesterday, DateUtils.FORMAT_DATE);
        }

        return sendMessageService.getDingTalkLeave(date);
    }

    /**
     * 定时更新三方人员信息
     *
     * @return {@link Integer}
     */
    @Override
    public Integer updateItPmUserInfo() {
        return personalService.updateItPmUserInfo();
    }

    @Override
    public Integer recordItBuDataMonthly(String date) {
        // 入参时间准备  date要求为yyyy-MM
        String startDate;
        String endDate;
        if (StringUtils.isBlank(date)) {
            LocalDate today = LocalDate.now();
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
            startDate = today.minusMonths(1).withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endDate = today.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX).format(timeFormatter);
        } else {
            Date inputDate = DateUtils.parseDate(date, DateUtils.FORMAT_DATE_YEAR_MONTH);
            Calendar calendar = Calendar.getInstance();
            if (inputDate == null) {
                throw new TakException("输入日期转换失败，请确认！");
            }
            calendar.setTime(inputDate);
            // 设置为当月第一天
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            startDate = DateUtils.formatDate(calendar.getTime(), DateUtils.FORMAT_DATE_TIME);
            // 设置为当月最后一天
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            endDate = DateUtils.formatDate(calendar.getTime(), DateUtils.FORMAT_DATE_TIME);
        }

        return itBuService.recordItBuDataMonthly(startDate, endDate);
    }

    /**
     * 钉钉周报-战队运营数据
     *
     * @param date 计算日期 格式 20240523
     * @return 战队运营数据
     */
    @Override
    public TeamReportVO teamOperationReport(String date) {
        TeamReportVO teamReport = new TeamReportVO();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        String endTime = DateUtils.convertToDateTime(date);
        String startTime = DateUtils.getDateTimeAgo(endTime, 30);
        endTime = DateUtils.getLastDayEnd(endTime);

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_TEAM_REPORT.getCode(), null);

        if (historyRecord != null) {
            teamReport = new Gson().fromJson(historyRecord.getHistoryData(), TeamReportVO.class);
        } else {
            log.info("-----calculate teamOperationReport task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();
            // 人员列表
            List<SysTeamUser> userList = sysTeamUserMapper.getCurrentList();
            // 事项列表
            List<CalcReportInfo> projectList = tbDyProjectMapper.getReportProjectList(startTime, endTime);

            // 计算人员上线率
            itBuService.calcPersonOnline(teamReport, teamList, userList, projectList);

            // 计算角色与业务匹配率
            itBuService.calcRoleMatching(teamReport, teamList, userList, projectList);

            // 计算工时负荷
            itBuService.calcWorkHourLoad(teamReport, teamList, userList, startTime, endTime);

            // 计算任务质量
            itBuService.calcTaskQuality(teamReport, teamList, userList, startTime, endTime);

            // 保存数据
            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
            record.setIndicatorDate(date);
            record.setDataClassification(HistoryDataClassificationEnum.DING_TEAM_REPORT.getCode());
            record.setHistoryData(JSON.toJSONString(teamReport));
            historyRecordMapper.insertSelective(record);
        }

        // 比较历史数据
        compareHistoryData(teamReport, date);

        return teamReport;
    }

    /**
     * 比较钉钉周报历史数据
     */
    private void compareHistoryData(TeamReportVO teamReport, String date) {

        // 比较历史数据
        String lastWeek = DateUtils.getDateAgo(date, 7);
        // 查询历史数据
        StatisticalHistoryDataRecordPO lastWeekRecord = historyRecordMapper.selectByDateAndType(lastWeek,
                HistoryDataClassificationEnum.DING_TEAM_REPORT.getCode(), null);

        if (lastWeekRecord != null) {
            TeamReportVO lastWeekReport = new Gson().fromJson(lastWeekRecord.getHistoryData(), TeamReportVO.class);
            teamReport.setOnlineTrend(
                    lastWeekReport.getPersonOnline() > teamReport.getPersonOnline() ? 2 :
                            lastWeekReport.getPersonOnline() < teamReport.getPersonOnline() ? 1 : 0
            );
            teamReport.setMatchRateTrend(
                    lastWeekReport.getMatchingRate() > teamReport.getMatchingRate() ? 2 :
                            lastWeekReport.getMatchingRate() < teamReport.getMatchingRate() ? 1 : 0
            );
            teamReport.setWorkHourLoadTrend(
                    lastWeekReport.getWorkHourLoad() > teamReport.getWorkHourLoad() ? 2 :
                            lastWeekReport.getWorkHourLoad() < teamReport.getWorkHourLoad() ? 1 : 0
            );
            teamReport.setTaskQualityTrend(
                    lastWeekReport.getTaskQuality() > teamReport.getTaskQuality() ? 2 :
                            lastWeekReport.getTaskQuality() < teamReport.getTaskQuality() ? 1 : 0
            );
        } else {
            teamReport.setOnlineTrend(0);
            teamReport.setMatchRateTrend(0);
            teamReport.setWorkHourLoadTrend(0);
            teamReport.setTaskQualityTrend(0);
        }
    }

    /**
     * 钉钉周报-战队运营列表数据
     *
     * @param date 计算日期 格式 20240523
     * @return 战队运营列表
     */
    @Override
    public List<TeamOperateListVO> teamOperationList(String date) {
        List<TeamOperateListVO> teamOperateList = new ArrayList<>();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        String endTime = DateUtils.convertToDateTime(date);
        String startTime = DateUtils.getDateTimeAgo(endTime, 30);
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_TEAM_OPERATE.getCode(), null);

        if (historyRecord != null) {
            teamOperateList = new Gson().fromJson(historyRecord.getHistoryData(), new TypeToken<List<TeamOperateListVO>>() {
            }.getType());
        } else {
            log.info("-----calculate teamOperationList task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();
            // 事项列表
            List<CalcReportInfo> projectList = tbDyProjectMapper.getTeamOperateTask(startTime, endTime);

            for (TeamInfo teamInfo : teamList) {
                TeamOperateListVO teamOperate = new TeamOperateListVO();
                teamOperate.setTeamId(teamInfo.getTeamId());
                teamOperate.setTeam(teamInfo.getTeamName());
                teamOperateList.add(teamOperate);
            }

            // 计算战队迭代周期
            itBuService.calcSprintWeek(teamOperateList, projectList, startTime, endTime);

            // 计算需求耗时 和 需求耗时的正态分布数据
            itBuService.calcRequirementTime(teamOperateList, projectList, startTime, endTime);

            // 计算业务单元激活率
            itBuService.calcUnitActiveRate(teamOperateList, startTime, endTime);

            // 保存数据
            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
            record.setIndicatorDate(date);
            record.setDataClassification(HistoryDataClassificationEnum.DING_TEAM_OPERATE.getCode());
            record.setHistoryData(JSON.toJSONString(teamOperateList));
            historyRecordMapper.insertSelective(record);

        }

        return teamOperateList;
    }

    /**
     * 钉钉周报-战队运营-正态分布数据
     *
     * @param date 计算日期 格式 20240523
     * @return 战队运营列表
     */
    @Override
    public TeamOperateDistributionListVO teamOperationDistributionList(String date) {
        TeamOperateDistributionListVO vo = new TeamOperateDistributionListVO();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        String endTime = DateUtils.convertToDateTime(date);
        String startTime = DateUtils.getDateTimeAgo(endTime, 30);
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_TEAM_OPERATE_DIDTRIBUTION.getCode(), null);

        if (historyRecord != null) {
            vo = new Gson().fromJson(historyRecord.getHistoryData(), TeamOperateDistributionListVO.class);
        } else {
            log.info("-----calculate teamOperationDistributionList task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();
            // 事项列表
            List<CalcReportInfo> projectList = tbDyProjectMapper.getTeamOperateTask(startTime, endTime);

            // 计算迭代周期
            itBuService.calcSprintWeekByDes(vo, projectList, startTime, endTime);

            // 保存数据
            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
            record.setIndicatorDate(date);
            record.setDataClassification(HistoryDataClassificationEnum.DING_TEAM_OPERATE_DIDTRIBUTION.getCode());
            record.setHistoryData(JSON.toJSONString(vo));
            historyRecordMapper.insertSelective(record);

        }

        return vo;
    }

    /**
     * 计算业务单元运营数据
     */
    @Override
    public BusinessReportVO businessReport(String date) {
        BusinessReportVO businessReport = new BusinessReportVO();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        String endTime = DateUtils.convertToDateTime(date);
        String startTime = DateUtils.getDateTimeAgo(endTime, 14);
        endTime = DateUtils.getLastDayEnd(endTime);

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_BUSINESS_OPERATE.getCode(), null);

        if (historyRecord != null) {
            businessReport = new Gson().fromJson(historyRecord.getHistoryData(), BusinessReportVO.class);
        } else {
            log.info("----- calculate businessReport task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<String> teamList = teamInfoMapper.getReportTeam().stream().map(TeamInfo::getTeamName).collect(Collectors.toList());

            // 获取IT业务单元
            List<StandardTimeDeliverable> unitInfoList = standardTimeMapper.selectByTeamRoleBus(null, null);

            // 事项列表
            List<CalcReportInfo> projectList = tbDyProjectMapper.getTeamTaskList(startTime, endTime).stream()
                    .filter(s -> CommonConstant.SUB_ROLE.contains(s.getSubRole()))
                    .filter(s -> teamList.contains(s.getTeam()))
                    .collect(Collectors.toList());

            // 计算业务单元调用率
            List<UnitCallRateVO> unitCallRateList = itBuService.calcUnitCallRate(unitInfoList, projectList, teamList);
            businessReport.setUnitCallRateList(unitCallRateList);

            // 计算业务单元投入工时
            List<UnitWorkHourVO> unitWorkHourList = itBuService.calcUnitWorkHour(unitInfoList, projectList, teamList);
            businessReport.setUnitWorkHourList(unitWorkHourList);

            // 计算各战队业务单元激活率
            List<TeamUnitActiveVO> teamUnitActiveList = itBuService.calcTeamUnitActive(unitInfoList, projectList, teamList);
            businessReport.setTeamUnitActiveList(teamUnitActiveList);
            businessReport.setBusinessUnitNum(unitInfoList.size());
            businessReport.setSubRoleList(CommonConstant.SUB_ROLE);

            // 保存数据
            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
            record.setIndicatorDate(date);
            record.setDataClassification(HistoryDataClassificationEnum.DING_BUSINESS_OPERATE.getCode());
            record.setHistoryData(JSON.toJSONString(businessReport, SerializerFeature.DisableCircularReferenceDetect));
            historyRecordMapper.insertSelective(record);
        }
        return businessReport;
    }

    @Override
    public Integer roleAnalysis(String date) {
        // 入参格式为yyyyMMdd
        LocalDate currentDate;
        if (StringUtils.isBlank(date)) {
            currentDate = LocalDate.now();
        } else {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
            try {
                currentDate = LocalDate.parse(date, inputFormatter);
            } catch (Exception e) {
                throw new TakException("输入日期转换失败，请确认！");
            }
        }
        return publicReportService.roleAnalysis(currentDate);
    }

    @Override
    public String roleRankAnalysis(String date) {
        // 处理时间
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate = LocalDate.parse(date, inputFormatter);

        // 遍历枚举，计算角色大榜数据
        for (RoleRankSecondTypeEnum role : RoleRankSecondTypeEnum.values()) {
            publicReportService.getRoleReportRanking(currentDate, role.getRoleName(), role.getType());
        }
        return "success";
    }

    @Override
    public String dingNotes() {

        // 查询日期构建
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        String date = currentDate.format(inputFormatter);
        List<String> dates = Collections.singletonList(date);

        List<StatisticalHistoryDataRecordPO> roleRecords = historyRecordMapper.selectByDatesAndType(dates,
                HistoryDataClassificationEnum.ROLE_ANALYSIS.getCode(), null);
        List<StatisticalHistoryDataRecordPO> teamRecords = historyRecordMapper.selectByDatesAndType(dates,
                HistoryDataClassificationEnum.DING_TEAM_REPORT.getCode(), null);
        List<StatisticalHistoryDataRecordPO> operateRecords = historyRecordMapper.selectByDatesAndType(dates,
                HistoryDataClassificationEnum.DING_TEAM_OPERATE.getCode(), null);

        DateTimeFormatter aiTaskFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_YEAR_MONTH);
        String aiTaskDate = currentDate.format(aiTaskFormatter);
        // 获取上月日期
        String lastMonth = DateUtils.getLastMonth(aiTaskDate);
        int aiTaskNum = historyRecordMapper.getAiTaskListByMonth(lastMonth);

        // 推送消息构建
        String message;
        StringBuilder errorMessage = new StringBuilder();
        if (CollectionUtil.isEmpty(roleRecords)) {
            errorMessage.append("旗效周报角色运营数据不存在，请及时处理；");
        }
        if (CollectionUtil.isEmpty(teamRecords)) {
            errorMessage.append("旗效周报战队运营数据不存在，请及时处理；");
        }
        if (CollectionUtil.isEmpty(operateRecords)) {
            errorMessage.append("旗效周报战队运行列表数据不存在，请及时处理；");
        }
        if (aiTaskNum <= 0 && currentDate.getDayOfMonth() == 8) {
            errorMessage.append("旗效月度同步分值数据不存在，请及时处理；");
        }

        if (StringUtils.isBlank(errorMessage.toString())) {
            return "平安无事";
        } else {
            message = "【旗效监控消息】" + errorMessage.substring(0, errorMessage.length() - 1);
        }

        // 钉钉机器人推送群聊
        Boolean atFlag = true;
        Boolean flag = pushRobotMsg(robotToken, null, robotSecret, message, atFlag);

        return (flag ? "推送成功, " : "推送失败, ") + message;
    }

    private Boolean pushRobotMsg(String robotToken, String userIds, String secret, String message, Boolean atFlag) {
        try {
            // 验签
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            log.info("dingTalk robot sign:{}", sign);

            //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息
            DingTalkClient client = new DefaultDingTalkClient(robotUrl + "?sign=" + sign + "&timestamp=" + timestamp);
            OapiRobotSendRequest req = new OapiRobotSendRequest();

            //定义文本内容
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            text.setContent(message);

            //定义 @ 对象
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setIsAtAll(atFlag);
            if (StringUtils.isNotBlank(userIds)) {
                at.setAtUserIds(Arrays.asList(userIds.split(",")));
            }

            //设置消息类型
            req.setMsgtype("text");
            req.setText(text);
            req.setAt(at);
            OapiRobotSendResponse rsp = client.execute(req, robotToken);
            log.info("dingTalk robot response:{}", rsp.getBody());
            return rsp.isSuccess();
        } catch (Exception e) {
            log.error("dingTalk robot response error!", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 发送钉钉工时录入提醒
     */
    @Override
    public void sendDingTalkNotes() {
        sendMessageService.sendMessage();
    }

    @Override
    public String insertL1FlowList() {
        return eaMapService.insertL1FlowList();
    }

    /**
     * 拉取 EA MAP 流程
     *
     * @return {@link String}
     */
    @Override
    public String insertFlowList() {
        return eaMapService.insertFlowList();
    }

    /**
     * 拉取 EA MAP 业务单元
     *
     * @return {@link String}
     */
    @Override
    public String insertBizUnitList() {
        return eaMapService.insertBizUnitList();
    }


    /**
     * 获取日期列表或默认值
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link String}>
     */
    private List<String> getDateListOrDefault(String startDate, String endDate) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime yesterday = now.minusDays(1);
            endDate = DateUtils.formatDate(yesterday, DateUtils.FORMAT_DATE);

            LocalDateTime sevenDaysAgo = now.minusDays(7);
            startDate = DateUtils.formatDate(sevenDaysAgo, DateUtils.FORMAT_DATE);
        }

        return DateUtils.periodDays(startDate, endDate);
    }

    @Override
    public void createDingWeekly(PushDingDTO dto) {
        sendMessageService.createDingWeekly(dto);
    }

    @Override
    public void createDingWeeklyMinor(PushDingDTO dto) {
        sendMessageService.createDingWeeklyMinor(dto);
    }

    @Override
    public void pushDingWeekly(PushDingDTO dto) {
        sendMessageService.pushDingWeekly(dto);
    }

    @Override
    public void pushDingWeeklyMinor(PushDingDTO dto) {
        sendMessageService.pushDingWeeklyMinor(dto);
    }

    /**
     * 钉钉周报-工时运营-忙闲指数数据
     * ** 忙闲统计全部角色，不是7个子角色 **
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-忙闲指数列表
     */
    @Override
    public List<BusyAndIdleListVO> busyAndIdleReport(String date, Integer userType) {
        List<BusyAndIdleListVO> returnList = new ArrayList<>();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询忙闲指数列表日期错误,{}", date, e);
        }

        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        String msg = "";
        if (currentDate.getDayOfMonth() <= 7) {
            // 如果是月初的7天内，计算上个月的第一天和最后一天
            startTime = currentDate.minusMonths(1).withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX).format(timeFormatter);

        } else {
            // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
            startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
            msg = "";
        }

        // 查询工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        // 负荷工时 (指标1)
        double loadHour = 0.0;
        loadHour = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85);
        // 负荷工时*1.2 (指标2)
        double loadHour2 = 0.0;
        loadHour2 = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85 * 1.2);

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST.getCode(), userType);

        if (historyRecord != null) {
            returnList = new Gson().fromJson(historyRecord.getHistoryData(), new TypeToken<List<BusyAndIdleListVO>>() {
            }.getType());
        } else {
            log.info("-----calculate busyAndIdleReport task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();
            // ** 忙闲统计全部角色，不是7个子角色 **

            String userTypeStr = "";
            //定时任务的list
            List<String> userTypeJobList = new ArrayList<>();
            if (userType == -1) {
                // 查所有，数据库只有0和1
            } else if (userType == -200) {
                userTypeJobList = Arrays.asList("-1", "0", "1");
            } else {
                userTypeStr = userType.toString();
            }
            if (CollectionUtil.isNotEmpty(userTypeJobList) && userType == -200) {
                //定时任务的list ，循环把三种情况插入到数据库
                for (String userTypeStrJob : userTypeJobList) {
                    List<BusyAndIdleListVO> returnListJob = new ArrayList<>();
                    StatisticalHistoryDataRecordPO historyRecordJob = historyRecordMapper.selectByDateAndType(date,
                            HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST.getCode(), Integer.parseInt(userTypeStrJob));
                    if (historyRecordJob != null) {
                        returnListJob = new Gson().fromJson(historyRecordJob.getHistoryData(), new TypeToken<List<BusyAndIdleListVO>>() {
                        }.getType());
                    } else {
                        String userTypeStrJobStr = "";
                        if ("-1".equals(userTypeStrJob)) {
                            // 查所有，数据库只有0和1
                        } else {
                            userTypeStrJobStr = userTypeStrJob;
                        }
                        List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHours(startTime, endTime, userTypeStrJobStr);
                        Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));

                        for (TeamInfo teamInfo : teamList) {
                            double loadHourEach = 0.0;
                            double loadHour2Each = 0.0;
                            BusyAndIdleListVO vo = new BusyAndIdleListVO();
                            List<BusyAndIdleResponse> list = map.get(teamInfo.getTeamName());
                            if (CollectionUtil.isNotEmpty(list)) {
                                vo.setTeam(teamInfo.getTeamName());
                                vo.setTeamId(teamInfo.getTeamId());
                                // 该战队总人数
                                int countAll = list.size();
                                int count1 = 0;//空闲
                                List<BusyAndIdlePersonListVO> person1 = new ArrayList<>();
                                int count2 = 0;//均衡
                                List<BusyAndIdlePersonListVO> person2 = new ArrayList<>();
                                int count3 = 0;//忙碌
                                List<BusyAndIdlePersonListVO> person3 = new ArrayList<>();
                                List<BusyAndIdleSubListVO> subList = new ArrayList<>();
                                if (currentDate.getDayOfMonth() <= 7) {
                                    // 如果是月初的7天内，计算上个月的第一天和最后一天
                                    msg = "上月法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                                } else {
                                    msg = "本月至周报推送前一天，法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                                }
                                vo.setMsg(msg);
                                for (BusyAndIdleResponse response : list) {
                                    // 判断 此人的拉入时间, 调整标准
                                    // 如统计月份有新拉入人员清单的人员，包括自有及三方。则根据拉入日期（不算日期当天），计算周报推送日期的工作天数，作为忙闲的标准
                                    if (response.getCreatedDate() != null &&
                                            response.getCreatedDate().after(DateUtils.parseDate(startTime, DateUtils.FORMAT_DATE_TIME)) &&
                                            response.getCreatedDate().before(DateUtils.parseDate(endTime, DateUtils.FORMAT_DATE_TIME))) {
                                        int workDaysChange = sysCalendarService.getWorkDays(
                                                DateUtils.convertToDate(DateUtils.formatDate(response.getCreatedDate(), DateUtils.FORMAT_DATE_TIME)),
                                                DateUtils.convertToDate(endTime));
                                        loadHourEach = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85);
                                        loadHour2Each = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85 * 1.2);
                                    } else {
                                        loadHourEach = loadHour;
                                        loadHour2Each = loadHour2;
                                    }

                                    BigDecimal hours = response.getHours();
                                    double value = hours.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                    BusyAndIdlePersonListVO personVO = new BusyAndIdlePersonListVO();
                                    personVO.setName(response.getUserName());
                                    personVO.setRealHour(hours);
                                    // 差距=实际-测算标准负荷工时
                                    personVO.setGap(hours.subtract(BigDecimal.valueOf(loadHourEach)));
                                    if (value < loadHourEach) {
                                        count1++;
                                        person1.add(personVO);
                                    } else if (value >= loadHourEach && value <= loadHour2Each) {
                                        count2++;
                                        person2.add(personVO);
                                    } else if (value > loadHour2Each) {
                                        count3++;
                                        person3.add(personVO);
                                    }
                                }
                                Collections.sort(person1, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                                Collections.sort(person2, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                                Collections.sort(person3, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                                BusyAndIdleSubListVO subVO1 = new BusyAndIdleSubListVO();
                                subVO1.setType(1);
                                subVO1.setCount(count1);
                                subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(countAll), 2));
                                subVO1.setPerson(person1);
                                subList.add(subVO1);
                                BusyAndIdleSubListVO subVO2 = new BusyAndIdleSubListVO();
                                subVO2.setType(2);
                                subVO2.setCount(count2);
                                subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(countAll), 2));
                                subVO2.setPerson(person2);
                                subList.add(subVO2);
                                BusyAndIdleSubListVO subVO3 = new BusyAndIdleSubListVO();
                                subVO3.setType(3);
                                subVO3.setCount(count3);
                                subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(countAll), 2));
                                subVO3.setPerson(person3);
                                subList.add(subVO3);

                                vo.setDetail(subList);
                            }
                            returnListJob.add(vo);
                        }

                        // 保存数据
                        StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                        record.setIndicatorDate(date);
                        record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST.getCode());
                        record.setDataSecondaryClassification(Integer.parseInt(userTypeStrJob));
                        record.setHistoryData(JSON.toJSONString(returnListJob));
                        historyRecordMapper.insertSelective(record);
                    }
                }
            } else {
                // 非定时任务执行
                List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHours(startTime, endTime, userTypeStr);
                Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));

                for (TeamInfo teamInfo : teamList) {
                    double loadHourEach = 0.0;
                    double loadHour2Each = 0.0;
                    BusyAndIdleListVO vo = new BusyAndIdleListVO();
                    List<BusyAndIdleResponse> list = map.get(teamInfo.getTeamName());
                    if (CollectionUtil.isNotEmpty(list)) {
                        vo.setTeam(teamInfo.getTeamName());
                        vo.setTeamId(teamInfo.getTeamId());
                        // 该战队总人数
                        int countAll = list.size();
                        int count1 = 0;//空闲
                        List<BusyAndIdlePersonListVO> person1 = new ArrayList<>();
                        int count2 = 0;//均衡
                        List<BusyAndIdlePersonListVO> person2 = new ArrayList<>();
                        int count3 = 0;//忙碌
                        List<BusyAndIdlePersonListVO> person3 = new ArrayList<>();
                        List<BusyAndIdleSubListVO> subList = new ArrayList<>();
                        if (currentDate.getDayOfMonth() <= 7) {
                            // 如果是月初的7天内，计算上个月的第一天和最后一天
                            msg = "上月法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                        } else {
                            msg = "本月至周报推送前一天，法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                        }
                        vo.setMsg(msg);
                        for (BusyAndIdleResponse response : list) {
                            // 判断 此人的拉入时间, 调整标准
                            // 如统计月份有新拉入人员清单的人员，包括自有及三方。则根据拉入日期（不算日期当天），计算周报推送日期的工作天数，作为忙闲的标准
                            if (response.getCreatedDate() != null &&
                                    response.getCreatedDate().after(DateUtils.parseDate(startTime, DateUtils.FORMAT_DATE_TIME)) &&
                                    response.getCreatedDate().before(DateUtils.parseDate(endTime, DateUtils.FORMAT_DATE_TIME))) {
                                int workDaysChange = sysCalendarService.getWorkDays(DateUtils.convertToDate(
                                                DateUtils.formatDate(response.getCreatedDate(), DateUtils.FORMAT_DATE_TIME)),
                                        DateUtils.convertToDate(endTime));
                                loadHourEach = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85);
                                loadHour2Each = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85 * 1.2);
                            } else {
                                loadHourEach = loadHour;
                                loadHour2Each = loadHour2;
                            }

                            BigDecimal hours = response.getHours();
                            double value = hours.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            BusyAndIdlePersonListVO personVO = new BusyAndIdlePersonListVO();
                            personVO.setName(response.getUserName());
                            personVO.setRealHour(hours);
                            // 差距=实际-测算标准负荷工时
                            personVO.setGap(hours.subtract(BigDecimal.valueOf(loadHourEach)));
                            if (value < loadHourEach) {
                                count1++;
                                person1.add(personVO);
                            } else if (value >= loadHourEach && value <= loadHour2Each) {
                                count2++;
                                person2.add(personVO);
                            } else if (value > loadHour2Each) {
                                count3++;
                                person3.add(personVO);
                            }
                        }
                        Collections.sort(person1, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                        Collections.sort(person2, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                        Collections.sort(person3, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                        BusyAndIdleSubListVO subVO1 = new BusyAndIdleSubListVO();
                        subVO1.setType(1);
                        subVO1.setCount(count1);
                        subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(countAll), 2));
                        subVO1.setPerson(person1);
                        subList.add(subVO1);
                        BusyAndIdleSubListVO subVO2 = new BusyAndIdleSubListVO();
                        subVO2.setType(2);
                        subVO2.setCount(count2);
                        subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(countAll), 2));
                        subVO2.setPerson(person2);
                        subList.add(subVO2);
                        BusyAndIdleSubListVO subVO3 = new BusyAndIdleSubListVO();
                        subVO3.setType(3);
                        subVO3.setCount(count3);
                        subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(countAll), 2));
                        subVO3.setPerson(person3);
                        subList.add(subVO3);

                        vo.setDetail(subList);
                    }
                    returnList.add(vo);
                }

                // 保存数据
                StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                record.setIndicatorDate(date);
                record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST.getCode());
                record.setDataSecondaryClassification(userType);
                record.setHistoryData(JSON.toJSONString(returnList));
                historyRecordMapper.insertSelective(record);
            }

        }

        return returnList;
    }

    /**
     * 钉钉周报-工时运营-忙闲指数数据
     * ** 忙闲统计全部角色，不是7个子角色 **
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-忙闲指数列表
     */
    @Override
    public List<BusyAndIdleListVO> busyAndIdleReportMinor(String date, Integer userType) {
        List<BusyAndIdleListVO> returnList = new ArrayList<>();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询忙闲指数列表日期错误,{}", date, e);
        }

        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        String msg = "";
        if (currentDate.getDayOfMonth() <= 7) {
            // 如果是月初的7天内，计算上个月的第一天和最后一天
            startTime = currentDate.minusMonths(1).withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX).format(timeFormatter);

        } else {
            // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
            startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
            msg = "";
        }

        // 查询工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        // 负荷工时 (指标1)
        double loadHour = 0.0;
        loadHour = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85);
        // 负荷工时*1.2 (指标2)
        double loadHour2 = 0.0;
        loadHour2 = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85 * 1.2);

        //副版-排除人员
        List<String> userExcludeList = Arrays.asList(ucgConfig.getExclude().split(","));

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST_MINOR.getCode(), userType);

        if (historyRecord != null) {
            returnList = new Gson().fromJson(historyRecord.getHistoryData(), new TypeToken<List<BusyAndIdleListVO>>() {
            }.getType());
        } else {
            log.info("-----calculate busyAndIdleReportMinor task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamMinorInfo> teamList = teamInfoMapper.getReportMinorTeam(CommonConstant.ROLE_ORDER_STATIC, userExcludeList);
            // ** 忙闲统计全部角色，不是7个子角色 **

            String userTypeStr = "";
            //定时任务的list
            List<String> userTypeJobList = new ArrayList<>();
            if (userType == -1) {
                // 查所有，数据库只有0和1
            } else if (userType == -200) {
                userTypeJobList = Arrays.asList("-1", "0", "1");
            } else {
                userTypeStr = userType.toString();
            }
            if (CollectionUtil.isNotEmpty(userTypeJobList) && userType == -200) {
                //定时任务的list ，循环把三种情况插入到数据库
                for (String userTypeStrJob : userTypeJobList) {
                    List<BusyAndIdleListVO> returnListJob = new ArrayList<>();
                    StatisticalHistoryDataRecordPO historyRecordJob = historyRecordMapper.selectByDateAndType(date, HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST_MINOR.getCode(), Integer.parseInt(userTypeStrJob));
                    if (historyRecordJob != null) {
                        returnListJob = new Gson().fromJson(historyRecordJob.getHistoryData(), new TypeToken<List<BusyAndIdleListVO>>() {
                        }.getType());
                    } else {
                        String userTypeStrJobStr = "";
                        if ("-1".equals(userTypeStrJob)) {
                            // 查所有，数据库只有0和1
                        } else {
                            userTypeStrJobStr = userTypeStrJob;
                        }
                        List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHoursMinor(startTime,
                                endTime, userTypeStrJobStr, CommonConstant.ROLE_ORDER_STATIC, userExcludeList);
                        Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));

                        for (TeamMinorInfo teamInfo : teamList) {
                            BusyAndIdleListVO vo = new BusyAndIdleListVO();
                            vo.setTeam(teamInfo.getTeamName());
                            vo.setTeamId(teamInfo.getTeamId());
                            List<BusyAndIdleResponse> list = map.get(teamInfo.getTeamName());
                            List<BusyAndIdleSubListVO> subList = new ArrayList<>();
                            if (CollectionUtil.isNotEmpty(list)) {
                                // 该战队总人数
                                int countAll = list.size();
                                int count1 = 0;//空闲
                                List<BusyAndIdlePersonListVO> person1 = new ArrayList<>();
                                int count2 = 0;//均衡
                                List<BusyAndIdlePersonListVO> person2 = new ArrayList<>();
                                int count3 = 0;//忙碌
                                List<BusyAndIdlePersonListVO> person3 = new ArrayList<>();

                                if (currentDate.getDayOfMonth() <= 7) {
                                    // 如果是月初的7天内，计算上个月的第一天和最后一天
                                    msg = "上月法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                                } else {
                                    msg = "本月至周报推送前一天，法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                                }
                                vo.setMsg(msg);
                                for (BusyAndIdleResponse response : list) {

                                    BigDecimal hours = response.getHours();
                                    double value = hours.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                    BusyAndIdlePersonListVO personVO = new BusyAndIdlePersonListVO();
                                    personVO.setName(response.getUserName());
                                    personVO.setRealHour(hours);
                                    // 差距=实际-测算标准负荷工时
                                    personVO.setGap(hours.subtract(BigDecimal.valueOf(loadHour)));
                                    if (value < loadHour) {
                                        count1++;
                                        person1.add(personVO);
                                    } else if (value >= loadHour && value <= loadHour2) {
                                        count2++;
                                        person2.add(personVO);
                                    } else if (value > loadHour2) {
                                        count3++;
                                        person3.add(personVO);
                                    }
                                }
                                Collections.sort(person1, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                                Collections.sort(person2, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                                Collections.sort(person3, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                                BusyAndIdleSubListVO subVO1 = new BusyAndIdleSubListVO();
                                subVO1.setType(1);
                                subVO1.setCount(count1);
                                subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(countAll), 2));
                                subVO1.setPerson(person1);
                                subList.add(subVO1);
                                BusyAndIdleSubListVO subVO2 = new BusyAndIdleSubListVO();
                                subVO2.setType(2);
                                subVO2.setCount(count2);
                                subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(countAll), 2));
                                subVO2.setPerson(person2);
                                subList.add(subVO2);
                                BusyAndIdleSubListVO subVO3 = new BusyAndIdleSubListVO();
                                subVO3.setType(3);
                                subVO3.setCount(count3);
                                subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(countAll), 2));
                                subVO3.setPerson(person3);
                                subList.add(subVO3);

                            } else {
                                //前端要求都赋值为0，才能正常显示！！
                                BusyAndIdleSubListVO subVO1 = new BusyAndIdleSubListVO();
                                subVO1.setType(1);
                                subVO1.setCount(0);
                                subVO1.setRatio(BigDecimal.valueOf(0));
                                subVO1.setPerson(new ArrayList<>());
                                subList.add(subVO1);
                                BusyAndIdleSubListVO subVO2 = new BusyAndIdleSubListVO();
                                subVO2.setType(2);
                                subVO2.setCount(0);
                                subVO2.setRatio(BigDecimal.valueOf(0));
                                subVO2.setPerson(new ArrayList<>());
                                subList.add(subVO2);
                                BusyAndIdleSubListVO subVO3 = new BusyAndIdleSubListVO();
                                subVO3.setType(3);
                                subVO3.setCount(0);
                                subVO3.setRatio(BigDecimal.valueOf(0));
                                subVO3.setPerson(new ArrayList<>());
                                subList.add(subVO3);
                            }
                            vo.setDetail(subList);
                            returnListJob.add(vo);
                        }

                        // 保存数据
                        StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                        record.setIndicatorDate(date);
                        record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST_MINOR.getCode());
                        record.setDataSecondaryClassification(Integer.parseInt(userTypeStrJob));
                        record.setHistoryData(JSON.toJSONString(returnListJob));
                        historyRecordMapper.insertSelective(record);
                    }
                }
            } else {
                // 非定时任务执行
                List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHoursMinor(startTime,
                        endTime, userTypeStr, CommonConstant.ROLE_ORDER_STATIC, userExcludeList);
                Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));

                for (TeamMinorInfo teamInfo : teamList) {
                    BusyAndIdleListVO vo = new BusyAndIdleListVO();
                    vo.setTeam(teamInfo.getTeamName());
                    vo.setTeamId(teamInfo.getTeamId());
                    List<BusyAndIdleResponse> list = map.get(teamInfo.getTeamName());
                    List<BusyAndIdleSubListVO> subList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(list)) {
                        // 该战队总人数
                        int countAll = list.size();
                        int count1 = 0;//空闲
                        List<BusyAndIdlePersonListVO> person1 = new ArrayList<>();
                        int count2 = 0;//均衡
                        List<BusyAndIdlePersonListVO> person2 = new ArrayList<>();
                        int count3 = 0;//忙碌
                        List<BusyAndIdlePersonListVO> person3 = new ArrayList<>();

                        if (currentDate.getDayOfMonth() <= 7) {
                            // 如果是月初的7天内，计算上个月的第一天和最后一天
                            msg = "上月法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                        } else {
                            msg = "本月至周报推送前一天，法定工作日" + workDays + "天，负荷工时" + loadHour + "h";
                        }
                        vo.setMsg(msg);
                        for (BusyAndIdleResponse response : list) {

                            BigDecimal hours = response.getHours();
                            double value = hours.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            BusyAndIdlePersonListVO personVO = new BusyAndIdlePersonListVO();
                            personVO.setName(response.getUserName());
                            personVO.setRealHour(hours);
                            // 差距=实际-测算标准负荷工时
                            personVO.setGap(hours.subtract(BigDecimal.valueOf(loadHour)));
                            if (value < loadHour) {
                                count1++;
                                person1.add(personVO);
                            } else if (value >= loadHour && value <= loadHour2) {
                                count2++;
                                person2.add(personVO);
                            } else if (value > loadHour2) {
                                count3++;
                                person3.add(personVO);
                            }
                        }
                        Collections.sort(person1, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                        Collections.sort(person2, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                        Collections.sort(person3, Comparator.comparing(BusyAndIdlePersonListVO::getRealHour).reversed());
                        BusyAndIdleSubListVO subVO1 = new BusyAndIdleSubListVO();
                        subVO1.setType(1);
                        subVO1.setCount(count1);
                        subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(countAll), 2));
                        subVO1.setPerson(person1);
                        subList.add(subVO1);
                        BusyAndIdleSubListVO subVO2 = new BusyAndIdleSubListVO();
                        subVO2.setType(2);
                        subVO2.setCount(count2);
                        subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(countAll), 2));
                        subVO2.setPerson(person2);
                        subList.add(subVO2);
                        BusyAndIdleSubListVO subVO3 = new BusyAndIdleSubListVO();
                        subVO3.setType(3);
                        subVO3.setCount(count3);
                        subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(countAll), 2));
                        subVO3.setPerson(person3);
                        subList.add(subVO3);

                    } else {
                        //前端要求都赋值为0，才能正常显示！！
                        BusyAndIdleSubListVO subVO1 = new BusyAndIdleSubListVO();
                        subVO1.setType(1);
                        subVO1.setCount(0);
                        subVO1.setRatio(BigDecimal.valueOf(0));
                        subVO1.setPerson(new ArrayList<>());
                        subList.add(subVO1);
                        BusyAndIdleSubListVO subVO2 = new BusyAndIdleSubListVO();
                        subVO2.setType(2);
                        subVO2.setCount(0);
                        subVO2.setRatio(BigDecimal.valueOf(0));
                        subVO2.setPerson(new ArrayList<>());
                        subList.add(subVO2);
                        BusyAndIdleSubListVO subVO3 = new BusyAndIdleSubListVO();
                        subVO3.setType(3);
                        subVO3.setCount(0);
                        subVO3.setRatio(BigDecimal.valueOf(0));
                        subVO3.setPerson(new ArrayList<>());
                        subList.add(subVO3);
                    }
                    vo.setDetail(subList);
                    returnList.add(vo);
                }

                // 保存数据
                StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                record.setIndicatorDate(date);
                record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_LIST_MINOR.getCode());
                record.setDataSecondaryClassification(userType);
                record.setHistoryData(JSON.toJSONString(returnList));
                historyRecordMapper.insertSelective(record);
            }

        }

        return returnList;
    }

    /**
     * 钉钉周报-工时运营-忙闲指数描述
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-忙闲指数描述
     */
    @Override
    public String busyAndIdleDescribe(String date) {
        String msg = "";
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询忙闲指数描述日期错误,{}", date, e);
        }
        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
        startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
        endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
        //截止到推送日的前一天的所有的工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        msg = String.valueOf(workDays);

        return msg;
    }

    /**
     * 钉钉周报-工时运营-忙闲指数描述
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-忙闲指数描述
     */
    @Override
    public String busyAndIdleDescribeMinor(String date) {
        String msg = "";
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询忙闲指数描述日期错误,{}", date, e);
        }
        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
        startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
        endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
        //截止到推送日的前一天的所有的工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        msg = String.valueOf(workDays);

        return msg;
    }

    @Override
    public ReportOverallDTO overall(String createdDate) {
        ReportOverallDTO overallDTO = new ReportOverallDTO();
        if (StringUtils.isBlank(createdDate)) {
            createdDate = DateUtils.getNowDate("yyyyMMdd");
        }
        String endTime = DateUtils.convertToDateTime(createdDate);
        if (StringUtils.isNotBlank(endTime)) {
            endTime = DateUtils.getLastDayEnd(endTime);
        } else {
            throw new TakException("查询日期错误！，日期为：" + createdDate);
        }
        String startTime = DateUtils.getDateTimeAgo(endTime, 30);

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(createdDate,
                HistoryDataClassificationEnum.REPORT_OVERALL.getCode(), null);
        if (historyRecord != null) {
            overallDTO = new Gson().fromJson(historyRecord.getHistoryData(), ReportOverallDTO.class);
        } else {
            log.info("-----calculate report overall task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();
            // 人员列表
            List<SysTeamUser> userList = sysTeamUserMapper.getCurrentList();
            // 事项列表
            List<CalcReportInfo> projectList = tbDyProjectMapper.getReportProjectList(startTime, endTime);

            // 赋值人员及人员上线数
            Map<String, Map<Integer, List<String>>> accountList = itBuService.calcPersonOnlineNum(overallDTO, teamList, userList, projectList);

            // 效能得分计算
            aiEvaluationService.calcOverallScore(overallDTO, accountList, createdDate);

            // 与上次周报数据做对比
            String oldDate = DateUtils.getDateAgo(createdDate, CommonConstant.INTEGER_7);
            StatisticalHistoryDataRecordPO sevenDaysAgoRecord = historyRecordMapper.selectByDateAndType(oldDate,
                    HistoryDataClassificationEnum.REPORT_OVERALL.getCode(), null);

            if (sevenDaysAgoRecord == null) {
                overallDTO.setAvgScoreTrend(0);
                overallDTO.setOnlineNumTrend(0);

            } else {
                ReportOverallDTO sevenDaysAgoOverallDTO = new Gson().fromJson(sevenDaysAgoRecord.getHistoryData(), ReportOverallDTO.class);

                double historyAvgScore = Double.parseDouble(sevenDaysAgoOverallDTO.getAvgScore());
                double avgScore = Double.parseDouble(overallDTO.getAvgScore());
                Integer historyOnlineTotalNum = sevenDaysAgoOverallDTO.getOnlineTotalNum();
                Integer onlineTotalNum = overallDTO.getOnlineTotalNum();

                overallDTO.setAvgScoreTrend(avgScore > historyAvgScore ? 1 : (avgScore < historyAvgScore ? 2 : 0));
                overallDTO.setOnlineNumTrend(onlineTotalNum > historyOnlineTotalNum ? 1 : (onlineTotalNum < historyOnlineTotalNum ? 2 : 0));
            }

            // 保存数据
            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
            record.setIndicatorDate(createdDate);
            record.setDataClassification(HistoryDataClassificationEnum.REPORT_OVERALL.getCode());
            record.setHistoryData(JSON.toJSONString(overallDTO));
            historyRecordMapper.insertSelective(record);
        }

        return overallDTO;
    }

    /**
     * 钉钉周报-工时运营-能力指数数据
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-能力指数列表
     */
    @Override
    public List<CapacityReportListVO> capacityReport(String date, Integer userType) {
        List<CapacityReportListVO> returnList = new ArrayList<>();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询能力指数列表日期错误,{}", date, e);
        }

        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        String msg = "";
        if (currentDate.getDayOfMonth() <= 7) {
            // 如果是月初的7天内，计算上个月的第一天和最后一天
            startTime = currentDate.minusMonths(1).withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX).format(timeFormatter);

        } else {
            // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
            startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
        }

        // 查询工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        // 负荷工时 (指标1)
        double loadHour = 0.0;
        loadHour = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85);

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST.getCode(), userType);

        if (historyRecord != null) {
            returnList = new Gson().fromJson(historyRecord.getHistoryData(), new TypeToken<List<CapacityReportListVO>>() {
            }.getType());
        } else {
            log.info("-----calculate capacityReport task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();

            String userTypeStr = "";
            //定时任务的list
            List<String> userTypeJobList = new ArrayList<>();
            if (userType == -1) {
                // 查所有，数据库只有0和1
            } else if (userType == -200) {
                userTypeJobList = Arrays.asList("-1", "0", "1");
            } else {
                userTypeStr = userType.toString();
            }
            if (CollectionUtil.isNotEmpty(userTypeJobList) && userType == -200) {
                //定时任务的list ，循环把三种情况插入到数据库
                for (String userTypeStrJob : userTypeJobList) {
                    List<CapacityReportListVO> returnListJob = new ArrayList<>();
                    StatisticalHistoryDataRecordPO historyRecordJob = historyRecordMapper.selectByDateAndType(date,
                            HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST.getCode(), Integer.parseInt(userTypeStrJob));
                    if (historyRecordJob != null) {
                        returnListJob = new Gson().fromJson(historyRecordJob.getHistoryData(), new TypeToken<List<CapacityReportListVO>>() {
                        }.getType());
                    } else {
                        String userTypeStrJobStr = "";
                        if ("-1".equals(userTypeStrJob)) {
                            // 查所有，数据库只有0和1
                        } else {
                            userTypeStrJobStr = userTypeStrJob;
                        }

                        List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHours(startTime,
                                endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStrJobStr);
                        if (CollectionUtil.isNotEmpty(capacityList)) {
                            //1.按照战队分组
                            Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                            for (TeamInfo teamInfo : teamList) {
                                CapacityReportListVO vo = new CapacityReportListVO();
                                List<CapacityResponse> list = capacityTeamMap.get(teamInfo.getTeamName());
                                if (CollectionUtil.isNotEmpty(list)) {
                                    // 该战队内总人数
                                    int teamPsrsonSum = (int) list.stream().map(CapacityResponse::getApplicantUserName).distinct().count();
                                    vo.setTeam(teamInfo.getTeamName());
                                    vo.setTeamId(teamInfo.getTeamId());
                                    if (currentDate.getDayOfMonth() <= 7) {
                                        // 如果是月初的7天内，计算上个月的第一天和最后一天
                                        msg = "上月法定工作日" + workDays + "天";
                                    } else {
                                        msg = "本月至周报推送前一天，法定工作日" + workDays + "天";
                                    }
                                    vo.setMsg(msg);
                                    //2.按照人员分组
                                    Map<String, List<CapacityResponse>> capacityTeamPersonMap = list.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));
                                    Map<String, String> userMap = list.stream().collect(Collectors.toMap(
                                            CapacityResponse::getApplicantUserName,
                                            CapacityResponse::getUserName, (existing, replacement) -> existing
                                    ));

                                    List<CapacityReportSubListVO> subList = new ArrayList<>();
                                    int count1 = 0;//低能力
                                    List<CapacityReportPersonListVO> person1 = new ArrayList<>();
                                    int count2 = 0;//普通能力
                                    List<CapacityReportPersonListVO> person2 = new ArrayList<>();
                                    int count3 = 0;//高能力
                                    List<CapacityReportPersonListVO> person3 = new ArrayList<>();
                                    for (Map.Entry<String, List<CapacityResponse>> entry : capacityTeamPersonMap.entrySet()) {
                                        String applicantUserName = entry.getKey();
                                        String userName = userMap.get(applicantUserName);
                                        List<CapacityResponse> personList = entry.getValue();

                                        if (CollectionUtil.isNotEmpty(personList)) {
                                            // 该人总任务数
                                            int countAll = personList.size();
                                            CapacityReportPersonListVO personVO = new CapacityReportPersonListVO();
                                            personVO.setName(userName);
                                            // 每个任务的"实际工时/标准工时"  求和
                                            double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                            // 平均值
                                            BigDecimal value = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 2);
                                            personVO.setRealHour(value);
                                            if (value.compareTo(BigDecimal.ZERO) == 0) {
                                                //实际工时为0，为0能力；不放在能力指数里，剔除，算为没上线
                                            } else {
                                                int comparisonResult = value.compareTo(new BigDecimal("1"));
                                                if (comparisonResult > 0) {
                                                    //大于1 低能力
                                                    count1++;
                                                    person1.add(personVO);
                                                } else if (comparisonResult < 0) {
                                                    //小于1 高能力
                                                    count3++;
                                                    person3.add(personVO);
                                                } else {
                                                    //等于1 普通能力
                                                    count2++;
                                                    person2.add(personVO);
                                                }
                                            }
                                        }
                                    }
                                    Collections.sort(person1, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
//                    Collections.sort(person2, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                                    Collections.sort(person3, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                                    CapacityReportSubListVO subVO1 = new CapacityReportSubListVO();
                                    subVO1.setType(1);
                                    subVO1.setCount(count1);
                                    subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(teamPsrsonSum), 2));
                                    subVO1.setPerson(person1);
                                    subList.add(subVO1);
                                    CapacityReportSubListVO subVO2 = new CapacityReportSubListVO();
                                    subVO2.setType(2);
                                    subVO2.setCount(count2);
                                    subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(teamPsrsonSum), 2));
                                    subVO2.setPerson(person2);
                                    subList.add(subVO2);
                                    CapacityReportSubListVO subVO3 = new CapacityReportSubListVO();
                                    subVO3.setType(3);
                                    subVO3.setCount(count3);
                                    subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(teamPsrsonSum), 2));
                                    subVO3.setPerson(person3);
                                    subList.add(subVO3);

                                    vo.setDetail(subList);
                                }

                                returnListJob.add(vo);
                            }

                            // 保存数据
                            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                            record.setIndicatorDate(date);
                            record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST.getCode());
                            record.setDataSecondaryClassification(Integer.parseInt(userTypeStrJob));
                            record.setHistoryData(JSON.toJSONString(returnListJob));
                            historyRecordMapper.insertSelective(record);
                        }
                    }
                }
            } else {
                // 非定时任务执行
                List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHours(startTime,
                        endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStr);
                if (CollectionUtil.isEmpty(capacityList)) {
                    return returnList;
                }
                //1.按照战队分组
                Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                for (TeamInfo teamInfo : teamList) {
                    CapacityReportListVO vo = new CapacityReportListVO();
                    List<CapacityResponse> list = capacityTeamMap.get(teamInfo.getTeamName());
                    if (CollectionUtil.isNotEmpty(list)) {
                        // 该战队内总人数
                        int teamPsrsonSum = (int) list.stream().map(CapacityResponse::getApplicantUserName).distinct().count();
                        vo.setTeam(teamInfo.getTeamName());
                        vo.setTeamId(teamInfo.getTeamId());
                        if (currentDate.getDayOfMonth() <= 7) {
                            // 如果是月初的7天内，计算上个月的第一天和最后一天
                            msg = "上月法定工作日" + workDays + "天";
                        } else {
                            msg = "本月至周报推送前一天，法定工作日" + workDays + "天";
                        }
                        vo.setMsg(msg);
                        //2.按照人员分组
                        Map<String, List<CapacityResponse>> capacityTeamPersonMap = list.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));
                        Map<String, String> userMap = list.stream().collect(Collectors.toMap(
                                CapacityResponse::getApplicantUserName,
                                CapacityResponse::getUserName, (existing, replacement) -> existing
                        ));

                        List<CapacityReportSubListVO> subList = new ArrayList<>();
                        int count1 = 0;//低能力
                        List<CapacityReportPersonListVO> person1 = new ArrayList<>();
                        int count2 = 0;//普通能力
                        List<CapacityReportPersonListVO> person2 = new ArrayList<>();
                        int count3 = 0;//高能力
                        List<CapacityReportPersonListVO> person3 = new ArrayList<>();
                        for (Map.Entry<String, List<CapacityResponse>> entry : capacityTeamPersonMap.entrySet()) {
                            String applicantUserName = entry.getKey();
                            String userName = userMap.get(applicantUserName);
                            List<CapacityResponse> personList = entry.getValue();

                            if (CollectionUtil.isNotEmpty(personList)) {
                                // 该人总任务数
                                int countAll = personList.size();
                                CapacityReportPersonListVO personVO = new CapacityReportPersonListVO();
                                personVO.setName(userName);
                                // 每个任务的"实际工时/标准工时"  求和
                                double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                // 平均值
                                BigDecimal value = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 2);
                                personVO.setRealHour(value);
                                if (value.compareTo(BigDecimal.ZERO) == 0) {
                                    //实际工时为0，为0能力；不放在能力指数里，剔除，算为没上线
                                } else {
                                    int comparisonResult = value.compareTo(new BigDecimal("1"));
                                    if (comparisonResult > 0) {
                                        //大于1 低能力
                                        count1++;
                                        person1.add(personVO);
                                    } else if (comparisonResult < 0) {
                                        //小于1 高能力
                                        count3++;
                                        person3.add(personVO);
                                    } else {
                                        //等于1 普通能力
                                        count2++;
                                        person2.add(personVO);
                                    }
                                }
                            }
                        }
                        Collections.sort(person1, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                        Collections.sort(person3, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                        CapacityReportSubListVO subVO1 = new CapacityReportSubListVO();
                        subVO1.setType(1);
                        subVO1.setCount(count1);
                        subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(teamPsrsonSum), 2));
                        subVO1.setPerson(person1);
                        subList.add(subVO1);
                        CapacityReportSubListVO subVO2 = new CapacityReportSubListVO();
                        subVO2.setType(2);
                        subVO2.setCount(count2);
                        subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(teamPsrsonSum), 2));
                        subVO2.setPerson(person2);
                        subList.add(subVO2);
                        CapacityReportSubListVO subVO3 = new CapacityReportSubListVO();
                        subVO3.setType(3);
                        subVO3.setCount(count3);
                        subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(teamPsrsonSum), 2));
                        subVO3.setPerson(person3);
                        subList.add(subVO3);

                        vo.setDetail(subList);
                    }

                    returnList.add(vo);
                }

                // 保存数据
                StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                record.setIndicatorDate(date);
                record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST.getCode());
                record.setDataSecondaryClassification(userType);
                record.setHistoryData(JSON.toJSONString(returnList));
                historyRecordMapper.insertSelective(record);
            }

        }
        return returnList;
    }

    /**
     * 钉钉周报-工时运营-能力指数数据-副版
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-能力指数列表
     */
    @Override
    public List<CapacityReportListVO> capacityReportMinor(String date, Integer userType) {
        List<CapacityReportListVO> returnList = new ArrayList<>();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询能力指数列表日期错误,{}", date, e);
        }

        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        String msg = "";
        if (currentDate.getDayOfMonth() <= 7) {
            // 如果是月初的7天内，计算上个月的第一天和最后一天
            startTime = currentDate.minusMonths(1).withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX).format(timeFormatter);

        } else {
            // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
            startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
        }

        // 查询工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        // 负荷工时 (指标1)
        double loadHour = 0.0;
        loadHour = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85);

        //副版-排除人员
        List<String> userExcludeList = Arrays.asList(ucgConfig.getExclude().split(","));

        //查询没调用过业务单元的
        List<EmptyUnitResponse> emptyUnitList = tbDyHoursMapper.selectEmptyUnit(startTime,
                endTime, CommonConstant.ROLE_ORDER_STATIC, userExcludeList);
        Map<String, String> emptyUnitListMap = emptyUnitList.stream()
                .collect(Collectors.groupingBy(EmptyUnitResponse::getTeam,
                        Collectors.mapping(EmptyUnitResponse::getUserName, Collectors.joining("、"))));

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date,
                HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST_MINOR.getCode(), userType);

        if (historyRecord != null) {
            returnList = new Gson().fromJson(historyRecord.getHistoryData(), new TypeToken<List<CapacityReportListVO>>() {
            }.getType());
        } else {
            log.info("-----calculate capacityReport task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
//            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();
            List<TeamMinorInfo> teamList = teamInfoMapper.getReportMinorTeam(CommonConstant.ROLE_ORDER_STATIC, userExcludeList);

            String userTypeStr = "";
            //定时任务的list
            List<String> userTypeJobList = new ArrayList<>();
            if (userType == -1) {
                // 查所有，数据库只有0和1
            } else if (userType == -200) {
                userTypeJobList = Arrays.asList("-1", "0", "1");
            } else {
                userTypeStr = userType.toString();
            }
            if (CollectionUtil.isNotEmpty(userTypeJobList) && userType == -200) {
                //定时任务的list ，循环把三种情况插入到数据库
                for (String userTypeStrJob : userTypeJobList) {
                    List<CapacityReportListVO> returnListJob = new ArrayList<>();
                    StatisticalHistoryDataRecordPO historyRecordJob = historyRecordMapper.selectByDateAndType(date,
                            HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST_MINOR.getCode(), Integer.parseInt(userTypeStrJob));
                    if (historyRecordJob != null) {
                        returnListJob = new Gson().fromJson(historyRecordJob.getHistoryData(), new TypeToken<List<CapacityReportListVO>>() {
                        }.getType());
                    } else {
                        String userTypeStrJobStr = "";
                        if ("-1".equals(userTypeStrJob)) {
                            // 查所有，数据库只有0和1
                        } else {
                            userTypeStrJobStr = userTypeStrJob;
                        }

                        List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHoursMinor(startTime,
                                endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStrJobStr, userExcludeList);
                        if (CollectionUtil.isNotEmpty(capacityList)) {
                            //1.按照战队分组
                            Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                            for (TeamMinorInfo teamInfo : teamList) {
                                CapacityReportListVO vo = new CapacityReportListVO();
                                vo.setTeam(teamInfo.getTeamName());
                                vo.setTeamId(teamInfo.getTeamId());
                                String emptyUnitUser = emptyUnitListMap.get(teamInfo.getTeamName());
                                if (StringUtils.isNotEmpty(emptyUnitUser)) {
                                    vo.setEmptyMsg(emptyUnitUser);
                                }
                                List<CapacityResponse> list = capacityTeamMap.get(teamInfo.getTeamName());
                                List<CapacityReportSubListVO> subList = new ArrayList<>();
                                if (CollectionUtil.isNotEmpty(list)) {
                                    // 该战队内总人数
                                    int teamPsrsonSum = (int) list.stream().map(CapacityResponse::getApplicantUserName).distinct().count();
                                    if (currentDate.getDayOfMonth() <= 7) {
                                        // 如果是月初的7天内，计算上个月的第一天和最后一天
                                        msg = "上月法定工作日" + workDays + "天";
                                    } else {
                                        msg = "本月至周报推送前一天，法定工作日" + workDays + "天";
                                    }
                                    vo.setMsg(msg);
                                    //2.按照人员分组
                                    Map<String, List<CapacityResponse>> capacityTeamPersonMap = list.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));
                                    Map<String, String> userMap = list.stream().collect(Collectors.toMap(
                                            CapacityResponse::getApplicantUserName,
                                            CapacityResponse::getUserName, (existing, replacement) -> existing
                                    ));

                                    int count1 = 0;//低能力
                                    List<CapacityReportPersonListVO> person1 = new ArrayList<>();
                                    int count2 = 0;//普通能力
                                    List<CapacityReportPersonListVO> person2 = new ArrayList<>();
                                    int count3 = 0;//高能力
                                    List<CapacityReportPersonListVO> person3 = new ArrayList<>();
                                    for (Map.Entry<String, List<CapacityResponse>> entry : capacityTeamPersonMap.entrySet()) {
                                        String applicantUserName = entry.getKey();
                                        String userName = userMap.get(applicantUserName);
                                        List<CapacityResponse> personList = entry.getValue();

                                        if (CollectionUtil.isNotEmpty(personList)) {
                                            // 该人总任务数
                                            int countAll = personList.size();
                                            CapacityReportPersonListVO personVO = new CapacityReportPersonListVO();
                                            personVO.setName(userName);
                                            // 每个任务的"实际工时/标准工时"  求和
                                            double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                            // 平均值
                                            BigDecimal value = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 2);
                                            personVO.setRealHour(value);
                                            if (value.compareTo(BigDecimal.ZERO) == 0) {
                                                //实际工时为0，为0能力；不放在能力指数里，剔除，算为没上线
                                            } else {
                                                int comparisonResult = value.compareTo(new BigDecimal("1"));
                                                if (comparisonResult > 0) {
                                                    //大于1 低能力
                                                    count1++;
                                                    person1.add(personVO);
                                                } else if (comparisonResult < 0) {
                                                    //小于1 高能力
                                                    count3++;
                                                    person3.add(personVO);
                                                } else {
                                                    //等于1 普通能力
                                                    count2++;
                                                    person2.add(personVO);
                                                }
                                            }
                                        }
                                    }
                                    Collections.sort(person1, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                                    Collections.sort(person3, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                                    CapacityReportSubListVO subVO1 = new CapacityReportSubListVO();
                                    subVO1.setType(1);
                                    subVO1.setCount(count1);
                                    subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(teamPsrsonSum), 2));
                                    subVO1.setPerson(person1);
                                    subList.add(subVO1);
                                    CapacityReportSubListVO subVO2 = new CapacityReportSubListVO();
                                    subVO2.setType(2);
                                    subVO2.setCount(count2);
                                    subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(teamPsrsonSum), 2));
                                    subVO2.setPerson(person2);
                                    subList.add(subVO2);
                                    CapacityReportSubListVO subVO3 = new CapacityReportSubListVO();
                                    subVO3.setType(3);
                                    subVO3.setCount(count3);
                                    subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(teamPsrsonSum), 2));
                                    subVO3.setPerson(person3);
                                    subList.add(subVO3);

                                }
                                vo.setDetail(subList);
                                returnListJob.add(vo);
                            }

                            // 保存数据
                            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                            record.setIndicatorDate(date);
                            record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST_MINOR.getCode());
                            record.setDataSecondaryClassification(Integer.parseInt(userTypeStrJob));
                            record.setHistoryData(JSON.toJSONString(returnListJob));
                            historyRecordMapper.insertSelective(record);
                        }
                    }
                }
            } else {
                // 非定时任务执行
                List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHoursMinor(startTime,
                        endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStr, userExcludeList);
                if (CollectionUtil.isEmpty(capacityList)) {
                    return returnList;
                }
                //1.按照战队分组
                Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                for (TeamMinorInfo teamInfo : teamList) {
                    CapacityReportListVO vo = new CapacityReportListVO();
                    vo.setTeam(teamInfo.getTeamName());
                    vo.setTeamId(teamInfo.getTeamId());
                    String emptyUnitUser = emptyUnitListMap.get(teamInfo.getTeamName());
                    if (StringUtils.isNotEmpty(emptyUnitUser)) {
                        vo.setEmptyMsg(emptyUnitUser);
                    }
                    List<CapacityResponse> list = capacityTeamMap.get(teamInfo.getTeamName());
                    List<CapacityReportSubListVO> subList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(list)) {
                        // 该战队内总人数
                        int teamPsrsonSum = (int) list.stream().map(CapacityResponse::getApplicantUserName).distinct().count();
                        if (currentDate.getDayOfMonth() <= 7) {
                            // 如果是月初的7天内，计算上个月的第一天和最后一天
                            msg = "上月法定工作日" + workDays + "天";
                        } else {
                            msg = "本月至周报推送前一天，法定工作日" + workDays + "天";
                        }
                        vo.setMsg(msg);
                        //2.按照人员分组
                        Map<String, List<CapacityResponse>> capacityTeamPersonMap = list.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));
                        Map<String, String> userMap = list.stream().collect(Collectors.toMap(
                                CapacityResponse::getApplicantUserName,
                                CapacityResponse::getUserName, (existing, replacement) -> existing
                        ));

                        int count1 = 0;//低能力
                        List<CapacityReportPersonListVO> person1 = new ArrayList<>();
                        int count2 = 0;//普通能力
                        List<CapacityReportPersonListVO> person2 = new ArrayList<>();
                        int count3 = 0;//高能力
                        List<CapacityReportPersonListVO> person3 = new ArrayList<>();
                        for (Map.Entry<String, List<CapacityResponse>> entry : capacityTeamPersonMap.entrySet()) {
                            String applicantUserName = entry.getKey();
                            String userName = userMap.get(applicantUserName);
                            List<CapacityResponse> personList = entry.getValue();

                            if (CollectionUtil.isNotEmpty(personList)) {
                                // 该人总任务数
                                int countAll = personList.size();
                                CapacityReportPersonListVO personVO = new CapacityReportPersonListVO();
                                personVO.setName(userName);
                                // 每个任务的"实际工时/标准工时"  求和
                                double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                // 平均值
                                BigDecimal value = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 2);
                                personVO.setRealHour(value);
                                if (value.compareTo(BigDecimal.ZERO) == 0) {
                                    //实际工时为0，为0能力；不放在能力指数里，剔除，算为没上线
                                } else {
                                    int comparisonResult = value.compareTo(new BigDecimal("1"));
                                    if (comparisonResult > 0) {
                                        //大于1 低能力
                                        count1++;
                                        person1.add(personVO);
                                    } else if (comparisonResult < 0) {
                                        //小于1 高能力
                                        count3++;
                                        person3.add(personVO);
                                    } else {
                                        //等于1 普通能力
                                        count2++;
                                        person2.add(personVO);
                                    }
                                }
                            }
                        }
                        Collections.sort(person1, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                        Collections.sort(person3, Comparator.comparing(CapacityReportPersonListVO::getRealHour).reversed());
                        CapacityReportSubListVO subVO1 = new CapacityReportSubListVO();
                        subVO1.setType(1);
                        subVO1.setCount(count1);
                        subVO1.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count1), BigDecimal.valueOf(teamPsrsonSum), 2));
                        subVO1.setPerson(person1);
                        subList.add(subVO1);
                        CapacityReportSubListVO subVO2 = new CapacityReportSubListVO();
                        subVO2.setType(2);
                        subVO2.setCount(count2);
                        subVO2.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count2), BigDecimal.valueOf(teamPsrsonSum), 2));
                        subVO2.setPerson(person2);
                        subList.add(subVO2);
                        CapacityReportSubListVO subVO3 = new CapacityReportSubListVO();
                        subVO3.setType(3);
                        subVO3.setCount(count3);
                        subVO3.setRatio(DecimalUtils.divideZeroByScale(BigDecimal.valueOf(count3), BigDecimal.valueOf(teamPsrsonSum), 2));
                        subVO3.setPerson(person3);
                        subList.add(subVO3);

                    }
                    vo.setDetail(subList);
                    returnList.add(vo);
                }

                // 保存数据
                StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                record.setIndicatorDate(date);
                record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_CAPACITY_LIST_MINOR.getCode());
                record.setDataSecondaryClassification(userType);
                record.setHistoryData(JSON.toJSONString(returnList));
                historyRecordMapper.insertSelective(record);
            }

        }
        return returnList;
    }

    /**
     * 钉钉周报-工时运营-工时矩阵数据
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-工时矩阵列表
     */
    @Override
    public List<BusyAndIdleMatrixVO> busyAndIdleMatrix(String date, Integer userType) {
        List<BusyAndIdleMatrixVO> returnList = new ArrayList<>();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询工时矩阵日期错误,{}", date, e);
        }

        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        String msg = "";
        if (currentDate.getDayOfMonth() <= 7) {
            // 如果是月初的7天内，计算上个月的第一天和最后一天
            startTime = currentDate.minusMonths(1).withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX).format(timeFormatter);

        } else {
            // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
            startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
        }

        // 查询工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        // 负荷工时 (指标1)
        double loadHour = 0.0;
        loadHour = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85);

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date, HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX.getCode(), userType);

        if (historyRecord != null) {
            returnList = new Gson().fromJson(historyRecord.getHistoryData(), new TypeToken<List<BusyAndIdleMatrixVO>>() {
            }.getType());
        } else {
            log.info("-----calculate busyAndIdleMatrix task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();


            String userTypeStr = "";
            //定时任务的list
            List<String> userTypeJobList = new ArrayList<>();
            if (userType == -1) {
                // 查所有，数据库只有0和1
            } else if (userType == -200) {
                userTypeJobList = Arrays.asList("-1", "0", "1");
            } else {
                userTypeStr = userType.toString();
            }
            if (CollectionUtil.isNotEmpty(userTypeJobList) && userType == -200) {
                //定时任务的list ，循环把三种情况插入到数据库
                for (String userTypeStrJob : userTypeJobList) {
                    List<BusyAndIdleMatrixVO> returnListJob = new ArrayList<>();
                    StatisticalHistoryDataRecordPO historyRecordJob = historyRecordMapper.selectByDateAndType(date,
                            HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX.getCode(), Integer.parseInt(userTypeStrJob));
                    if (historyRecordJob != null) {
                        returnListJob = new Gson().fromJson(historyRecordJob.getHistoryData(), new TypeToken<List<BusyAndIdleMatrixVO>>() {
                        }.getType());
                    } else {
                        String userTypeStrJobStr = "";
                        if ("-1".equals(userTypeStrJob)) {
                            // 查所有，数据库只有0和1
                        } else {
                            userTypeStrJobStr = userTypeStrJob;
                        }

                        // 忙闲指数
                        List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHoursForMatrix(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStrJobStr);
                        Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));
                        // 能力指数
                        List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHoursForMatrix(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStrJobStr);
//                        if(CollectionUtil.isEmpty(capacityList)){
//                            return returnList;
//                        }
                        // 能力指数 1.按照战队分组
                        Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                        for (TeamInfo teamInfo : teamList) {
                            BusyAndIdleMatrixVO vo = new BusyAndIdleMatrixVO();
                            // 获取指定战队的 忙闲指数数据
                            List<BusyAndIdleResponse> busyIdleTeamList = map.get(teamInfo.getTeamName());
                            // 获取指定战队的 能力指数数据
                            List<CapacityResponse> capacityTeamList = capacityTeamMap.get(teamInfo.getTeamName());

                            if (CollectionUtil.isNotEmpty(busyIdleTeamList) && CollectionUtil.isNotEmpty(capacityTeamList)) {
                                vo.setTeam(teamInfo.getTeamName());
                                vo.setTeamId(teamInfo.getTeamId());
                                List<BusyAndIdleMatrixSubVO> subList = new ArrayList<>();

                                // 能力指数 2.按照人员分组
                                Map<String, List<CapacityResponse>> capacityTeamPersonMap = capacityTeamList.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));

                                for (BusyAndIdleResponse response : busyIdleTeamList) {
                                    double loadHourEach = 0.0;
                                    BusyAndIdleMatrixSubVO matrixSubVO = new BusyAndIdleMatrixSubVO();
                                    String userName = response.getUserName();
                                    String applicantUserName = response.getApplicantUserName();
                                    matrixSubVO.setName(userName);
                                    matrixSubVO.setLoginAccount(response.getApplicantUserName());
                                    // 忙闲：实际工时=负荷工时；能力：标准工时=实际工时
                                    BigDecimal hours = response.getHours();//实际工时
                                    BigDecimal standardTime = response.getStandardTime();//标准工时
                                    //X 轴 忙闲
//                        // 产品逻辑
//                        matrixSubVO.setBusy(hours.subtract(BigDecimal.valueOf(loadHour)));
                                    // 判断 此人的拉入时间, 调整标准
                                    // 如统计月份有新拉入人员清单的人员，包括自有及三方。则根据拉入日期（不算日期当天），计算周报推送日期的工作天数，作为忙闲的标准
                                    if (response.getCreatedDate() != null &&
                                            response.getCreatedDate().after(DateUtils.parseDate(startTime, DateUtils.FORMAT_DATE_TIME)) &&
                                            response.getCreatedDate().before(DateUtils.parseDate(endTime, DateUtils.FORMAT_DATE_TIME))) {
                                        int workDaysChange = sysCalendarService.getWorkDays(DateUtils.convertToDate(DateUtils.formatDate(response.getCreatedDate(), DateUtils.FORMAT_DATE_TIME)), DateUtils.convertToDate(endTime));
//                                        loadHour = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85);
                                        loadHourEach = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85);
                                    } else {
                                        loadHourEach = loadHour;
                                    }

                                    // 前端逻辑
                                    matrixSubVO.setBusy(DecimalUtils.divideZeroByScale(hours, BigDecimal.valueOf(loadHourEach), 2));
                                    //Y 轴 能力
                                    //在忙闲的循环中，获取此人的能力指数
                                    List<CapacityResponse> personList = capacityTeamPersonMap.get(applicantUserName);
                                    if (CollectionUtil.isNotEmpty(personList)) {
                                        // 该人总任务数
                                        int countAll = personList.size();
                                        // 1.每个任务的"实际工时/标准工时"  求和 sum
                                        double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                        // 2.平均值 = （sum/总任务数）的倒数 ： 因为前端画图的逻辑是 （标准工时/实际工时）
                                        BigDecimal avg = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(1), (DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 6)), 2);

                                        // Y轴计算公式 -(A-1) = 1-A ; A为每个人所有任务的"实际工时/标准工时"的平均值
//                            // 产品逻辑
//                            matrixSubVO.setCapacity(new BigDecimal("1").subtract(avg));
                                        // 前端逻辑
                                        matrixSubVO.setCapacity(avg);
                                        // 此处为解析 高、低、普通能力的逻辑 (产品逻辑)
//                            int comparisonResult = avg.compareTo(new BigDecimal("1"));
//                            if (comparisonResult > 0) {
//                                //大于1 低能力 ; 低能力在Y轴的负轴上，所以加"-"
//                            } else if (comparisonResult < 0) {
//                                //小于1 高能力
//                                matrixSubVO.setCapacity(avg);
//                            } else {
//                                //等于1 普通能力
//                                matrixSubVO.setCapacity(avg);
//                            }
                                    } else {
                                        log.info("忙闲指数中存在的用户-{}-能力指数不存在", userName);
                                        matrixSubVO.setCapacity(BigDecimal.ZERO);
                                    }

                                    if (hours.compareTo(BigDecimal.ZERO) == 0 || standardTime.compareTo(BigDecimal.ZERO) == 0) {
                                        //不放在象限里的情况
                                        //1.实际工时为0，为0能力；2.标准工时为0，也不放在象限内； 不放在象限里，剔除，算为没上线，没有表现，评价不出来
                                    } else {
                                        subList.add(matrixSubVO);
                                    }
                                }
                                vo.setDetail(subList);
                            }
                            returnListJob.add(vo);
                        }

                        // 保存数据
                        StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                        record.setIndicatorDate(date);
                        record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX.getCode());
                        record.setDataSecondaryClassification(Integer.parseInt(userTypeStrJob));
                        record.setHistoryData(JSON.toJSONString(returnListJob));
                        historyRecordMapper.insertSelective(record);


                    }
                }
            } else {
                // 非定时任务执行
                // 忙闲指数
                List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHoursForMatrix(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStr);
                Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));
                // 能力指数
                List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHoursForMatrix(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStr);
                if (CollectionUtil.isEmpty(capacityList)) {
                    return returnList;
                }
                // 能力指数 1.按照战队分组
                Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                for (TeamInfo teamInfo : teamList) {
                    BusyAndIdleMatrixVO vo = new BusyAndIdleMatrixVO();
                    // 获取指定战队的 忙闲指数数据
                    List<BusyAndIdleResponse> busyIdleTeamList = map.get(teamInfo.getTeamName());
                    // 获取指定战队的 能力指数数据
                    List<CapacityResponse> capacityTeamList = capacityTeamMap.get(teamInfo.getTeamName());

                    if (CollectionUtil.isNotEmpty(busyIdleTeamList) && CollectionUtil.isNotEmpty(capacityTeamList)) {
                        vo.setTeam(teamInfo.getTeamName());
                        vo.setTeamId(teamInfo.getTeamId());
                        List<BusyAndIdleMatrixSubVO> subList = new ArrayList<>();

                        // 能力指数 2.按照人员分组
                        Map<String, List<CapacityResponse>> capacityTeamPersonMap = capacityTeamList.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));

                        for (BusyAndIdleResponse response : busyIdleTeamList) {
                            double loadHourEach = 0.0;
                            BusyAndIdleMatrixSubVO matrixSubVO = new BusyAndIdleMatrixSubVO();
                            String userName = response.getUserName();
                            String applicantUserName = response.getApplicantUserName();
                            matrixSubVO.setName(userName);
                            matrixSubVO.setLoginAccount(response.getApplicantUserName());
                            // 忙闲：实际工时=负荷工时；能力：标准工时=实际工时
                            BigDecimal hours = response.getHours();//实际工时
                            BigDecimal standardTime = response.getStandardTime();//标准工时
                            //X 轴 忙闲
//                        // 产品逻辑
//                        matrixSubVO.setBusy(hours.subtract(BigDecimal.valueOf(loadHour)));
                            // 判断 此人的拉入时间, 调整标准
                            // 如统计月份有新拉入人员清单的人员，包括自有及三方。则根据拉入日期（不算日期当天），计算周报推送日期的工作天数，作为忙闲的标准
                            if (response.getCreatedDate() != null &&
                                    response.getCreatedDate().after(DateUtils.parseDate(startTime, DateUtils.FORMAT_DATE_TIME)) &&
                                    response.getCreatedDate().before(DateUtils.parseDate(endTime, DateUtils.FORMAT_DATE_TIME))) {
                                int workDaysChange = sysCalendarService.getWorkDays(DateUtils.convertToDate(DateUtils.formatDate(response.getCreatedDate(), DateUtils.FORMAT_DATE_TIME)), DateUtils.convertToDate(endTime));
//                                loadHour = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85);
                                loadHourEach = DecimalUtils.formatDoubleValue(workDaysChange * 8 * 0.85);
                            } else {
                                loadHourEach = loadHour;
                            }
                            // 前端逻辑
                            matrixSubVO.setBusy(DecimalUtils.divideZeroByScale(hours, BigDecimal.valueOf(loadHourEach), 2));
                            //Y 轴 能力
                            //在忙闲的循环中，获取此人的能力指数
                            List<CapacityResponse> personList = capacityTeamPersonMap.get(applicantUserName);
                            if (CollectionUtil.isNotEmpty(personList)) {
                                // 该人总任务数
                                int countAll = personList.size();
                                // 1.每个任务的"实际工时/标准工时"  求和 sum
                                double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                // 2.平均值 = （sum/总任务数）的倒数 ： 因为前端画图的逻辑是 （标准工时/实际工时）
                                BigDecimal avg = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(1), (DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 6)), 2);

                                // Y轴计算公式 -(A-1) = 1-A ; A为每个人所有任务的"实际工时/标准工时"的平均值
//                            // 产品逻辑
//                            matrixSubVO.setCapacity(new BigDecimal("1").subtract(avg));
                                // 前端逻辑
                                matrixSubVO.setCapacity(avg);
                                // 此处为解析 高、低、普通能力的逻辑 (产品逻辑)
//                            int comparisonResult = avg.compareTo(new BigDecimal("1"));
//                            if (comparisonResult > 0) {
//                                //大于1 低能力 ; 低能力在Y轴的负轴上，所以加"-"
//                            } else if (comparisonResult < 0) {
//                                //小于1 高能力
//                                matrixSubVO.setCapacity(avg);
//                            } else {
//                                //等于1 普通能力
//                                matrixSubVO.setCapacity(avg);
//                            }
                            } else {
                                log.info("忙闲指数中存在的用户-{}-能力指数不存在", userName);
                                matrixSubVO.setCapacity(BigDecimal.ZERO);
                            }

                            if (hours.compareTo(BigDecimal.ZERO) == 0 || standardTime.compareTo(BigDecimal.ZERO) == 0) {
                                //不放在象限里的情况
                                //1.实际工时为0，为0能力；2.标准工时为0，也不放在象限内； 不放在象限里，剔除，算为没上线，没有表现，评价不出来
                            } else {
                                subList.add(matrixSubVO);
                            }
                        }
                        vo.setDetail(subList);
                    }
                    returnList.add(vo);
                }

                // 保存数据
                StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                record.setIndicatorDate(date);
                record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX.getCode());
                record.setDataSecondaryClassification(userType);
                record.setHistoryData(JSON.toJSONString(returnList));
                historyRecordMapper.insertSelective(record);
            }

        }
        return returnList;
    }

    /**
     * 钉钉周报-工时运营-工时矩阵数据-副版
     *
     * @param date 计算日期 格式 20240523
     * @return 工时运营-工时矩阵列表
     */
    @Override
    public List<BusyAndIdleMatrixVO> busyAndIdleMatrixMinor(String date, Integer userType) {
        List<BusyAndIdleMatrixVO> returnList = new ArrayList<>();
        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }

        // 1. 获取分析日期入参
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate currentDate;
        try {
            currentDate = LocalDate.parse(date, inputFormatter);
        } catch (Exception e) {
            currentDate = LocalDate.now();
            log.error("查询工时矩阵日期错误,{}", date, e);
        }

        String startTime;
        String endTime;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_TIME);
        String msg = "";
        if (currentDate.getDayOfMonth() <= 7) {
            // 如果是月初的7天内，计算上个月的第一天和最后一天
            startTime = currentDate.minusMonths(1).withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX).format(timeFormatter);

        } else {
            // 如果不是月初的7天内，计算本月的第一天开始时刻和前一天的最后一刻
            startTime = currentDate.withDayOfMonth(1).atStartOfDay().format(timeFormatter);
            endTime = currentDate.minusDays(1).atTime(LocalTime.MAX).format(timeFormatter);
        }

        // 查询工作日
        int workDays = sysCalendarService.getWorkDays(DateUtils.convertToDate(startTime), DateUtils.convertToDate(endTime));
        // 负荷工时 (指标1)
        double loadHour = 0.0;
        loadHour = DecimalUtils.formatDoubleValue(workDays * 8 * 0.85);

        //副版-排除人员
        List<String> userExcludeList = Arrays.asList(ucgConfig.getExclude().split(","));

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date, HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX_MINOR.getCode(), userType);

        if (historyRecord != null) {
            returnList = new Gson().fromJson(historyRecord.getHistoryData(), new TypeToken<List<BusyAndIdleMatrixVO>>() {
            }.getType());
        } else {
            log.info("-----calculate busyAndIdleMatrix task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
//            List<TeamInfo> teamList = teamInfoMapper.getReportTeam();
            List<TeamMinorInfo> teamList = teamInfoMapper.getReportMinorTeam(CommonConstant.ROLE_ORDER_STATIC, userExcludeList);

            String userTypeStr = "";
            //定时任务的list
            List<String> userTypeJobList = new ArrayList<>();
            if (userType == -1) {
                // 查所有，数据库只有0和1
            } else if (userType == -200) {
                userTypeJobList = Arrays.asList("-1", "0", "1");
            } else {
                userTypeStr = userType.toString();
            }
            if (CollectionUtil.isNotEmpty(userTypeJobList) && userType == -200) {
                //定时任务的list ，循环把三种情况插入到数据库
                for (String userTypeStrJob : userTypeJobList) {
                    List<BusyAndIdleMatrixVO> returnListJob = new ArrayList<>();
                    StatisticalHistoryDataRecordPO historyRecordJob = historyRecordMapper.selectByDateAndType(date, HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX_MINOR.getCode(), Integer.parseInt(userTypeStrJob));
                    if (historyRecordJob != null) {
                        returnListJob = new Gson().fromJson(historyRecordJob.getHistoryData(), new TypeToken<List<BusyAndIdleMatrixVO>>() {
                        }.getType());
                    } else {
                        String userTypeStrJobStr = "";
                        if ("-1".equals(userTypeStrJob)) {
                            // 查所有，数据库只有0和1
                        } else {
                            userTypeStrJobStr = userTypeStrJob;
                        }

                        // 忙闲指数
                        List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHoursForMatrixMinor(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStrJobStr, userExcludeList);
                        Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));
                        // 能力指数
                        List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHoursForMatrixMinor(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStrJobStr, userExcludeList);
//                        if(CollectionUtil.isEmpty(capacityList)){
//                            return returnList;
//                        }
                        // 能力指数 1.按照战队分组
                        Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                        for (TeamMinorInfo teamInfo : teamList) {
                            BusyAndIdleMatrixVO vo = new BusyAndIdleMatrixVO();
                            vo.setTeam(teamInfo.getTeamName());
                            vo.setTeamId(teamInfo.getTeamId());
                            // 获取指定战队的 忙闲指数数据
                            List<BusyAndIdleResponse> busyIdleTeamList = map.get(teamInfo.getTeamName());
                            // 获取指定战队的 能力指数数据
                            List<CapacityResponse> capacityTeamList = capacityTeamMap.get(teamInfo.getTeamName());

                            List<BusyAndIdleMatrixSubVO> subList = new ArrayList<>();
                            if (CollectionUtil.isNotEmpty(busyIdleTeamList) && CollectionUtil.isNotEmpty(capacityTeamList)) {
//                                vo.setTeam(teamInfo.getTeamName());
//                                vo.setTeamId(teamInfo.getTeamId());

                                // 能力指数 2.按照人员分组
                                Map<String, List<CapacityResponse>> capacityTeamPersonMap = capacityTeamList.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));

                                for (BusyAndIdleResponse response : busyIdleTeamList) {
                                    BusyAndIdleMatrixSubVO matrixSubVO = new BusyAndIdleMatrixSubVO();
                                    String userName = response.getUserName();
                                    String applicantUserName = response.getApplicantUserName();
                                    matrixSubVO.setName(userName);
                                    matrixSubVO.setLoginAccount(response.getApplicantUserName());
                                    // 忙闲：实际工时=负荷工时；能力：标准工时=实际工时
                                    BigDecimal hours = response.getHours();//实际工时
                                    BigDecimal standardTime = response.getStandardTime();//标准工时
                                    //X 轴 忙闲
//                        // 产品逻辑
//                        matrixSubVO.setBusy(hours.subtract(BigDecimal.valueOf(loadHour)));
                                    // 前端逻辑
                                    matrixSubVO.setBusy(DecimalUtils.divideZeroByScale(hours, BigDecimal.valueOf(loadHour), 2));
                                    //Y 轴 能力
                                    //在忙闲的循环中，获取此人的能力指数
                                    List<CapacityResponse> personList = capacityTeamPersonMap.get(applicantUserName);
                                    if (CollectionUtil.isNotEmpty(personList)) {
                                        // 该人总任务数
                                        int countAll = personList.size();
                                        // 1.每个任务的"实际工时/标准工时"  求和 sum
                                        double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                        // 2.平均值 = （sum/总任务数）的倒数 ： 因为前端画图的逻辑是 （标准工时/实际工时）
                                        BigDecimal avg = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(1), (DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 6)), 2);

                                        // Y轴计算公式 -(A-1) = 1-A ; A为每个人所有任务的"实际工时/标准工时"的平均值
//                            // 产品逻辑
//                            matrixSubVO.setCapacity(new BigDecimal("1").subtract(avg));
                                        // 前端逻辑
                                        matrixSubVO.setCapacity(avg);
                                        // 此处为解析 高、低、普通能力的逻辑 (产品逻辑)
//                            int comparisonResult = avg.compareTo(new BigDecimal("1"));
//                            if (comparisonResult > 0) {
//                                //大于1 低能力 ; 低能力在Y轴的负轴上，所以加"-"
//                            } else if (comparisonResult < 0) {
//                                //小于1 高能力
//                                matrixSubVO.setCapacity(avg);
//                            } else {
//                                //等于1 普通能力
//                                matrixSubVO.setCapacity(avg);
//                            }
                                    } else {
                                        log.info("忙闲指数中存在的用户-{}-能力指数不存在", userName);
                                        matrixSubVO.setCapacity(BigDecimal.ZERO);
                                    }

                                    if (hours.compareTo(BigDecimal.ZERO) == 0 || standardTime.compareTo(BigDecimal.ZERO) == 0) {
                                        //不放在象限里的情况
                                        //1.实际工时为0，为0能力；2.标准工时为0，也不放在象限内； 不放在象限里，剔除，算为没上线，没有表现，评价不出来
                                    } else {
                                        subList.add(matrixSubVO);
                                    }
                                }
                            }
                            vo.setDetail(subList);
                            returnListJob.add(vo);
                        }

                        // 保存数据
                        StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                        record.setIndicatorDate(date);
                        record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX_MINOR.getCode());
                        record.setDataSecondaryClassification(Integer.parseInt(userTypeStrJob));
                        record.setHistoryData(JSON.toJSONString(returnListJob));
                        historyRecordMapper.insertSelective(record);


                    }
                }
            } else {
                // 非定时任务执行
                // 忙闲指数
                List<BusyAndIdleResponse> busyIdleHoursList = tbDyHoursMapper.selectBusyIdleHoursForMatrixMinor(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStr, userExcludeList);
                Map<String, List<BusyAndIdleResponse>> map = busyIdleHoursList.stream().collect(Collectors.groupingBy(BusyAndIdleResponse::getTeam));
                // 能力指数
                List<CapacityResponse> capacityList = tbDyHoursMapper.selectCapacityHoursForMatrixMinor(startTime, endTime, CommonConstant.ROLE_ORDER_STATIC, userTypeStr, userExcludeList);
                if (CollectionUtil.isEmpty(capacityList)) {
                    return returnList;
                }
                // 能力指数 1.按照战队分组
                Map<String, List<CapacityResponse>> capacityTeamMap = capacityList.stream().collect(Collectors.groupingBy(CapacityResponse::getTeam));

                for (TeamMinorInfo teamInfo : teamList) {
                    BusyAndIdleMatrixVO vo = new BusyAndIdleMatrixVO();
                    vo.setTeam(teamInfo.getTeamName());
                    vo.setTeamId(teamInfo.getTeamId());
                    // 获取指定战队的 忙闲指数数据
                    List<BusyAndIdleResponse> busyIdleTeamList = map.get(teamInfo.getTeamName());
                    // 获取指定战队的 能力指数数据
                    List<CapacityResponse> capacityTeamList = capacityTeamMap.get(teamInfo.getTeamName());

                    List<BusyAndIdleMatrixSubVO> subList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(busyIdleTeamList) && CollectionUtil.isNotEmpty(capacityTeamList)) {
                        // 能力指数 2.按照人员分组
                        Map<String, List<CapacityResponse>> capacityTeamPersonMap = capacityTeamList.stream().collect(Collectors.groupingBy(CapacityResponse::getApplicantUserName));

                        for (BusyAndIdleResponse response : busyIdleTeamList) {
                            BusyAndIdleMatrixSubVO matrixSubVO = new BusyAndIdleMatrixSubVO();
                            String userName = response.getUserName();
                            String applicantUserName = response.getApplicantUserName();
                            matrixSubVO.setName(userName);
                            matrixSubVO.setLoginAccount(response.getApplicantUserName());
                            // 忙闲：实际工时=负荷工时；能力：标准工时=实际工时
                            BigDecimal hours = response.getHours();//实际工时
                            BigDecimal standardTime = response.getStandardTime();//标准工时
                            //X 轴 忙闲
//                        // 产品逻辑
//                        matrixSubVO.setBusy(hours.subtract(BigDecimal.valueOf(loadHour)));
                            // 前端逻辑
                            matrixSubVO.setBusy(DecimalUtils.divideZeroByScale(hours, BigDecimal.valueOf(loadHour), 2));
                            //Y 轴 能力
                            //在忙闲的循环中，获取此人的能力指数
                            List<CapacityResponse> personList = capacityTeamPersonMap.get(applicantUserName);
                            if (CollectionUtil.isNotEmpty(personList)) {
                                // 该人总任务数
                                int countAll = personList.size();
                                // 1.每个任务的"实际工时/标准工时"  求和 sum
                                double multiplierSum = personList.stream().mapToDouble(CapacityResponse::getMultiplier).sum();
                                // 2.平均值 = （sum/总任务数）的倒数 ： 因为前端画图的逻辑是 （标准工时/实际工时）
                                BigDecimal avg = DecimalUtils.divideZeroByScale(BigDecimal.valueOf(1), (DecimalUtils.divideZeroByScale(BigDecimal.valueOf(multiplierSum), BigDecimal.valueOf(countAll), 6)), 2);

                                // Y轴计算公式 -(A-1) = 1-A ; A为每个人所有任务的"实际工时/标准工时"的平均值
//                            // 产品逻辑
//                            matrixSubVO.setCapacity(new BigDecimal("1").subtract(avg));
                                // 前端逻辑
                                matrixSubVO.setCapacity(avg);
                                // 此处为解析 高、低、普通能力的逻辑 (产品逻辑)
//                            int comparisonResult = avg.compareTo(new BigDecimal("1"));
//                            if (comparisonResult > 0) {
//                                //大于1 低能力 ; 低能力在Y轴的负轴上，所以加"-"
//                            } else if (comparisonResult < 0) {
//                                //小于1 高能力
//                                matrixSubVO.setCapacity(avg);
//                            } else {
//                                //等于1 普通能力
//                                matrixSubVO.setCapacity(avg);
//                            }
                            } else {
                                log.info("忙闲指数中存在的用户-{}-能力指数不存在", userName);
                                matrixSubVO.setCapacity(BigDecimal.ZERO);
                            }

                            if (hours.compareTo(BigDecimal.ZERO) == 0 || standardTime.compareTo(BigDecimal.ZERO) == 0) {
                                //不放在象限里的情况
                                //1.实际工时为0，为0能力；2.标准工时为0，也不放在象限内； 不放在象限里，剔除，算为没上线，没有表现，评价不出来
                            } else {
                                subList.add(matrixSubVO);
                            }
                        }
                    }
                    vo.setDetail(subList);
                    returnList.add(vo);
                }

                // 保存数据
                StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
                record.setIndicatorDate(date);
                record.setDataClassification(HistoryDataClassificationEnum.DING_HOURS_BUSY_IDLE_MATRIX_MINOR.getCode());
                record.setDataSecondaryClassification(userType);
                record.setHistoryData(JSON.toJSONString(returnList));
                historyRecordMapper.insertSelective(record);
            }

        }
        return returnList;
    }

    /**
     * 定时清理 ai_score 表
     *
     * @return
     */
    @Override
    public String delAiScorePaper() {

        // 获取日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);

        Long fileId = 1L;
        while (fileId > 0) {

            // 查询 ai_score 数据
            List<AIScorePO> dataList = aiScoreMapper.getAiScoreData(fileId).stream()
                    .filter(s -> s.getUpdateTime().before(calendar.getTime()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(dataList)) {
                List<String> dataIdList = dataList.stream().map(AIScorePO::getId).collect(Collectors.toList());
                // 更新 ai_score
                aiScoreMapper.delDocPaperData(dataIdList);
                fileId = dataList.get(dataIdList.size() - 1).getFileId();
            } else {
                fileId = 0L;
            }
        }

        return "success";
    }

    @Override
    public void updateTakBusiness() {
        List<TakBusinessVO> updatedList = new ArrayList<>();
        List<TakBusinessVO> list = takBusinessMapper.getAllBusiness();
        for (TakBusinessVO vo : list) {
            TakBusinessVO updateVO = new TakBusinessVO();
            BusinessProjectResponse response = requestService.getProjectDetail(String.valueOf(vo.getBusid()));
            if (response != null) {
                String businessProjectName = response.getBusinessProjectName();
                if (StringUtils.isNotEmpty(businessProjectName)) {
                    updateVO.setBusid(vo.getBusid());
                    updateVO.setBusname(businessProjectName);
                    updatedList.add(updateVO);
                }
            }
        }
        takBusinessMapper.batchUpdateName(updatedList);
    }

    @Override
    public ReportOverallDTO minorOverall(String createdDate) {

        if (StringUtils.isBlank(createdDate)) {
            createdDate = DateUtils.getNowDate("yyyyMMdd");
        }
        String endTime = DateUtils.convertToDateTime(createdDate);
        if (StringUtils.isBlank(endTime)) {
            throw new TakException("查询日期错误！，日期为：" + createdDate);
        }
        endTime = DateUtils.getLastDayEnd(endTime);
        String startTime = DateUtils.getDateTimeAgo(endTime, 30);

        ReportOverallDTO overallDTO = new ReportOverallDTO();
        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(createdDate, HistoryDataClassificationEnum.MINOR_REPORT_OVERALL.getCode(), null);
        if (historyRecord != null) {
            overallDTO = new Gson().fromJson(historyRecord.getHistoryData(), ReportOverallDTO.class);
        } else {
            log.info("-----calculate minor report overall task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<TeamInfo> teamList = teamInfoMapper.getAllTeamInfo();
            // 人员列表
            List<SysTeamUser> userList = sysTeamUserMapper.getCurrentList();
            // 事项列表
            List<CalcReportInfo> projectList = tbDyProjectMapper.getProjectList(startTime, endTime);

            // 赋值人员及人员上线数
            itBuService.calcPersonMinorOnlineNum(overallDTO, teamList, userList, projectList);

            // 与上次周报数据做对比
            String oldDate = DateUtils.getDateAgo(createdDate, CommonConstant.INTEGER_7);
            StatisticalHistoryDataRecordPO sevenDaysAgoRecord = historyRecordMapper.selectByDateAndType(oldDate, HistoryDataClassificationEnum.MINOR_REPORT_OVERALL.getCode(), null);

            if (sevenDaysAgoRecord == null) {
                overallDTO.setAvgScoreTrend(0);
                overallDTO.setOnlineNumTrend(0);

            } else {
                ReportOverallDTO sevenDaysAgoOverallDTO = new Gson().fromJson(sevenDaysAgoRecord.getHistoryData(), ReportOverallDTO.class);
                Integer historyOnlineTotalNum = sevenDaysAgoOverallDTO.getOnlineTotalNum();
                Integer onlineTotalNum = overallDTO.getOnlineTotalNum();
                overallDTO.setOnlineNumTrend(onlineTotalNum > historyOnlineTotalNum ? 1 : (onlineTotalNum < historyOnlineTotalNum ? 2 : 0));
            }

            // 保存数据
            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
            record.setIndicatorDate(createdDate);
            record.setDataClassification(HistoryDataClassificationEnum.MINOR_REPORT_OVERALL.getCode());
            record.setHistoryData(JSON.toJSONString(overallDTO));
            historyRecordMapper.insertSelective(record);
        }

        return overallDTO;
    }

    @Override
    public BusinessReportVO minorBusinessReport(String date) {

        if (StringUtils.isBlank(date)) {
            date = DateUtils.getNowDate("yyyyMMdd");
        }
        String endTime = DateUtils.convertToDateTime(date);
        String startTime = DateUtils.getDateTimeAgo(endTime, 14);
        endTime = DateUtils.getLastDayEnd(endTime);

        // 查询历史数据
        StatisticalHistoryDataRecordPO historyRecord = historyRecordMapper.selectByDateAndType(date, HistoryDataClassificationEnum.MINOR_DING_BUSINESS_OPERATE.getCode(), null);

        BusinessReportVO businessReport = new BusinessReportVO();
        if (historyRecord != null) {
            businessReport = new Gson().fromJson(historyRecord.getHistoryData(), BusinessReportVO.class);
        } else {
            log.info("----- calculate businessReport task ----- startTime:{}, endTime:{}", startTime, endTime);

            // 查询战队列表
            List<String> teamList = teamInfoMapper.getOldBuTeamInfo(Collections.singletonList(excludeUsers), CommonConstant.SUB_ROLE);

            // 获取非IT业务单元
            List<StandardTimeDeliverable> unitInfoList = standardTimeMapper.selectByApplicantRole(null, CommonConstant.APPLICATION_ROLE);
            Map<String, List<String>> unitInfoListMap = unitInfoList.stream()
                    .collect(Collectors.groupingBy(
                            StandardTimeDeliverable::getTeam,
                            Collectors.mapping(
                                    StandardTimeDeliverable::getBusinessUnit,
                                    Collectors.collectingAndThen(
                                            Collectors.toSet(),
                                            ArrayList::new
                                    )
                            )
                    ));

            // 事项列表
            List<CalcReportInfo> projectList = tbDyProjectMapper.getOldTeamOperateTask(startTime, endTime, Collections.singletonList(excludeUsers), CommonConstant.SUB_ROLE);

            // 计算各战队业务单元激活率
            List<TeamUnitActiveVO> teamUnitActiveList = itBuService.calcTeamOldUnitActive(unitInfoListMap, projectList, teamList);
            businessReport.setTeamUnitActiveList(teamUnitActiveList);
            businessReport.setBusinessUnitNum(unitInfoList.size());

            // 保存数据
            StatisticalHistoryDataRecordPO record = new StatisticalHistoryDataRecordPO();
            record.setIndicatorDate(date);
            record.setDataClassification(HistoryDataClassificationEnum.MINOR_DING_BUSINESS_OPERATE.getCode());
            record.setHistoryData(JSON.toJSONString(businessReport, SerializerFeature.DisableCircularReferenceDetect));
            historyRecordMapper.insertSelective(record);
        }
        return businessReport;
    }

    /**
     * 计算员工效能分数
     */
    @Override
    public String calcEvaluationScore(AITaskScoreEvaluationDTO dto) {
        List<AITaskScoreEvaluationPO> aiPOList = new ArrayList<>();
        if (dto == null) {
            dto = new AITaskScoreEvaluationDTO();
        }

        if (dto.getYearMonth() == null) {
            // 判断当前月份
            String yearMonth = DateUtils.getYearMonth();
            dto.setYearMonth(yearMonth);
            // 判断7天前是否是上一个月，如果是，重新计算上一个月的数据
            String beforeYearMonth = DateUtils.getDateTimeAgo(DateUtils.getCurrentTime(), 7).substring(0, 7);
            if (!Objects.equals(yearMonth, beforeYearMonth)) {
                aiPOList = metricsService.calcEvaluation(MetricsParam.builder()
                        .yearMonth(beforeYearMonth).userCodeList(dto.getUserCodeList()).build());

                // 将人员的分存到数据库中, 有则更新，无则新增
                aiTaskScoreEvaluationService.saveEvaluationList(beforeYearMonth, aiPOList);
            }
            // 计算当前月份数据
            aiPOList = metricsService.calcEvaluation(MetricsParam.builder()
                    .yearMonth(yearMonth).userCodeList(dto.getUserCodeList()).build());
        } else {
            // 计算输入月份数据
            aiPOList = metricsService.calcEvaluation(MetricsParam.builder()
                    .yearMonth(dto.getYearMonth()).userCodeList(dto.getUserCodeList()).build());
        }

        // 将人员的分存到数据库中, 有则更新，无则新增
        aiTaskScoreEvaluationService.saveEvaluationList(dto.getYearMonth(), aiPOList);

        return "success";
    }

    /**
     * 计算员工效能运营结果
     */
    @Override
    public String calcOperationalScore(CalcIndicatorDTO dto) {

        if (dto == null) {
            dto = new CalcIndicatorDTO();
        }

        if (dto.getYearMonth() == null) {
            // 判断当前月份
            String yearMonth = DateUtils.getYearMonth();
            // 判断7天前是否是上一个月，如果是，重新计算上一个月的数据
            String beforeYearMonth = DateUtils.getDateTimeAgo(DateUtils.getCurrentTime(), 7).substring(0, 7);
            if (!Objects.equals(yearMonth, beforeYearMonth)) {
                dto.setYearMonth(beforeYearMonth);
                aiEvaluationService.calcOperationalScore(dto);
            }
            // 计算当前月份数据
            dto.setYearMonth(yearMonth);
            aiEvaluationService.calcOperationalScore(dto);
        } else {
            // 计算输入月份数据
            aiEvaluationService.calcOperationalScore(dto);
        }

        return "success";
    }

    /**
     * 按迭代计算交付质量指标
     * @param dto 请求参数
     * @return
     */
    @Override
    public String calcDigitalIndicator(CalcIndicatorDTO dto) {

        if (dto == null) {
            dto = new CalcIndicatorDTO();
        }

        if (dto.getSprint() == null) {
            // 判断当前迭代
            SprintInfoPO nowSprint = sprintInfoMapper.getSprintInfoByDate(DateUtils.calcDateBefore(new Date(), 1));

            // 判断3天前是否是上一个迭代，如果是，重新计算上一个迭代的数据
            SprintInfoPO lastSprint = sprintInfoMapper.getSprintInfoByDate(DateUtils.calcDateBefore(new Date(), 4));
            if (!Objects.equals(nowSprint.getSprintName(), lastSprint.getSprintName())) {
                aiEvaluationService.calcDigitalIndicator(dto, lastSprint);
            }
            // 计算当前迭代数据
            aiEvaluationService.calcDigitalIndicator(dto, nowSprint);
        } else {
            // 查询迭代信息
            SprintInfoPO sprint = sprintInfoMapper.selectByName(dto.getSprint());
            dto.setSprint(sprint.getSprintName());
            aiEvaluationService.calcDigitalIndicator(dto, sprint);
        }

        return "success";
    }

    @Override
    public void getApdexScore(String date) {
        if (StringUtils.isBlank(date) || !DateUtils.validateDate(date, DateUtils.FORMAT_DATE)) {
            date = DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE);
        }
        devopsSyncService.recordApdex(date);
    }

    /**
     * 每日调用：推送 Apdex 低于 0.94 的服务预警通知
     */
    public String slaDingNotes() {
        try {
            // 1. 获取前一日日期
            LocalDate yesterday = LocalDate.now().minusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String dateStr = yesterday.format(formatter);

            // 2. 查询前一日 Apdex < 0.94 的 MSA 服务
            List<ApdexWarningDTO> lowApdexServices = appPerformanceMetricMapper.selectLowApdexServices(dateStr, 0.94, "MSA");


            log.info("lowApdexServices",JSON.toJSONString(lowApdexServices));
            if (lowApdexServices == null || lowApdexServices.isEmpty()) {
                return "【Apdex 预警】昨日无低于 0.94 的服务，系统稳定。";
            }

            // 3. 构建 Markdown 表格消息
            StringBuilder message = new StringBuilder();
            message.append("【系统稳定性预警】昨日 Apdex < 0.94 服务清单\n\n");

            message.append("| 服务模块 | Apdex得分 | 服务负责人 |\n");
            message.append("|---------|----------|------------|\n");

            Set<String> atUserIds = new HashSet<>();
            Set<String> architectNames = new HashSet<>();        // 架构师姓名（sub_leader）
            Set<String> coachNames = new HashSet<>();           // 敏捷教练姓名（system_leader）
            for (ApdexWarningDTO service : lowApdexServices) {
                message.append(String.format("| %s | %.2f | %s |\n",
                        service.getServiceCode(),
                        service.getApdex(),
                        service.getSubImplementLeader() ));

                // 收集服务负责人域账号用于 @
//                if (service.getSubLeaderAccount() != null) {
//                    atUserIds.add(service.getSubLeaderAccount());
//                }
//                if (service.getSystemLeaderAccount() != null) {
//                    atUserIds.add(service.getSystemLeaderAccount());
//                }

                // 收集姓名（用于展示）
//                if (service.getSubLeader() != null) {
//                    architectNames.add(service.getSubLeader());
//                }
                if (service.getSystemLeader() != null) {
                    coachNames.add(service.getSystemLeader());
                }

            }

            message.append("\n📌 处理要求：\n");
            message.append("1. 确认问题真实性\n");
            message.append("2. 根因分析\n");
            message.append("3. 制定整改方案（视情况：如单接口、单sql等级别3日内，服务交互、历史、中间件等问题7日内）\n\n");
            // 构建去重后的抄送文案
            StringBuilder cc = new StringBuilder("抄送：");

            // 架构师（sub_leader）
            if (!architectNames.isEmpty()) {
                cc.append("架构师@费奕繁，@张超，@鹿胜宝");
            }

            // 分隔符（如果两个都有）
            if (!architectNames.isEmpty() && !coachNames.isEmpty()) {
                cc.append("，");
            }

            // 敏捷教练（system_leader）
            if (!coachNames.isEmpty()) {
                cc.append("系统负责人@").append(String.join("，@", coachNames));
            }

            message.append("\n").append(cc.toString());

            // 4. 发送钉钉消息（使用 Markdown 格式更佳，但 text 也可清晰显示）
            Boolean success = pushRobotMsg(
                    slaRobotToken,
                    String.join(",", atUserIds),
                    slaRobotSecret,
                    message.toString(),
                    false  // 不开启 @all
            );

            return (success ? "推送成功" : "推送失败") + "：共 " + lowApdexServices.size() + " 个低分服务";

        } catch (Exception e) {
            log.error("聚合发送 Apdex 预警失败", e);
            return "推送失败：" + e.getMessage();
        }
    }


}
