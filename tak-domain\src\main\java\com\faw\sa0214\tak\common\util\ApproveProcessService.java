package com.faw.sa0214.tak.common.util;

import com.alibaba.fastjson.JSONObject;
import com.faw.sa0214.tak.client.dto.bpm.StartProcessDTO;
import com.faw.sa0214.tak.common.constant.BizException;
import com.faw.sa0214.tak.mapper.PersonalMapper;
import com.faw.sa0214.tak.model.dto.SysTeamUserDTO;
import com.faw.sa0214.tak.model.request.SysTeamUserRequest;
import com.faw.sa0214.tak.po.TeamInfo;
import com.faw.sa0214.tak.po.TeamRole;
import com.faw.sa0214.tak.po.UserChangeHistroy;
import com.faw.sa0214.tak.service.ProcessService;
import com.faw.sa0214.tak.service.TeamInfoService;
import com.faw.sa0214.tak.service.TeamRoleService;
import com.faw.sa0214.tak.service.UserChangeHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 人员变动批准流程服务（暂未整理）
 *
 * <AUTHOR>
 * @date 2024/08/22
 */
@Slf4j
@Service
@Deprecated
public class ApproveProcessService {

    @Autowired
    private PersonalMapper personalMapper;
    private UserChangeHistoryService userChangeHistoryService;
    @Autowired
    private TeamInfoService teamInfoService;
    @Autowired
    private TeamRoleService teamRoleService;

    @Autowired
    private ProcessService processService;

    /**
     * 启动工作流
     *
     * @param creatorCode     提交人
     * @param approveUserCode 被审批人
     * @return {@code String }
     * @throws Throwable throwable
     * <AUTHOR>
     */
    public String startWorkflowChangeUser(String creatorCode, String approveUserCode, UserChangeHistroy userChangeHistroy) {
        String processInstanceId = "";
        userChangeHistoryService = SpringContextUtil.getBean(UserChangeHistoryService.class);
        String taskInstanceCode = "";
        String approvePerson = "";
        SysTeamUserDTO sysTeamUserDTO = null;
        List<SysTeamUserDTO> userDTOS;
        log.info("战队信息" + userChangeHistroy);
        try {
            //获得申请人相关信息
            SysTeamUserRequest sysTeamUserRequest = new SysTeamUserRequest();
            sysTeamUserRequest.setUserId(approveUserCode);
            userDTOS = personalMapper.findPersonalInfo(sysTeamUserRequest);
            if (userDTOS != null && !userDTOS.isEmpty() && userDTOS.get(0) != null) {
                sysTeamUserDTO = userDTOS.get(0);
                userChangeHistroy.setLoginAccount(sysTeamUserDTO.getLoginAccount());
                userChangeHistroy.setAdjustBeforeProductChain(sysTeamUserDTO.getProductChain());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
        if (sysTeamUserDTO != null && sysTeamUserDTO.getUserId() != null && !sysTeamUserDTO.getUserId().isEmpty()) {
            try {
                //判断战队长是否是同一人
                //判断是否是多个流程,并是否进入第二个流程,如果没有则说明首次进入
                if (userChangeHistroy.getApproveNodes() != null && !userChangeHistroy.getApproveNodes().isEmpty()) {
                    JSONObject jsonObject = JSONObject.parseObject(userChangeHistroy.getApproveNodes());
                    approvePerson = jsonObject.getString("second");
                    // 第二次审批增加"等同于战队长" start
                    String becondManager = "";
                    List<TeamInfo> teamAfters = teamInfoService.getTeamByTeamId(userChangeHistroy.getAdjustAfterTeam());
                    if (CollectionUtils.isNotEmpty(teamAfters)) {
                        becondManager = teamAfters.get(0).getTeamSecondManager(); //审批后"等同于战队长"
                        if (StringUtils.isNotEmpty(becondManager)) {
                            approvePerson = approvePerson + "," + becondManager;
                        }
                    }
                    // 第二次审批增加"等同于战队长" end
                } else {
                    String beforeTeamManager = "";
                    String beforeTeamSecondManager = "";
                    String afterTeamManager = "";
                    String afterTeamSecondManager = "";
                    String beforeTeamManagerName = "";
                    String afterTeamManagerName = "";
                    boolean adjustBeforeTeamNameFlag = false;
                    List<TeamInfo> teamBefores = teamInfoService.getTeamByTeamId(userChangeHistroy.getAdjustBeforeTeam());
                    List<TeamInfo> teamAfters = teamInfoService.getTeamByTeamId(userChangeHistroy.getAdjustAfterTeam());
                    //审批人处理----开始
                    if (teamBefores != null && !teamBefores.isEmpty()) {
                        if (StringUtils.isBlank(userChangeHistroy.getAdjustBeforeTeam())) {
                            beforeTeamManager = "zhaoyanhui3";      // 没有战队长的人，给赵艳辉审批
                            adjustBeforeTeamNameFlag = true; // 审批前战队名称默认为空，不再是"保密"
                        } else {
                            beforeTeamManager = teamBefores.get(0).getTeamMagager();         //审批前战队长
                            beforeTeamSecondManager = teamBefores.get(0).getTeamSecondManager();         //审批前"等同于战队长"
                        }
                        SysTeamUserRequest sysTeamUserRequest = new SysTeamUserRequest();
                        sysTeamUserRequest.setLoginAccount(beforeTeamManager);
                        SysTeamUserDTO sysTeamUserTeam = personalMapper.findPersonalByLoginAccount(sysTeamUserRequest);
                        if (sysTeamUserTeam != null) {
                            beforeTeamManagerName = sysTeamUserTeam.getUserName();
                        }
                    }
                    if (teamAfters != null && !teamAfters.isEmpty()) {
                        afterTeamManager = teamAfters.get(0).getTeamMagager();                //审批后战队长
                        afterTeamSecondManager = teamAfters.get(0).getTeamSecondManager();    //审批后"等同于战队长"
                        SysTeamUserRequest sysTeamUserRequest = new SysTeamUserRequest();
                        sysTeamUserRequest.setLoginAccount(afterTeamManager);
                        SysTeamUserDTO sysTeamUserTeam = personalMapper.findPersonalByLoginAccount(sysTeamUserRequest);
                        if (sysTeamUserTeam != null) {
                            afterTeamManagerName = sysTeamUserTeam.getUserName();
                        }
                    }
                    //判断审批人是否是同一个
                    log.info("beforeTeamManager是" + beforeTeamManager);
                    log.info("afterTeamManager是" + afterTeamManager);
                    log.info("beforeTeamSecondManager是" + beforeTeamSecondManager);
                    log.info("afterTeamSecondManager是" + afterTeamSecondManager);
                    if (!beforeTeamManager.equals("") && !afterTeamManager.equals("") && !beforeTeamManager.equals(afterTeamManager)) {
                        //将申请放入记录中
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("first", beforeTeamManager);
                        jsonObject.put("second", afterTeamManager);
                        userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
                        approvePerson = beforeTeamManager;
                        if (StringUtils.isNotEmpty(beforeTeamSecondManager)) {
                            approvePerson = beforeTeamManager + "," + beforeTeamSecondManager;
                        }
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        if (StringUtils.isNotEmpty(beforeTeamManager)) {
                            approvePerson = beforeTeamManager;
                            if (StringUtils.isNotEmpty(beforeTeamSecondManager)) {
                                approvePerson = beforeTeamManager + "," + beforeTeamSecondManager;
                            }
                        } else {
                            approvePerson = afterTeamManager;
                            if (StringUtils.isNotEmpty(afterTeamSecondManager)) {
                                approvePerson = afterTeamManager + "," + afterTeamSecondManager;
                            }
                        }

                        log.info("审批人是" + approvePerson + ".");
                        jsonObject.put("first", (beforeTeamManager == null || beforeTeamManager.equals("")) ? afterTeamManager : beforeTeamManager);
                        userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
                    }
                    //审批人处理----接收
                    //角色处理---开始
                    String beforeRoleId = "";
                    String afterRoleId = "";
                    String beforeRoleName = "";
                    String afterRoleName = "";
                    String beforeRoleIdMinor = "";
                    String beforeRoleNameMinor = "";
                    String afterRoleIdMinor = "";
                    String afterRoleNameMinor = "";
                    if (userChangeHistroy != null && userChangeHistroy.getAdjustBeforeRole() != null && !userChangeHistroy.getAdjustBeforeRole().equals("")) {
                        TeamRole teamRole = teamRoleService.getById(userChangeHistroy.getAdjustBeforeRole());
                        beforeRoleName = teamRole.getRoleName();
                        beforeRoleId = userChangeHistroy.getAdjustBeforeRole();
                    }
                    if (userChangeHistroy != null && userChangeHistroy.getAdjustAfterRole() != null && !userChangeHistroy.getAdjustAfterRole().equals("")) {
                        TeamRole teamRole = teamRoleService.getById(userChangeHistroy.getAdjustAfterRole());
                        afterRoleName = teamRole.getRoleName();
                        afterRoleId = userChangeHistroy.getAdjustAfterRole();
                    }
                    if (userChangeHistroy != null && userChangeHistroy.getAdjustBeforeRoleMinor() != null && !userChangeHistroy.getAdjustBeforeRoleMinor().equals("")) {
                        TeamRole teamRole = teamRoleService.getById(userChangeHistroy.getAdjustBeforeRoleMinor());
                        beforeRoleNameMinor = teamRole.getRoleName();
                        beforeRoleIdMinor = userChangeHistroy.getAdjustBeforeRoleMinor();
                    }
                    if (userChangeHistroy != null && userChangeHistroy.getAdjustAfterRoleMinor() != null && !userChangeHistroy.getAdjustAfterRoleMinor().equals("")) {
                        TeamRole teamRole = teamRoleService.getById(userChangeHistroy.getAdjustAfterRoleMinor());
                        afterRoleNameMinor = teamRole.getRoleName();
                        afterRoleIdMinor = userChangeHistroy.getAdjustAfterRoleMinor();
                    }
                    //角色处理---结束
                    if (beforeTeamManagerName == null || beforeTeamManagerName.equals("")) {
                        if (userDTOS != null && userDTOS.size() > 0) {
                            SysTeamUserDTO sysTeamUserDTO1 = userDTOS.get(0);
                            beforeTeamManagerName = sysTeamUserDTO1.getHomeDevTeam();
                        }
                    }
                    if (beforeRoleName == null || beforeRoleName.equals("")) {
                        if (userDTOS != null && userDTOS.size() > 0) {
                            SysTeamUserDTO sysTeamUserDTO1 = userDTOS.get(0);
                            beforeRoleName = sysTeamUserDTO1.getApplicationRole();
                        }
                    }
                    userChangeHistroy.setAdjustBeforeTeam((teamBefores != null && teamBefores.size() > 0 && teamBefores.get(0) != null && teamBefores.get(0).getTeamId() != null && !teamBefores.get(0).getTeamId().equals("")) ? teamBefores.get(0).getTeamId().toString() : "");
                    userChangeHistroy.setAdjustAfterTeam((teamAfters != null && teamAfters.size() > 0 && teamAfters.get(0) != null && teamAfters.get(0).getTeamId() != null && !teamAfters.get(0).getTeamId().equals("")) ? teamAfters.get(0).getTeamId().toString() : "");
                    if (adjustBeforeTeamNameFlag) {
                        // 审批前战队名称默认为空，不再是"保密"
                        userChangeHistroy.setAdjustBeforeTeamName("");
                    } else {
                        userChangeHistroy.setAdjustBeforeTeamName((teamBefores != null && teamBefores.size() > 0 && teamBefores.get(0) != null && teamBefores.get(0).getTeamName() != null && !teamBefores.get(0).getTeamName().equals("")) ? teamBefores.get(0).getTeamName() : "");
                    }
                    userChangeHistroy.setAdjustAfterTeamName((teamAfters != null && teamAfters.size() > 0 && teamAfters.get(0) != null && teamAfters.get(0).getTeamName() != null && !teamAfters.get(0).getTeamName().equals("")) ? teamAfters.get(0).getTeamName() : "");
                    userChangeHistroy.setBeforeApprover((teamBefores != null && teamBefores.size() > 0 && teamBefores.get(0) != null && teamBefores.get(0).getTeamMagager() != null && !teamBefores.get(0).getTeamMagager().equals("")) ? teamBefores.get(0).getTeamMagager() : "");
                    userChangeHistroy.setAfterApprover((teamAfters != null && teamAfters.size() > 0 && teamAfters.get(0) != null && teamAfters.get(0).getTeamMagager() != null && !teamAfters.get(0).getTeamMagager().equals("")) ? teamAfters.get(0).getTeamMagager() : "");
                    userChangeHistroy.setBeforeApproverName(beforeTeamManagerName);
                    userChangeHistroy.setAfterApproverName(afterTeamManagerName);
                    userChangeHistroy.setAdjustBeforeRole(beforeRoleId);
                    userChangeHistroy.setAdjustBeforeRoleName(beforeRoleName);
                    userChangeHistroy.setAdjustAfterRole(afterRoleId);
                    userChangeHistroy.setAdjustAfterRoleName(afterRoleName);
                    //辅角色
                    userChangeHistroy.setAdjustBeforeRoleMinor(beforeRoleIdMinor);
                    userChangeHistroy.setAdjustBeforeRoleNameMinor(beforeRoleNameMinor);
//                    userChangeHistroy.setAdjustBeforeChildRoleMinor("");
                    userChangeHistroy.setAdjustAfterRoleMinor(afterRoleIdMinor);
                    userChangeHistroy.setAdjustAfterRoleNameMinor(afterRoleNameMinor);
//                    userChangeHistroy.setAdjustAfterChildRoleMinor("");


                    userChangeHistroy.setIsCompleted("false");
                    userChangeHistroy.setCreatedBy((userChangeHistroy.getCreatedBy() != null && !userChangeHistroy.getCreatedBy().equals("")) ? userChangeHistroy.getCreatedBy() : creatorCode);
                    userChangeHistoryService.save(userChangeHistroy);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw e;
            }
            //获得记录中的业务id装备传入审批流程
            if (userChangeHistroy != null && userChangeHistroy.getId() != null && !userChangeHistroy.getId().equals("")) {
                taskInstanceCode = String.valueOf(userChangeHistroy.getId());
                //拼装map
                Map<String, String> map = new HashMap<>();
                if (sysTeamUserDTO.getUserName() != null && !sysTeamUserDTO.getUserName().equals("")) {
                    map.put("titleName", sysTeamUserDTO.getUserName() + "的岗位调整");
                } else {
                    map.put("titleName", "岗位调整");
                }
                map.put("userName", sysTeamUserDTO.getUserName());
                map.put("adjustBeforeTeam", userChangeHistroy.getAdjustBeforeTeamName());
                map.put("adjustAfterTeam", userChangeHistroy.getAdjustAfterTeamName());
                map.put("adjustBeforeRole", userChangeHistroy.getAdjustBeforeRoleName());
                map.put("adjustAfterRole", userChangeHistroy.getAdjustAfterRoleName());
                map.put("adjustBeforeChildRole", userChangeHistroy.getAdjustBeforeChildRole());
                map.put("adjustAfterChildRole", userChangeHistroy.getAdjustAfterChildRole());
                map.put("adjustBeforeChildTeam", userChangeHistroy.getAdjustBeforeChildTeam());
                map.put("adjustAfterChildTeam", userChangeHistroy.getAdjustAfterChildTeam());

                map.put("adjustBeforeRoleMinor", userChangeHistroy.getAdjustBeforeRoleNameMinor());
                map.put("adjustAfterRoleMinor", userChangeHistroy.getAdjustAfterRoleNameMinor());
                map.put("adjustBeforeChildRoleMinor", userChangeHistroy.getAdjustBeforeChildRoleMinor());
                map.put("adjustAfterChildRoleMinor", userChangeHistroy.getAdjustAfterChildRoleMinor());
                // 修改 产品链
                map.put("adjustBeforeProductChain", userChangeHistroy.getAdjustBeforeProductChain());
                map.put("adjustAfterProductChain", userChangeHistroy.getAdjustAfterProductChain());

                map.put("code", taskInstanceCode);
                try {
                    log.info("creatorCode" + creatorCode);
                    StartProcessDTO.BizModel bizModel = new StartProcessDTO.BizModel();
                    bizModel.setApplyHeader(map);

                    Map<String, String> assignees = new HashMap<>();
                    assignees.put("审批", approvePerson);

                    if (StringUtils.isBlank(creatorCode)) {
                        creatorCode = approveUserCode;
                    }
                    processInstanceId = processService.startSingleNodeWorkflow(creatorCode, assignees,
                            bizModel, taskInstanceCode);
                    updateUserChangeHistory(taskInstanceCode, processInstanceId);
                } catch (Throwable e) {
                    log.error(e.getMessage(), e);
                    throw new BizException(e.getMessage());
                }
                log.info("线程启动成功，返回流程id：{}", processInstanceId);
                return processInstanceId;
            }
        }
        return processInstanceId;
    }

    private void updateUserChangeHistory(String taskInstanceCode, String processInstanceId) {
        if (taskInstanceCode != null && !taskInstanceCode.equals("") && processInstanceId != null && !processInstanceId.equals("")) {
            userChangeHistoryService = SpringContextUtil.getBean(UserChangeHistoryService.class);
            UserChangeHistroy userChangeHistroy = new UserChangeHistroy();
            userChangeHistroy.setId(Integer.parseInt(taskInstanceCode));
            userChangeHistroy.setProcessInstanceId(processInstanceId);
            userChangeHistroy.setIsCompleted("false");
            userChangeHistoryService.updateById(userChangeHistroy);
        }
    }
}
