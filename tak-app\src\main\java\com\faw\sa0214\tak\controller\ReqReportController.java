package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.dto.rwh.RwhPageResult;
import com.faw.sa0214.tak.client.dto.rwh.StatisticsTaskInstanceBO;
import com.faw.sa0214.tak.client.dto.rwh.StatisticsTaskInstanceDTO;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.request.*;
import com.faw.sa0214.tak.model.response.ProductManageResponse;
import com.faw.sa0214.tak.model.response.ReqFinishResponse;
import com.faw.sa0214.tak.model.response.RequirementReportResponse;
import com.faw.sa0214.tak.service.ReqReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 需求池周报
 * 页面接口
 */
@Tag(name = "需求池周报", description = "需求池周报")
@RequestMapping("/reqReport")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReqReportController {

    private final ReqReportService reqReportService;

    @Operation(summary = "产品运营", description = "[author:10027705]")
    @PostMapping("/getProductManageReport")
    public Result<List<ProductManageResponse>> getProductManageReport(@Validated @RequestBody ReqProductManageRequest request) {
        return Result.success(reqReportService.getProductManageReport(request));
    }

    @Operation(summary = "获取角色工作台任务（远程调用使用）", description = "[author:10027705]")
    @PostMapping("/getRwhTaskInfo")
    public Result<RwhPageResult<StatisticsTaskInstanceDTO>> getRwhTaskInfo(@RequestBody StatisticsTaskInstanceBO bo) {
        return Result.success(reqReportService.getRwhTaskInfo(bo));
    }

    @Operation(summary = "本周完成-需求分析&产品设计模块", description = "[author:50012536]")
    @PostMapping("/getFinishRequirement1")
    public Result<List<ReqFinishResponse>> getFinishRequirement1(@RequestBody ReqRequest request) {
        return Result.success(reqReportService.getFinishRequirement1(request));
    }

    @Operation(summary = "本周完成-产品研发&产品上线模块", description = "[author:50012536]")
    @PostMapping("/getFinishRequirement2")
    public Result<List<ReqFinishResponse>> getFinishRequirement2(@RequestBody ReqRequest request) {
        return Result.success(reqReportService.getFinishRequirement2(request));
    }

    @Operation(summary = "下周计划-需求列表", description = "[author:50012536]")
    @PostMapping("/getNextWeekRequirement")
    public Result<List<ReqFinishResponse>> getNextWeekRequirement(@RequestBody ReqRequest request) {
        return Result.success(reqReportService.getNextWeekRequirement(request));
    }

    @Operation(summary = "下周计划-获取待添加需求列表", description = "[author:50012536]")
    @PostMapping("/getRequirementListByUser")
    public Result<PageResult<ReqFinishResponse>> getRequirementListByUser(@RequestBody ReqPageRequest request) {
        return Result.success(reqReportService.getRequirementListByUser(request));
    }

    @Operation(summary = "下周计划-新增计划", description = "[author:50012536]")
    @PostMapping("/addReqNextWeekPlan")
    public Result<String> addReqNextWeekPlan(@RequestBody List<ReqPlanRequest> request) {
        return reqReportService.addReqNextWeekPlan(request);
    }

    @Operation(summary = "下周计划-删除计划", description = "[author:50012536]")
    @PostMapping("/delReqNextWeekPlan")
    public Result<String> delReqNextWeekPlan(@RequestBody ReqRequest request) {
        return reqReportService.delReqNextWeekPlan(request);
    }


    // ----------------------------------------- 前需求池周报接口 ---------------------------------------------- */

    @Operation(summary = "需求池周报-根据时间范围查询迭代", description = "[author:10027705]")
    @PostMapping("/getSprintByDate")
    public Result<String> getSprintByDate(@RequestBody RequireSprintRequest request) {
        return reqReportService.getSprintByDate(request);
    }

    @Operation(summary = "需求池周报-饼图", description = "[author:10027705]")
    @PostMapping("/getRequirePieReport")
    public Result<RequirementPieReportDTO> getRequirePieReport(@RequestBody RequireSprintRequest request) {
        return reqReportService.getRequirePieReport(request);
    }

    @Operation(summary = "需求池周报-人员列表", description = "[author:10027705]")
    @PostMapping("/getRequireUserReport")
    public Result<List<RequirementUserReportDTO>> getRequireUserReport(@RequestBody RequireSprintRequest request) {
        return reqReportService.getRequireUserReport(request);
    }

    @Operation(summary = "需求池周报-人员维度-饼图", description = "[author:10027705]")
    @PostMapping("/getRequireUserReportPie")
    public Result<RequirementPieReportDTO> getRequireUserReportPie(@RequestBody RequireReportUserRequest request) {
        return reqReportService.getRequireUserReportPie(request);
    }

    @Operation(summary = "需求池周报-人员维度-流程统计", description = "[author:10027705]")
    @PostMapping("/getRequireUserReportFlow")
    public Result<List<RequirementReportUserFlowDTO>> getRequireUserReportFlow(@RequestBody RequireReportUserRequest request) {
        return reqReportService.getRequireUserReportFlow(request);
    }

    @Operation(summary = "需求池周报-人员维度-需求列表", description = "[author:10027705]")
    @PostMapping("/getRequireUserReportList")
    public Result<PageResult<RequirementReportResponse>> getRequireUserReportList(@RequestBody RequireReportUserRequest request) {
        return reqReportService.getRequireUserReportList(request);
    }

    @Operation(summary = "需求池周报-需求状态维度-折线图-新增", description = "[author:10027705]")
    @PostMapping("/getRequireStatusReportLine")
    public Result<List<RequirementStatusReportLineDTO>> getRequireStatusReportLine(@RequestBody RequireReportStatusRequest request) {
        return reqReportService.getRequireStatusReportLine(request);
    }

    @Operation(summary = "需求池周报-需求状态维度-流程统计", description = "[author:10027705]")
    @PostMapping("/getRequireStatusReportFlow")
    public Result<List<RequirementStatusReportFlowDTO>> getRequireStatusReportFlow(@RequestBody RequireReportStatusRequest request) {
        return reqReportService.getRequireStatusReportFlow(request);
    }

    @Operation(summary = "需求池周报-需求状态维度-需求列表", description = "[author:10027705]")
    @PostMapping("/getRequireStatusReportList")
    public Result<PageResult<RequirementReportResponse>> getRequireStatusReportList(@RequestBody RequireReportStatusRequest request) {
        return reqReportService.getRequireStatusReportList(request);
    }

}
