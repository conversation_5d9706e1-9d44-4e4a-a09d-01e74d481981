package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.user.UserCheck;
import com.faw.sa0214.tak.model.request.RequirementStatisticsRequest;
import com.faw.sa0214.tak.model.response.RequirementStatisticsCardResponse;
import com.faw.sa0214.tak.model.response.RequirementStatisticsResponse;
import com.faw.sa0214.tak.service.RequirementStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/03/25
 */
@Tag(name = "需求池统计", description = "需求池统计")
@RequestMapping("/requirement/statistics")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RequirementStatisticsController {

    private final RequirementStatisticsService requirementStatisticsService;

    /**
     * 需求统计-小看板卡片
     * @param request 请求
     * @return {@link Result }<{@link RequirementStatisticsCardResponse }>
     */
    @Operation(summary = "需求统计-小看板卡片", description = "[author:10027705]")
    @PostMapping("/getRequirementCardV2")
    public Result<RequirementStatisticsCardResponse> getRequirementCardV2(@RequestBody RequirementStatisticsRequest request) {
        return requirementStatisticsService.getRequirementCard(request);
    }

    /**
     * 查询需求统计列表
     */
    @Operation(summary = "需求统计-看板列表", description = "[author:50012536]")
    @PostMapping("/getRequirementStatisticsList")
    public Result<RequirementStatisticsResponse> getRequirementStatisticsList(@RequestBody RequirementStatisticsRequest request) {
        return requirementStatisticsService.getRequirementStatisticsList(request);
    }

}
