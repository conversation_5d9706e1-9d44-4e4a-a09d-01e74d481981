package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.token.TokenCheck;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.vo.*;
import com.faw.sa0214.tak.service.PublicReportService;
import com.faw.sa0214.tak.service.ScheduleJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/28
 */
@Tag(name = "公网报告接口", description = "公网报告接口")
@RequestMapping("/public")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PublicReportController {

    private final PublicReportService publicReportService;
    private final ScheduleJobService scheduleJobService;

    /**
     * 旗效周报--角色运营--查询角色运营总览
     */

    @Operation(summary = "旗效周报--角色运营--查询角色运营总览", description = "[author:10027705]")
    @GetMapping("/getRoleReportOverall")
    @TokenCheck
    public Result<List<RoleReportDTO>> getRoleReportOverall(@RequestParam(name = "sign", required = false) String sign,
                                                            @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                            @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(publicReportService.getRoleReportOverall(createdDate));
    }

    /**
     * 旗效周报--角色运营--个人详情查询
     */

    @Operation(summary = "旗效周报--角色运营--个人详情查询", description = "[author:10027705]")
    @GetMapping("/getRoleReportUserDetail")
    @TokenCheck
    public Result<RoleReportUserDetailDTO> getRoleReportUserDetail(@RequestParam(name = "sign", required = false) String sign,
                                                                   @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                                   @RequestParam(name = "createdDate") String createdDate,
                                                                   @RequestParam(name = "userCode") String userCode) {
        return Result.success(publicReportService.getRoleReportUserDetail(createdDate, userCode));
    }

    /**
     * 旗效周报--角色运营--角色大榜
     */
    @Operation(summary = "旗效周报--角色运营--角色大榜", description = "[author:50012536]")
    @GetMapping("/getRoleReportRanking")
    @TokenCheck
    public Result<RoleReportRankDTO> getRoleReportRanking(@RequestParam(name = "sign", required = false) String sign,
                                                          @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                          @RequestParam(name = "createdDate") String createdDate,
                                                          @RequestParam(name = "subRoleName") String subRoleName,
                                                          @RequestParam(name = "type") Integer type) {
        return Result.success(publicReportService.getRoleReportRanking(createdDate, subRoleName, type));
    }

    /**
     * 旗效周报--战队运营--查询战队运营总览
     */

    @Operation(summary = "旗效周报--战队运营--查询战队运营总览", description = "[author:50012536]")
    @GetMapping("/teamOperationReport")
    @TokenCheck
    public Result<TeamReportVO> teamOperationReport(@RequestParam(name = "sign", required = false) String sign,
                                                    @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                    @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.teamOperationReport(createdDate));
    }

    /**
     * 旗效周报--战队运营--查询战队运营列表数据
     */

    @Operation(summary = "旗效周报--战队运营--查询战队运营列表数据", description = "[author:50012536]")
    @GetMapping("/teamOperationList")
    @TokenCheck
    public Result<List<TeamOperateListVO>> teamOperationList(@RequestParam(name = "sign", required = false) String sign,
                                                             @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                             @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.teamOperationList(createdDate));
    }

    /**
     * 旗效周报-战队运营-正态分布数据
     */

    @Operation(summary = "钉钉周报-战队运营-正态分布数据", description = "[author:10027705]")
    @GetMapping("/teamOperationDistributionList")
    @TokenCheck
    public Result<TeamOperateDistributionListVO> teamOperationDistributionList(@RequestParam(name = "sign", required = false) String sign,
                                                                               @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                                               @RequestParam(name = "createdDate") String createdDate) {
        TeamOperateDistributionListVO result = scheduleJobService.teamOperationDistributionList(createdDate);
        return Result.success(result);
    }

    /**
     * 旗效周报--业务单元运营
     */

    @Operation(summary = "旗效周报--业务单元运营", description = "[author:50012536]")
    @GetMapping("/businessReport")
    @TokenCheck
    public Result<BusinessReportVO> businessReport(@RequestParam(name = "sign", required = false) String sign,
                                                   @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                   @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.businessReport(createdDate));
    }

    /**
     * 旗效周报--工时运营--忙闲指数运营数据
     */

    @Operation(summary = "旗效周报--工时运营--忙闲指数运营数据", description = "[author:10027705]")
    @GetMapping("/busyAndIdleReport")
    @TokenCheck
    public Result<List<BusyAndIdleListVO>> busyAndIdleReport(@RequestParam(name = "sign", required = false) String sign,
                                                             @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                             @RequestParam(name = "createdDate") String createdDate,
                                                             @RequestParam(name = "userType") Integer userType) {
        return Result.success(scheduleJobService.busyAndIdleReport(createdDate, userType));
    }

    /**
     * 旗效周报--工时运营--忙闲指数运营描述
     */

    @Operation(summary = "旗效周报--工时运营--忙闲指数运营描述", description = "忙闲指数运营描述 [author:10027705]")
    @GetMapping("/busyAndIdleDescribe")
    @TokenCheck
    public Result<String> busyAndIdleDescribe(@RequestParam(name = "sign", required = false) String sign,
                                              @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                              @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.busyAndIdleDescribe(createdDate));
    }

    /**
     * 旗效周报--工时运营--战队运营总体描述
     */
    @Operation(summary = "旗效周报--工时运营--战队运营总体描述", description = "[author:10027705]")
    @GetMapping("/overall")
    @TokenCheck
    public Result<ReportOverallDTO> overall(@RequestParam(name = "sign", required = false) String sign,
                                            @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                            @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.overall(createdDate));
    }

    /**
     * 旗效周报--工时运营--能力指数数据
     */

    @Operation(summary = "旗效周报--工时运营--能力指数数据", description = "[author:10027705]")
    @GetMapping("/capacityReport")
    @TokenCheck
    public Result<List<CapacityReportListVO>> capacityReport(@RequestParam(name = "sign", required = false) String sign,
                                                             @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                             @RequestParam(name = "createdDate") String createdDate,
                                                             @RequestParam(name = "userType") Integer userType) {
        return Result.success(scheduleJobService.capacityReport(createdDate, userType));
    }

    /**
     * 旗效周报--工时运营--工时矩阵数据
     */

    @Operation(summary = "旗效周报--工时运营--工时矩阵数据", description = "[author:10027705]")
    @GetMapping("/busyAndIdleMatrix")
    @TokenCheck
    public Result<List<BusyAndIdleMatrixVO>> busyAndIdleMatrix(@RequestParam(name = "sign", required = false) String sign,
                                                               @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                               @RequestParam(name = "createdDate") String createdDate,
                                                               @RequestParam(name = "userType") Integer userType) {
        return Result.success(scheduleJobService.busyAndIdleMatrix(createdDate, userType));
    }

    /**
     * 旗效周报--业务单元运营--运营分析
     */

    @Operation(summary = "旗效周报--业务单元运营--运营分析", description = "[author:10027705]")
    @GetMapping("/businessUnitAnalysis")
    @TokenCheck
    public Result<List<BusinessUnitAnalysisVO>> businessUnitAnalysis(@RequestParam(name = "sign", required = false) String sign,
                                                                     @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                                     @RequestParam(name = "createdDate") String createdDate,
                                                                     @RequestParam(name = "subRole") String subRole,
                                                                     @RequestParam(name = "team", required = false) String team) {
        return Result.success(publicReportService.businessUnitAnalysis(createdDate, subRole, team));
    }

    /**
     * 基础选项信息
     *
     * @return {@link Result }<{@link BaseInfoVO }>
     */
    @Operation(summary = "旗效周报--基础选项", description = "[author:10027705]")
    @GetMapping("/baseInfo")
    public Result<BaseInfoVO> baseInfo() {
        return Result.success(publicReportService.baseInfo());
    }

    /**
     * 旗效周报--角色运营--查询角色运营总榜
     */

    @Operation(summary = "旗效周报--角色运营--查询角色运营总榜", description = "[author:10027705]")
    @GetMapping("/getRoleReportOverallRankData")
    @TokenCheck
    public Result<RoleOverallRankDTO> getRoleReportOverallRankData(@RequestParam(name = "sign", required = false) String sign,
                                                                   @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                                   @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(publicReportService.getRoleReportOverallRankData(createdDate));
    }
}
