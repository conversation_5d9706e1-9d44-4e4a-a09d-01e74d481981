package com.faw.sa0214.tak.mapper;

import com.faw.sa0214.tak.model.dto.CodeReviewGitlabProjectDto;
import com.faw.sa0214.tak.po.CodeReviewGitlabProjectPO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 代码审查项目表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-28 19:38
 */
public interface CodeReviewGitlabProjectMapper {

    /**
     * 新增项目信息
     *
     * @param po 项目信息
     * @return 影响行数
     */
    int insert(CodeReviewGitlabProjectPO po);

    /**
     * 根据项目ID更新项目信息
     *
     * @param po 项目信息
     * @return 影响行数
     */
    int update(CodeReviewGitlabProjectPO po);

    /**
     * 根据项目ID查询项目信息
     *
     * @param projectId 项目ID
     * @return 项目信息
     */
    CodeReviewGitlabProjectPO selectOne(Integer projectId);

    /**
     * 根据条件查询项目信息列表
     *
     * @param dto 条件
     * @return 项目信息列表
     */
    List<CodeReviewGitlabProjectPO> selectList(CodeReviewGitlabProjectDto dto);

    /**
     * 根据id查询记录
     *
     * @param ids IDS
     * @return {@link List }<{@link CodeReviewGitlabProjectPO }>
     */
    List<CodeReviewGitlabProjectPO> selectByIds(@RequestParam("ids") List<Integer> ids);

    /**
     * 按 ID 更新令牌
     *
     * @param id             主键id
     * @param encodedToken   加密令牌
     * @param updateUserName 更新用户名
     * @return int
     */
    int updateTokenById(Integer id, String encodedToken, String updateUserName);

    /**
     * 根据项目ID列表查询项目信息列表
     *
     * @param projectIds 项目ID列表
     * @return 项目信息列表
     */
    List<CodeReviewGitlabProjectPO> selectListByProjectIds(List<Integer> projectIds);
}