package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.user.UserCheck;
import com.faw.sa0214.tak.client.dto.user.CloudNativeUserDTO;
import com.faw.sa0214.tak.client.dto.user.UserByRoleQueryDTO;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.request.*;
import com.faw.sa0214.tak.model.response.ProductManagerResponse;
import com.faw.sa0214.tak.model.request.TeamUserQueryRequest;
import com.faw.sa0214.tak.model.response.TeamUserResponse;
import com.faw.sa0214.tak.po.UserChangeHistroy;
import com.faw.sa0214.tak.service.PersonalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人员管理接口
 *
 * <AUTHOR>
 */
@Tag(name = "人员管理接口", description = "人员管理接口")
@RequestMapping("/personal")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PersonalController {

    private final PersonalService personalService;

    /**
     * 查找idm人员信息
     *
     * @param searchRequest 搜索请求
     * @return {@link Result}<{@link PageResult}<{@link SysTeamUserDTO}>>
     */

    @Operation(summary = "查找idm人员信息", description = "[author:********]")
    @PostMapping("/findIbmPersonalInfo")
    public Result<PageResult<SysTeamUserDTO>> findIbmPersonalInfo(@RequestBody SearchRequest searchRequest) {
        return Result.success(personalService.findIbmPersonalInfo(searchRequest));
    }

    /**
     * 查找 IT PM 人员信息
     * @param searchRequest 搜索请求
     * @return {@link Result}<{@link PageResult}<{@link SysTeamUserDTO}>>
     */
    @UserCheck(interfaceRole = "takAdmin")
    @Operation(summary = "查找 IT PM 人员信息", description = "[author:********]")
    @PostMapping("/findItPmPersonalInfo")
    public Result<PageResult<SysTeamUserDTO>> findItPmPersonalInfo(@RequestBody SearchRequest searchRequest) {
        return Result.success(personalService.findItPmPersonalInfo(searchRequest));
    }

    /**
     * 远程调用，查找IT PM人员信息
     *
     * @param searchRequest 搜索请求
     * @return {@link Result }<{@link PageResult }<{@link SysTeamUserDTO }>>
     */
    @Operation(summary = "远程调用，查找IT PM人员信息", description = "[author:********]")
    @PostMapping("/getITPMUserList")
    public Result<PageResult<SysTeamUserDTO>> getItPmUserList(@RequestBody ItPmPersonSearchDTO searchRequest) {
        return Result.success(personalService.getItPmUserList(searchRequest));
    }

    /**
     * 批量新增用户数据
     * @param saveRequestList sys团队用户请求
     * @return {@link Result}<{@link String}>
     */
    @UserCheck(interfaceRole = "takAdmin")
    @Operation(summary = "批量新增用户数据", description = "[author:********]")
    @PostMapping("/insertSysTeamUser")
    public Result<String> insertSysTeamUser(@RequestBody List<UserSaveRequest> saveRequestList) {
        for (UserSaveRequest request : saveRequestList) {

            // 24-04-30, 由于ITPM部分数据存在userid缺失的问题，产品要求将用户id的校验去掉
            if (StringUtils.isEmpty(request.getUserName())) {
                return Result.failed("用户名为空");
            }
            if (StringUtils.isEmpty(request.getLoginAccount())) {
                return Result.failed("域账号为空");
            }
        }
        if (saveRequestList.size() > CommonConstant.INTEGER_20) {
            return Result.failed("一次性添加人员不得超过20人");
        }
        return personalService.insertSysTeamUser(saveRequestList);
    }


    /**
     * 人员清单列表
     *
     * @param request 用户查询请求
     * @return {@link Result}<{@link PageResult}<{@link SysTeamUserDTO}>>
     */
    @UserCheck(interfaceRole = "takAdmin")
    @Operation(summary = "人员清单列表", description = "[author:********]")
    @PostMapping("/findPersonalInfo")
    public Result<PageResult<SysTeamUserDTO>> findPersonalInfo(@RequestBody UserSearchRequest request) {
        return Result.success(personalService.findPersonalInfo(request));
    }

    /**
     * 按id逻辑删除人员
     * @param sysTeamUserRequest sys团队用户
     * @return {@link Result}<{@link String}>
     */
    @UserCheck(interfaceRole = "takAdmin")
    @Operation(summary = "按id逻辑删除人员", description = "[author:********]")
    @PostMapping("/deletePersonal")
    public Result<String> deletePersonal(@RequestBody SysTeamUserRequest sysTeamUserRequest) throws Throwable {
        if (StringUtils.isEmpty(sysTeamUserRequest.getLoginAccount())) {
            return Result.failed("用户不能为空");
        }
        return personalService.deletePersonal(sysTeamUserRequest);
    }

    /**
     * 获取搜索选项信息
     *
     * @return {@link Result}<{@link SearchOptionsInfo}>
     */
    @Operation(summary = "获取搜索选项信息", description = "[author:********]")
    @PostMapping("/findSearchOptionsInfo")
    public Result<SearchOptionsInfo> findSearchOptionsInfo() {
        return Result.success(personalService.findSearchOptionsInfo());
    }

    /**
     * 按角色查找子角色名称
     *
     * @param role 角色
     * @return {@link Result}<{@link List}<{@link String}>>
     */

    @Operation(summary = "按角色查找子角色名称", description = "[author:********]")
    @GetMapping("/findChildRoleNameByRole")
    public Result<List<String>> findChildRoleNameByRole(@RequestParam("role") String role) {
        return Result.success(personalService.findChildRoleNameByRole(role));
    }

    /**
     * 按战队查找归属开发团队
     *
     * @param team 团队
     * @return {@link Result}<{@link List}<{@link String}>>
     */

    @Operation(summary = "按战队查找归属开发团队", description = "[author:********]")
    @GetMapping("/findHomeDevTeamByTeam")
    public Result<List<String>> findHomeDevTeamByTeam(@RequestParam("team") String team) {
        return Result.success(personalService.findHomeDevTeamByTeam(team));
    }

    /**
     * 按用户id查找修改记录
     * @return {@link Result}<{@link List}<{@link UserChangeHistroy}>>
     */
    @UserCheck(interfaceRole = "takAdmin")
    @Operation(summary = "按用户id查找修改记录", description = "[author:********]")
    @GetMapping("/findHistoryInfoByUserId")
    public Result<List<UserChangeHistroy>> findHistoryInfoByUserId(@RequestParam("userId") String userId) {
        return Result.success(personalService.findHistoryInfoByUserId(userId));
    }

    /**
     * 通过登录帐户查找个人信息
     * @param sysTeamUserRequest sys团队用户请求
     * @return {@link Result}<{@link SysTeamUserDTO}>
     */
    @Operation(summary = "通过登录帐户查找个人信息", description = "[author:********]")
    @PostMapping("/findPersonalByLoginAccount")
    public Result<SysTeamUserDTO> findPersonalByLoginAccount(@RequestBody SysTeamUserRequest sysTeamUserRequest) {
        if (StringUtils.isEmpty(sysTeamUserRequest.getLoginAccount())) {
            return Result.failed("参数不能为空");
        }
        return Result.success(personalService.findPersonalByLoginAccount(sysTeamUserRequest));
    }

    /**
     * 更新团队角色
     *
     * @param sysTeamUserRequest sys团队用户请求
     * @return {@link Result}<{@link String}>
     */

    @Operation(summary = "更新团队角色", description = "[author:********]")
    @PostMapping("/updateTeamRole")
    public Result<String> updateTeamRole(@RequestBody SysTeamUserRequest sysTeamUserRequest) {
        if (StringUtils.isEmpty(sysTeamUserRequest.getLoginAccount())) {
            return Result.failed("用户不能为空");
        }
        personalService.updateTeamRole(sysTeamUserRequest);
        return Result.success("修改成功");
    }

    /**
     * 根据登陆人账号获取人员信息
     *
     * @param loginAccount 登录帐户
     * @return {@link Result}<{@link List}<{@link SysTeamUserDTO}>>
     */
    @Operation(summary = "根据登录人账号获取人员信息", description = "[author:********]")
    @GetMapping("/getPersonalByLoginAccount")
    public Result<List<SysTeamUserDTO>> getPersonalByLoginAccount(
            @RequestParam(value = "loginAccount", required = false) String loginAccount) {
        return Result.success(personalService.getPersonalByLoginAccount(loginAccount));
    }

    /**
     * 根据账号列表获取人员详细信息
     *
     * @param request 请求
     * @return {@link Result}<{@link List}<{@link SysTeamUserDTO}>>
     */

    @Operation(summary = "根据账号列表获取人员详细信息", description = "[author:********]")
    @PostMapping("/getDetailByLoginAccount")
    public Result<List<SysTeamUserDTO>> getDetailByLoginAccount(@RequestBody UserDetailRequest request) {
        return Result.success(personalService.getDetailByLoginAccount(request));
    }


    /**
     * 按团队查找角色
     *
     * @param teamId 团队 ID
     * @param team   团队
     * @return {@link Result}<{@link List}<{@link String}>>
     */

    @Operation(summary = "根据战队名或战队id查询存在成员的主角色列表", description = "[author:********]")
    @GetMapping("/findRoleByTeam")
    public Result<List<String>> findRoleByTeam(@RequestParam(value = "teamId", required = false) Integer teamId,
                                               @RequestParam(value = "team", required = false) String team) {

        return Result.success(personalService.findRoleByTeam(teamId, team));
    }

    /**
     * 获取用户中心数据
     *
     * @param request 用户中心查询请求
     * @return {@link Result}<{@link SysTeamUserDTO}>
     */

    @Operation(summary = "获取用户中心数据", description = "获取用户中心数据[author:********]")
    @PostMapping("/getUserCenterList")
    public Result<PageResult<SysTeamUserDTO>> getUserCenterList(@RequestBody UserCenterSearchRequest request) {
        return Result.success(personalService.getUserCenterList(request));
    }

    /**
     * 检查任务生成权限
     *
     * @param loginAccount 登录账户
     * @return {@link Result}<{@link Integer}>
     */
    @Operation(summary = "检查任务生成权限", description = "[author:********]")
    @GetMapping("/checkTaskGeneratePermission")
    public Result<Integer> checkTaskGeneratePermission(@RequestParam(value = "loginAccount", required = false) String loginAccount) {
        return Result.success(personalService.checkTaskGeneratePermission(loginAccount));
    }

    /**
     * 根据角色查询用户
     *
     * @param request 登录账户
     * @return {@link Result}<{@link Integer}>
     */
    @Operation(summary = "根据角色查询用户", description = "[author:********]")
    @PostMapping("/getUserByRole")
    public Result<List<CloudNativeUserDTO>> getUserByRole(@RequestBody UserByRoleQueryDTO request) {
        return Result.success(personalService.getUserByRole(request));
    }

    /**
     * 查询一段时间内有需求的产品经理（登录人所在战队）
     *
     * @param request 请求
     * @return {@link Result }<{@link List }<{@link ProductManagerResponse }>>
     */
    @Operation(summary = "查询一段时间内有需求的产品经理", description = "[author:********]")
    @PostMapping("/getPeriodReqProductManagerByTeam")
    public Result<List<ProductManagerResponse>> getPeriodReqProductManagerByTeam(@Validated @RequestBody ProductManagerRequest request) {
        return Result.success(personalService.getPeriodReqProductManagerByTeam(request));
    }

    /**
     * 根据团队ID和角色名称查询用户列表
     *
     * @param request 查询请求
     * @return {@link Result}<{@link List}<{@link TeamUserResponse}>>
     */
    @Operation(summary = "根据团队ID和角色名称查询用户列表", description = "[author:********]")
    @PostMapping("/getTeamUsersByTeamIdAndRole")
    public Result<List<TeamUserResponse>> getTeamUsersByTeamIdAndRole(@Validated @RequestBody TeamUserQueryRequest request) {
        return Result.success(personalService.getTeamUsersByTeamIdAndRole(request));
    }

    /**
     * 查询全部人员信息接口
     */
    @Operation(summary = "查询全部人员信息接口", description = "[author:50012536]")
    @PostMapping("/queryUserList")
    public Result<List<UserDTO>> queryUserList(@Validated @RequestBody UserDTO user) {
        return Result.success(personalService.queryUserList(user));
    }

    /**
     * 新增及出项 IT PM 用户
     * 调用方：IT PM
     */
    @Operation(summary = "新增及出项 IT PM 用户", description = "[author:********]")
    @PostMapping("/syncThirdPartyUser")
    public Result<Boolean> syncThirdPartyUser(@Valid @RequestBody List<ThirdPartyUserRequest> userList) {
        return personalService.syncThirdPartyUser(userList);
    }
}
