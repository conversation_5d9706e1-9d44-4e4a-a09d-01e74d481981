package com.faw.sa0214.tak.model.dto.baseCenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户中心职级信息
 */
@Schema(description = "用户中心职级信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPosition {
    @Schema(description = "职级编号")
    private String code;
    @Schema(description = "岗位")
    private String positionName;
    @Schema(description = "岗位类型")
    private String positionType;
    @Schema(description = "岗位信息")
    private String positionSpecies;
    @Schema(description = "级别")
    private String positionLevel;
    @Schema(description = "岗位编号")
    private String dutyCode;
    @Schema(description = "岗位名称")
    private String dutyName;
    @Schema(description = "组织编码")
    private String orgCode;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "人员编号")
    private String userCode;
    @Schema(description = "组织类型")
    private String orgTypeCode;
    @Schema(description = "组织名称")
    private String orgTypeName;
    @Schema(description = "组织全程编号")
    private String orgFullCode;
    @Schema(description = "组织全程")
    private String orgFullName;
    @Schema(description = "公司编号")
    private String companyCode;
    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "三方公司编号")
    private String thirdPartyBusinessKey;
    @Schema(description = "类型")
    private String type;
}
