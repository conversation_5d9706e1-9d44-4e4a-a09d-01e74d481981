package com.faw.sa0214.tak.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.DevopsFeignClient;
import com.faw.sa0214.tak.client.MetaDataFeignClient;
import com.faw.sa0214.tak.client.WatchFeignClient;
import com.faw.sa0214.tak.client.dto.devops.*;
import com.faw.sa0214.tak.client.dto.meta.MetaDataRequest;
import com.faw.sa0214.tak.client.dto.meta.MetaDataResponse;
import com.faw.sa0214.tak.client.dto.meta.RectificationRequest;
import com.faw.sa0214.tak.client.dto.meta.RectificationResponse;
import com.faw.sa0214.tak.client.dto.metric.MetricRestApiParam;
import com.faw.sa0214.tak.client.dto.metric.MetricScoreDTO;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.common.constant.enums.ErrorCodeEnum;
import com.faw.sa0214.tak.common.constant.enums.devops.ScrumItemTypeEnum;
import com.faw.sa0214.tak.common.constant.enums.metadata.MetaDataTeamTransferEnum;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.JsonUtil;
import com.faw.sa0214.tak.mapper.*;
import com.faw.sa0214.tak.model.base.TakException;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.po.*;
import com.faw.sa0214.tak.service.DevopsSyncService;
import com.faw.sa0214.tak.service.TakBusinessService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2024/08/08
 */
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
@Slf4j
public class DevopsSyncServiceImpl implements DevopsSyncService {

    private final TakBusinessService takBusinessService;
    private final AutoCaseMapper autoCaseMapper;
    private final TestCaseMapper testCaseMapper;
    private final TbDyHoursMapper tbDyHoursMapper;
    private final TbDySprintMapper tbSprintMapper;
    private final SmokeTestMapper smokeTestMapper;
    private final TbDyProjectMapper tbDyProjectMapper;
    private final SysTeamUserMapper sysTeamUserMapper;
    private final PlatformIssueMapper platformIssueMapper;
    private final TbDyProjectErrorMapper projectErrorMapper;
    private final TbDyProjectExtendMapper tbDyProjectExtendMapper;

    private final DevopsFeignClient devopsFeignClient;
    private final MetaDataFeignClient metaDataFeignClient;
    private final WatchFeignClient watchFeignClient;
    private final AppPerformanceMetricMapper appPerformanceMetricMapper;
    private final RequirementMapMapper requirementMapMapper;
    private final RequirementMapExternalMapper requirementMapExternalMapper;

    private final static String TB_DY_PROJECT = "tb_dy_project";
    private final static String TB_DY_HOURS = "tb_dy_hours";
    private final static String TB_DY_PROJECT_EXTEND = "tb_dy_project_extend";
    private final static String REQUIREMENT_MAP = "requirement_map";
    private final static String REQUIREMENT_MAP_EXTERNAL = "requirement_map_external";

    private final static List<Integer> DELIVERABLES = Arrays.asList(2, 15, 16, 22, 23, 49, 55, 56);
    private final static Set<String> CSP_SYSTEM = Set.of("SA-0514", "SA-0401", "SA-0402", "SA-0403", "SA-0404", "SA-0405",
            "SA-0201", "SA-0202", "SA-0208", "SA-0212", "SA-0214", "SA-0101", "SA-0103", "SA-0105", "SA-0106", "MC-0001");


    /**
     * 记录devops拉取的敏捷事项（当前拉取的所有项目、所有人员）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Override
    @Async("createThreadPoolExecutor")
    public void recordDevopsScrum(String startTime, String endTime) {

        // 查询待同步的项目信息
        List<TakBusiness> businessList = takBusinessService.getBusinessData();
        List<Long> busIdList = businessList.stream().map(TakBusiness::getBusid).distinct().toList();

        // 定时任务以每天为基准拉取任务，错误记入error表中
        try {
            insertTbProject(busIdList, startTime, endTime, null);
            log.info("schedule task--------------------------------record devops scrums success, record date:{}", startTime);
        } catch (Exception e) {
            log.info("schedule task--------------------------------record devops scrums failed, record date:{}, error:", startTime, e);
            TbDyProjectError tbDyProjectError = new TbDyProjectError();
            tbDyProjectError.setProjectTime(startTime);
            if (e.getMessage().length() > 250) {
                tbDyProjectError.setErrorMsg(e.getMessage().substring(0, 250));
            } else {
                tbDyProjectError.setErrorMsg(e.getMessage());
            }
            projectErrorMapper.insert(tbDyProjectError);
        }
    }

    /**
     * 记录devops拉取的敏捷事项
     *
     * @param busIds    项目ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userIds   用户 ID
     */
    @Override
    public void insertTbProject(List<Long> busIds, String startTime, String endTime, List<String> userIds) {

        busIds.forEach(k -> {

            // 单个项目敏捷事项获取
            ScrumItemExtendDTO scrumItemExtend = sendApiByBusinessId(k, startTime, endTime, userIds);

            // 转换
            List<TbDyProjectPO> scrumRecords = transferScrumRecords(scrumItemExtend);

            // 持久化
            handleRecords(scrumRecords);

        });
    }

    /**
     * 持久化拉回的敏捷事项
     *
     * @param scrumRecords Scrum 记录
     */
    private void handleRecords(List<TbDyProjectPO> scrumRecords) {
        if (CollectionUtils.isEmpty(scrumRecords)) {
            return;
        }

        // 去重处理：保留最后出现的itemId对应的对象
        Map<Long, TbDyProjectPO> projectMap = new LinkedHashMap<>();
        scrumRecords.forEach(project -> projectMap.put(project.getItemId(), project));
        List<TbDyProjectPO> uniqueProjects = new ArrayList<>(projectMap.values());

        // 提取所有itemId
        List<Long> itemIds = uniqueProjects.stream()
                .map(TbDyProjectPO::getItemId)
                .collect(Collectors.toList());

        // 批量删除旧数据、插入新数据
        if (CollectionUtils.isNotEmpty(itemIds)) {
            tbDyProjectMapper.deleteByScrumIds(itemIds);

            // 分批插入配置（每批1000条）
            int batchSize = CommonConstant.INTEGER_1000;
            int total = uniqueProjects.size();
            for (int i = 0; i < total; i += batchSize) {
                int end = Math.min(i + batchSize, total);
                List<TbDyProjectPO> batchList = uniqueProjects.subList(i, end);
                tbDyProjectMapper.batchInsert(batchList);
            }
        }
    }

    /**
     * 按照项目id获取大禹敏捷事项数据
     *
     * @param id        项目id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userIds   用户 ID
     * @return {@link ScrumItemExtendDTO}
     */
    private ScrumItemExtendDTO sendApiByBusinessId(Long id, String startTime, String endTime, List<String> userIds) {
        ScrumItemExtendDTO dto = new ScrumItemExtendDTO();
        List<ScrumItemDTO> scrumItems = new ArrayList<>();
        List<ScrumItemExtendFieldDTO> extendList = new ArrayList<>();
        Set<String> existingIds = new HashSet<>();

        for (String type : CommonConstant.SCRUM_ITEM_TYPE) {
            List<ScrumItemExtendFieldDTO> extendFieldList = new ArrayList<>();
            List<ScrumItemDTO> items = processScrumItemType(id, type, startTime, endTime, userIds, extendFieldList);
            scrumItems.addAll(items);

            for (ScrumItemExtendFieldDTO field : extendFieldList) {
                String fieldId = field.getFiledKey();
                if (existingIds.add(fieldId)) {
                    extendList.add(field);
                }
            }
        }

        dto.setList(scrumItems);
        dto.setExtendList(extendList);
        return dto;
    }

    /**
     * 处理单个Scrum事项类型的所有分页数据
     */
    private List<ScrumItemDTO> processScrumItemType(Long projectId, String itemType, String startTime,
                                                    String endTime, List<String> userIds, List<ScrumItemExtendFieldDTO> extendList) {
        ScrumItemListRequest request = buildBaseRequest(projectId, itemType, startTime, endTime, userIds);
        List<ScrumItemDTO> items = new ArrayList<>();
        fetchAllPages(request, items, extendList);
        return items;
    }

    /**
     * 分页获取所有数据（重试3次）
     */
    private void fetchAllPages(ScrumItemListRequest request, List<ScrumItemDTO> resultContainer, List<ScrumItemExtendFieldDTO> extendList) {
        final int maxAttempts = CommonConstant.INTEGER_4;
        int currentPage = request.getPageNum();
        Integer totalPages = null;
        int pageSize = request.getPageSize();

        do {
            // 分页请求状态控制
            boolean pageSuccess = false;
            PageResult<ScrumItemDTO> pageData = null;

            // 带重试的分页请求
            for (int attempt = CommonConstant.INTEGER_1; attempt <= maxAttempts; attempt++) {
                try {
                    request.setPageNum(currentPage);
                    log.info("请求Scrum事项分页数据 [类型: {}, 页码: {}, 页大小: {}]", request.getItemType(), currentPage, pageSize);

                    Result<PageResult<ScrumItemDTO>> result = devopsFeignClient.getScrumItems(request);

                    if (result.isSuccess() && result.getData() != null) {
                        pageData = result.getData();
                        pageSuccess = true;
                        break;  // 请求成功跳出重试循环
                    }

                    log.warn("项目{}, 类型: {}, 第{}页请求失败 (尝试{}): {}", request.getBusinessProjectId(), request.getItemType(),
                            currentPage, attempt, result.getMessage() != null ? result.getMessage() : "无错误信息");
                } catch (Exception e) {
                    log.warn("项目{}, 第{}页请求异常 (尝试{}): {}", request.getBusinessProjectId(), currentPage, attempt,
                            e.getClass().getSimpleName(), e);
                }
            }

            // 分页请求失败处理
            if (!pageSuccess) {
                log.warn("项目{}, 类型: {}, 第{}页请求失败已达最大重试次数，终止分页获取", request.getBusinessProjectId(), request.getItemType(),
                        currentPage);
                return;
            }

            // 初始化总页数 (仅首次请求时计算)
            if (totalPages == null) {
                Long totalItems = pageData.getTotal() != null ? pageData.getTotal() : 0L;
                totalPages = calculateTotalPages(totalItems, pageSize);

                if (totalPages == 0) {
                    log.info("未查询到有效数据");
                    return;
                }
            }

            // 空数据页处理
            if (CollectionUtils.isEmpty(pageData.getList())) {
                log.info("第{}页返回空数据列表，继续下一页请求", currentPage);
                currentPage++;
                continue;
            }

            // 扩展数据处理 (仅首次有效分页处理)
            List<ScrumItemExtendFieldDTO> extendFieldList = Optional.ofNullable(pageData.getExtend())
                    .map(extendObj -> {
                        try {
                            return JsonUtil.toJsonString(extendObj);
                        } catch (Exception e) {
                            return null;
                        }
                    })
                    .map(jsonString -> {
                        try {
                            return JSON.parseArray(jsonString, ScrumItemExtendFieldDTO.class);
                        } catch (Exception e) {
                            return Collections.<ScrumItemExtendFieldDTO>emptyList(); // 返回空列表
                        }
                    })
                    .orElse(Collections.emptyList());
            if (CollectionUtil.isNotEmpty(extendFieldList)) {
                extendList.addAll(extendFieldList);
            }

            // 过滤非本项目的数据 合并数据
            List<ScrumItemDTO> dataList = pageData.getList().stream()
                    .filter(s -> Objects.equals(request.getBusinessProjectId(), s.getBusinessProjectId())).toList();
            resultContainer.addAll(dataList);
            log.info("已合并第{}页数据，当前总数: {}", currentPage, resultContainer.size());

            // 页码控制
            if (currentPage >= totalPages) {
                log.info("已完成全部分页请求，总页数: {}", totalPages);
                break;
            }

            currentPage++;
        } while (currentPage <= totalPages);
    }

    /**
     * 分页计算逻辑
     */
    private int calculateTotalPages(Long totalItems, int pageSize) {
        return (totalItems == 0 || pageSize == 0) ? 0 : (int) Math.ceil((double) totalItems / pageSize);
    }


    /**
     * 构建请求体
     *
     * @param projectId 项目 ID
     * @param itemType  项目类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userIds   用户 ID
     * @return {@link ScrumItemListRequest }
     */
    private ScrumItemListRequest buildBaseRequest(Long projectId, String itemType,
                                                  String startTime, String endTime,
                                                  List<String> userIds) {
        ScrumItemListRequest request = new ScrumItemListRequest();
        request.setPageNum(CommonConstant.INTEGER_1);
        request.setPageSize(CommonConstant.INTEGER_100);
        request.setBusinessProjectId(projectId);
        request.setItemType(ScrumItemTypeEnum.valueOf(itemType));
        request.setPeriodFlag("M");
        request.setEditTimeFrom(startTime);
        request.setEditTimeTo(endTime);
        request.setCreateTimeFrom(startTime);
        request.setCreateTimeTo(endTime);
        if (CollectionUtils.isNotEmpty(userIds)) {
            request.setAssignedUserIds(userIds);
        }

        return request;
    }

    /**
     * 转换为大禹事项表的持久化对象
     *
     * @param scrumItemExtend 敏捷事项拓展属性
     */
    private List<TbDyProjectPO> transferScrumRecords(ScrumItemExtendDTO scrumItemExtend) {

        List<ScrumItemDTO> scrumList = Optional.ofNullable(scrumItemExtend.getList())
                .orElse(Collections.emptyList());

        List<TbDyProjectPO> dyProjects = new ArrayList<>();
        for (ScrumItemDTO scrumItem : scrumList) {
            TbDyProjectPO tbDyProject = new TbDyProjectPO();
            tbDyProject.setGmtCreate(scrumItem.getGmtCreated());
            tbDyProject.setGmtModified(scrumItem.getGmtModified());
            tbDyProject.setProject(scrumItem.getProjectType());
            tbDyProject.setItemId(scrumItem.getId());
            tbDyProject.setItemTitle(scrumItem.getSubject());
            tbDyProject.setType(scrumItem.getType());
            tbDyProject.setStatus(scrumItem.getStatusType());
            tbDyProject.setPriority(scrumItem.getPriority());
            tbDyProject.setAuthor(scrumItem.getCreatorId());
            tbDyProject.setCreator(scrumItem.getCreatorAccount());
            tbDyProject.setAssignedUserId(scrumItem.getAssignedUserId());
            tbDyProject.setAssignedUserName(scrumItem.getAssignedUserAccount());
            tbDyProject.setParentRequirementId(scrumItem.getParentId());
            tbDyProject.setStatusName(scrumItem.getStatusName());
            tbDyProject.setPredictHours(scrumItem.getPredictHours());
            tbDyProject.setActualHours(scrumItem.getActualHours());
            tbDyProject.setBusId(scrumItem.getBusinessProjectId());
            tbDyProject.setStartTime(scrumItem.getStartTime());
            tbDyProject.setEndTime(scrumItem.getEndTime());
            tbDyProject.setActualStartTime(scrumItem.getActualStartTime());
            tbDyProject.setActualEndTime(scrumItem.getActualEndTime());
            tbDyProject.setIteration(scrumItem.getSprintId());
            if (scrumItem.getLocked() != null) {
                tbDyProject.setLocked(scrumItem.getLocked() ? CommonConstant.INTEGER_1 : CommonConstant.INTEGER_0);
            }
            tbDyProject.setDefectType(scrumItem.getDefectType());
            tbDyProject.setEnvironment(scrumItem.getEnvironment());
            tbDyProject.setSerioutLevel(scrumItem.getSeriousLevel());
            tbDyProject.setRiskLevel(scrumItem.getRiskRating());
            tbDyProject.setRiskType(scrumItem.getRiskType());
            tbDyProject.setRiskImpack(scrumItem.getRiskImpact());
            tbDyProject.setStoryPoint(scrumItem.getStoryPoint());

            tbDyProject.setCompleteTime(scrumItem.getFirstDoneTime());
            tbDyProject.setReopenTimes(scrumItem.getReopenNum());
            tbDyProject.setReviewRounds(scrumItem.getReviewNum());

            // 处理自定义字段属性
            handleExtendFields(tbDyProject, scrumItem, scrumItemExtend);

            dyProjects.add(tbDyProject);

        }

        return dyProjects;
    }

    /**
     * 处理扩展字段
     *
     * @param tbDyProject 大禹敏捷事项 PO
     * @param scrumItem   敏捷事项
     */
    private void handleExtendFields(TbDyProjectPO tbDyProject, ScrumItemDTO scrumItem, ScrumItemExtendDTO scrumItemExtend) {

        List<ScrumItemExtendFieldDTO> extendList = Optional.ofNullable(scrumItemExtend.getExtendList())
                .orElse(Collections.emptyList());

        Map<String, String> customizeFields = scrumItem.getCustomizeFields();

        Map<String, String> fieldMeta = new HashMap<>();
        for (ScrumItemExtendFieldDTO dto : extendList) {
            fieldMeta.put(dto.getFiledKey(), dto.getName());
        }

        if (customizeFields == null) {
            return;
        }

        Map<String, String> customerizeMap = new HashMap<>();
        for (Map.Entry<String, String> entry : customizeFields.entrySet()) {
            String oldKey = entry.getKey();
            String newKey = fieldMeta.get(oldKey);
            customerizeMap.put(newKey, entry.getValue());
        }

        String itBuField = customerizeMap.get("IT业务单元");
        String buField = customerizeMap.get("业务单元");

        if (StringUtils.isBlank(itBuField)) {
            tbDyProject.setBusinessUnit(buField);
        } else {
            tbDyProject.setBusinessUnit(itBuField);
        }

        String standTime = customerizeMap.get("标准工时");
        tbDyProject.setStandardTime(standTime);

    }


    /**
     * 记录devops拉取的工时（当前拉取的所有项目、所有人员）（异步）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Override
    @Async("createThreadPoolExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void recordDevopsHours(String startTime, String endTime) {
        recordHours(startTime, endTime);
    }

    /**
     * 记录devops拉取的工时（当前拉取的所有项目、所有人员）（同步）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Override
    public void recordHours(String startTime, String endTime) {
        // 查询待同步的项目信息
        List<TakBusiness> businessList = takBusinessService.getBusinessData();
        List<Long> busIdList = businessList.stream().map(TakBusiness::getBusid).distinct().toList();

        insertTbDyHours(busIdList, startTime, endTime, null);
        log.info("schedule task--------------------------------record devops work hour success, start date:{}", startTime);
    }

    /**
     * 记录devops拉取的工时数据
     *
     * @param busIds    项目ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userMap   用户映射
     */
    @Override
    public void insertTbDyHours(List<Long> busIds, String startTime, String endTime, Map<String, String> userMap) {
        busIds.forEach(busId -> {

            // 单个项目工时获取
            List<ScrumHourLogDTO> scrumHourList = getScrumHourByBusinessId(busId, startTime, endTime, userMap);

            // 转换
            List<TbDyHourPO> scrumRecords = transferScrumHourRecords(scrumHourList);

            // 持久化
            handleHourRecords(busId, startTime, endTime, scrumRecords, userMap);

            log.info("项目:{}, 日期：{} - {}，工时处理完毕", busId, startTime, endTime);
        });
    }

    /**
     * 数据持久化
     *
     * @param busId        项目 ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param scrumRecords 工时记录
     * @param userMap      用户映射
     */
    private void handleHourRecords(Long busId, String startTime, String endTime, List<TbDyHourPO> scrumRecords,
                                   Map<String, String> userMap) {

        if (CollectionUtils.isEmpty(scrumRecords)) {
            return;
        }

        List<String> accountList = new ArrayList<>();
        if (MapUtil.isNotEmpty(userMap)) {
            accountList = new ArrayList<>(userMap.values());
        }
        String startDateStr = startTime + " 00:00:00";
        String endDateStr = endTime + " 23:59:59";
        List<TbDyHours> lists = tbDyHoursMapper.selectByDateAndBus(busId, startDateStr, endDateStr, accountList);

        // 批量删除旧数据
        List<Long> ids = lists.stream().map(TbDyHours::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(ids)) {
            tbDyHoursMapper.deleteByIds(ids);
        }

        // 新增数据
        tbDyHoursMapper.batchInsert(scrumRecords);
    }

    /**
     * 工时实体映射转换
     *
     * @param scrumHourList Scrum 小时列表
     * @return {@link List }<{@link TbDyHourPO }>
     */
    private List<TbDyHourPO> transferScrumHourRecords(List<ScrumHourLogDTO> scrumHourList) {

        List<TbDyHourPO> records = new ArrayList<>();

        for (ScrumHourLogDTO scrumHourLog : scrumHourList) {
            TbDyHourPO tbDyHour = new TbDyHourPO();
            tbDyHour.setGmtCreate(scrumHourLog.getDate());
            tbDyHour.setScrumItemId(scrumHourLog.getItemId());
            if (scrumHourLog.getWorkDescription().length() > 2048) {
                tbDyHour.setWorkDescription(scrumHourLog.getWorkDescription().substring(0, 2048));
            } else {
                tbDyHour.setWorkDescription(scrumHourLog.getWorkDescription());
            }
            tbDyHour.setApplicantUserName(scrumHourLog.getApplicantUserName());
            tbDyHour.setModiferName(scrumHourLog.getSubject());
            tbDyHour.setBusId(scrumHourLog.getBusinessProjectId());
            tbDyHour.setWorkType(scrumHourLog.getProjectName());
            tbDyHour.setConsumeHours(scrumHourLog.getConsumeHours());

            records.add(tbDyHour);
        }

        return records;
    }

    /**
     * 按照项目id获取大禹工时数据
     *
     * @param busId     项目 ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userMap   用户映射
     * @return {@link List }<{@link ScrumHourLogDTO }>
     */
    private List<ScrumHourLogDTO> getScrumHourByBusinessId(Long busId, String startTime, String endTime, Map<String, String> userMap) {
        List<ScrumHourLogDTO> scrumHourList = new ArrayList<>();
        ScrumItemHourRequest request = createInitialRequest(busId, startTime, endTime, userMap);

        Result<PageResult<ScrumHourLogDTO>> firstPageResult = devopsFeignClient.getScrumHours(request);
        if (!firstPageResult.isSuccess() || firstPageResult.getData() == null) {
            log.warn("获取项目工时失败或结果为空，对应项目为: {}", busId);
            return scrumHourList;
        }

        PageResult<ScrumHourLogDTO> firstPageData = firstPageResult.getData();
        List<ScrumHourLogDTO> list = firstPageData.getList();
        if (CollectionUtil.isEmpty(list)) {
            return scrumHourList;
        }
        scrumHourList.addAll(list);

        long totalRecords = firstPageData.getTotal();
        int pageSize = request.getPageSize();
        int totalPages = calculateTotalPages(totalRecords, pageSize);

        fetchRemainingPages(request, totalPages, scrumHourList);

        return scrumHourList;
    }

    /**
     * 构建工时查询首次请求的请求体
     *
     * @param busId     项目 ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userMap   用户映射
     * @return {@link ScrumItemHourRequest }
     */
    private ScrumItemHourRequest createInitialRequest(Long busId, String startTime, String endTime, Map<String, String> userMap) {
        ScrumItemHourRequest request = new ScrumItemHourRequest();
        request.setBusinessProjectId(busId);
        request.setPageNum(CommonConstant.INTEGER_1);
        request.setPageSize(CommonConstant.INTEGER_100);
        request.setStartDate(startTime);
        request.setEndDate(endTime);

        if (MapUtil.isNotEmpty(userMap)) {
            request.setApplicantUserIds(new ArrayList<>(userMap.keySet()));
        }
        return request;
    }

    /**
     * 获取剩余分页页面
     *
     * @param request         请求
     * @param totalPages      总页数
     * @param resultContainer 结果
     */
    private void fetchRemainingPages(ScrumItemHourRequest request, int totalPages, List<ScrumHourLogDTO> resultContainer) {
        for (int currentPage = CommonConstant.INTEGER_2; currentPage <= totalPages; currentPage++) {
            request.setPageNum(currentPage);
            Result<PageResult<ScrumHourLogDTO>> pageResult = devopsFeignClient.getScrumHours(request);

            if (pageResult.isSuccess() && pageResult.getData() != null) {
                List<ScrumHourLogDTO> list = pageResult.getData().getList();
                if (CollectionUtil.isNotEmpty(list)) {
                    resultContainer.addAll(list);
                }
            } else {
                log.warn("获取项目工时失败或结果为空，当前分页为：{}，对应项目为: {}", currentPage, request.getBusinessProjectId());
            }
        }
    }

    /**
     * 记录devops拉取的敏捷事项（当前拉取的所有项目、所有人员）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordDevopsSprint(String startTime, String endTime) {
        // 查询待同步的项目信息
        List<TakBusiness> businessList = takBusinessService.getBusinessData();
        List<Long> busIdList = businessList.stream().map(TakBusiness::getBusid).distinct().toList();

        insertTbDySprint(busIdList, startTime, endTime, CommonConstant.INTEGER_2);
        log.info("schedule task--------------------------------record devops sprint success, start date:{}", startTime);
    }

    /**
     * 记录devops拉取的迭代数据
     *
     * @param busIds    项目 ID 列表
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param dateFlag  日期标识， 1：表示按迭代创建时间，2：表示按迭代修改时间，3：表示按迭代开始时间
     */
    @Override
    public void insertTbDySprint(List<Long> busIds, String startTime, String endTime, Integer dateFlag) {
        if (dateFlag == null) {
            dateFlag = CommonConstant.INTEGER_2;
        }

        List<String[]> dateRanges = new ArrayList<>();

        if (Objects.equals(dateFlag, CommonConstant.INTEGER_1) || Objects.equals(dateFlag, CommonConstant.INTEGER_2)) {
            // 生成每日时间范围列表
            DateUtils.periodDays(startTime, endTime).forEach(day -> dateRanges.add(new String[]{day, day}));
        } else {
            // 添加完整时间范围
            dateRanges.add(new String[]{startTime, endTime});
        }

        for (String[] range : dateRanges) {
            String rangeStart = range[0];
            String rangeEnd = range[1];

            for (Long busId : busIds) {
                // 单个项目迭代获取
                List<ProjectSprintDTO> scrumHourList = getSprintByBusinessId(busId, rangeStart, rangeEnd, dateFlag);

                // 转换
                List<TbDySprintPO> sprintRecords = transferSprintRecords(scrumHourList);

                // 持久化
                handleSprintRecords(busId, startTime, endTime, sprintRecords);

                log.info("项目:{}, 日期：{} - {}，冲刺处理完毕", busId, rangeStart, rangeEnd);
            }
        }
    }

    /**
     * 数据持久化
     *
     * @param busId         项目 ID
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param sprintRecords 迭代记录
     */
    private void handleSprintRecords(Long busId, String startTime, String endTime, List<TbDySprintPO> sprintRecords) {
        if (CollectionUtils.isEmpty(sprintRecords)) {
            return;
        }

        // 提取所有itemId
        Set<Long> sprintIds = sprintRecords.stream()
                .map(TbDySprintPO::getSprintId)
                .collect(Collectors.toSet());

        // 批量删除旧数据、批量插入新数据
        if (CollectionUtils.isNotEmpty(sprintIds)) {
            tbSprintMapper.deleteByBusIdAndSprintIds(busId, sprintIds);
            tbSprintMapper.batchInsert(sprintRecords);
        }
    }

    private List<TbDySprintPO> transferSprintRecords(List<ProjectSprintDTO> scrumHourList) {
        if (CollectionUtils.isEmpty(scrumHourList)) {
            return Collections.emptyList();
        } else {
            List<TbDySprintPO> sprintRecords = new ArrayList<>();
            for (ProjectSprintDTO projectSprintDTO : scrumHourList) {
                TbDySprintPO sprintRecord = new TbDySprintPO();
                sprintRecord.setBusId(projectSprintDTO.getBusinessProjectId());
                sprintRecord.setSprintId(projectSprintDTO.getId());
                sprintRecord.setSprintName(projectSprintDTO.getSprintName());
                sprintRecord.setSprintStatus(projectSprintDTO.getStatus());
                sprintRecord.setAssignedUserId(projectSprintDTO.getAssignedAccount());
                sprintRecord.setStartTime(projectSprintDTO.getStartTime());
                sprintRecord.setEndTime(projectSprintDTO.getEndTime());
                sprintRecord.setGmtCreated(projectSprintDTO.getGmtCreated());
                sprintRecord.setGmtModified(projectSprintDTO.getGmtModified());
                sprintRecords.add(sprintRecord);
            }
            return sprintRecords;
        }
    }

    private List<ProjectSprintDTO> getSprintByBusinessId(Long busId, String startTime, String endTime, Integer dateFlag) {
        List<ProjectSprintDTO> scrumHourList = new ArrayList<>();

        ProjectSprintRequest request = new ProjectSprintRequest();
        request.setPageNum(CommonConstant.INTEGER_1);
        request.setPageSize(CommonConstant.INTEGER_100);

        request.setBusinessProjectId(busId);
        // 1：表示按迭代创建时间，2：表示按迭代修改时间，3：表示按迭代开始时间
        if (Objects.equals(dateFlag, CommonConstant.INTEGER_1)) {
            request.setCreatDate(startTime);
        } else if (Objects.equals(dateFlag, CommonConstant.INTEGER_2)) {
            request.setUpdateDate(startTime);
        } else if (Objects.equals(dateFlag, CommonConstant.INTEGER_3)) {
            request.setStartTimeFrom(startTime);
            request.setStartTimeTo(endTime);
        }

        Result<PageResult<ProjectSprintDTO>> result = devopsFeignClient.getSprintInfo(request);

        if (result.isSuccess()) {
            PageResult<ProjectSprintDTO> data = result.getData();
            Long total = data.getTotal();
            List<ProjectSprintDTO> scrumHours = data.getList();
            scrumHourList.addAll(scrumHours);
            long pageTotal = total % CommonConstant.INTEGER_100 == 0 ? (total / CommonConstant.INTEGER_100) : (total / CommonConstant.INTEGER_100 + 1);
            if (pageTotal > 1) {
                for (int i = 2; i <= pageTotal; i++) {
                    request.setPageNum(i);
                    Result<PageResult<ProjectSprintDTO>> resultNext = devopsFeignClient.getSprintInfo(request);
                    PageResult<ProjectSprintDTO> dataNext = resultNext.getData();
                    List<ProjectSprintDTO> scrumHoursNext = dataNext.getList();
                    scrumHourList.addAll(scrumHoursNext);
                }
            }
        }
        return scrumHourList;
    }


    @Override
    public void syncDeletedItems(String day) {

        // 获取当天的起止日期
        LocalDate date = LocalDate.parse(day);
        LocalDateTime startOfDay = LocalDateTime.of(date, LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.of(date, LocalTime.MAX);
        Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

        // RPC调用获取删除的事项
        DevopsSearchDTO dto = new DevopsSearchDTO();
        dto.setStartTime(startDate);
        dto.setEndTime(endDate);

        Result<List<Long>> deletedTasks = devopsFeignClient.getDeletedTasks(dto);
        if (deletedTasks == null || deletedTasks.getCode() != ErrorCodeEnum.SUCCESS.getCode()) {
            log.warn("RPC调用获取删除的事项失败");
            return;
        }
        List<Long> itemIds = deletedTasks.getData();
        if (itemIds == null || itemIds.isEmpty()) {
            log.info("没有找到当天删除的事项");
            return;
        }

        // 更新事项表及工时表
        log.info("update scrum ids: {}", JsonUtil.toJsonString(itemIds));
        updateTable(TB_DY_PROJECT, itemIds);
        updateTable(TB_DY_HOURS, itemIds);
        updateTable(TB_DY_PROJECT_EXTEND, itemIds);
        updateTable(REQUIREMENT_MAP, itemIds);
        updateTable(REQUIREMENT_MAP_EXTERNAL, itemIds);

    }

    private void updateTable(String tableName, List<Long> itemIds) {
        try {
            if (TB_DY_PROJECT.equals(tableName)) {
                tbDyProjectMapper.deleteByScrumIds(itemIds);
            } else if (TB_DY_HOURS.equals(tableName)) {
                tbDyHoursMapper.deleteByScrumIds(itemIds);
            } else if (TB_DY_PROJECT_EXTEND.equals(tableName)) {
                tbDyProjectExtendMapper.deleteByScrumIds(itemIds);
            } else if (REQUIREMENT_MAP.equals(tableName)) {
                requirementMapMapper.deleteByScrumIds(itemIds);
            } else if (REQUIREMENT_MAP_EXTERNAL.equals(tableName)) {
                requirementMapExternalMapper.deleteByScrumIds(itemIds);
            }
        } catch (Exception e) {
            log.error("更新{}表失败, ids={}", tableName, itemIds, e);
        }
    }

    /**
     * 获取 AI 任务信息列表
     *
     * @param startDateString 开始日期字符串
     * @param endDateString   结束日期字符串
     * @return {@link Integer }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer getAiTaskInfoList(String startDateString, String endDateString) {
        // 获取规定天数内的ai任务数据
        List<AiSprintTaskDTO> aiTaskList = getDyAiTaskInfoList(startDateString, endDateString);

        // 封装持久化请求体
        List<TbDyProjectExtendPO> extendList = generateProjectExtend(aiTaskList);

        // 更新数据
        return saveOrUpdate(extendList);
    }

    /**
     * 保存或更新大禹 AI 任务数据
     *
     * @param extendList 大禹任务拓展属性列表
     * @return {@link Integer}
     */
    private Integer saveOrUpdate(List<TbDyProjectExtendPO> extendList) {

        if (CollectionUtil.isEmpty(extendList)) {
            return 0;
        }

        List<TbDyProjectExtendPO> filterExtendList = extendList.stream()
                .filter(projectExtend -> projectExtend.getScrumId() != null)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(filterExtendList)) {
            log.info("The data synchronized from devops is empty after filtering!");
            return 0;
        }

        // 删除之前同步的旧信息
        List<Long> scrumIds = filterExtendList.stream().map(TbDyProjectExtendPO::getScrumId).collect(Collectors.toList());
        int deletedCount = tbDyProjectExtendMapper.deleteByScrumIds(scrumIds);
        log.info("TbDyProjectExtendServiceImpl.#saveOrUpdate, delete old data number:{}", deletedCount);

        // 插入新的同步信息
        int addCount = 0;
        for (int i = 0; i < filterExtendList.size(); ) {
            addCount += tbDyProjectExtendMapper.batchInsert(filterExtendList.subList(i, Math.min(i + 50, filterExtendList.size())));
            i += 50;
        }
        log.info("TbDyProjectExtendServiceImpl.#saveOrUpdate, save or update data number:{}", addCount);

        return addCount;
    }

    /**
     * 转换持久化对象
     *
     * @param aiTaskList AI任务列表
     * @return {@link List}<{@link TbDyProjectExtendPO}>
     */
    private List<TbDyProjectExtendPO> generateProjectExtend(List<AiSprintTaskDTO> aiTaskList) {

        List<TbDyProjectExtendPO> projectExtendList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(aiTaskList)) {
            aiTaskList.forEach(aiSprintTaskDTO -> {
                Integer deliverableItemId = aiSprintTaskDTO.getDeliverableItemId();

                TbDyProjectExtendPO project = new TbDyProjectExtendPO();
                project.setScrumId(aiSprintTaskDTO.getRelatedItemTaskId());
                project.setScrumType(aiSprintTaskDTO.getType());
                project.setDeliverableItemId(deliverableItemId);
                project.setAssignedUserId(aiSprintTaskDTO.getAssignedUserId());
                project.setRelatedTaskBill(aiSprintTaskDTO.getRelatedTaskBill());
                project.setArchitectureFirstLevel(aiSprintTaskDTO.getArchitectureFirstLevelName());
                project.setArchitectureSecondLevel(aiSprintTaskDTO.getArchitectureSecondLevelName());
                project.setArchitectureLevel(aiSprintTaskDTO.getArchitectureLevel());
                project.setArchitectureType(aiSprintTaskDTO.getArchitectureType());

                // 后端代码特殊处理，没有上线代码评分的赋值默认80分
                if (DELIVERABLES.contains(deliverableItemId) && aiSprintTaskDTO.getAiScore() == null) {
                    project.setAiScore(80.0);
                    if (aiSprintTaskDTO.getAiEvaluation() == null) {
                        project.setAiEvaluation("默认分");
                    }
                } else {
                    project.setAiScore(aiSprintTaskDTO.getAiScore());
                    project.setAiEvaluation(aiSprintTaskDTO.getAiEvaluation());
                }
                project.setGmtCreated(aiSprintTaskDTO.getGmtCreated());
                project.setGmtModified(aiSprintTaskDTO.getGmtModified());

                projectExtendList.add(project);
            });
        }

        return projectExtendList;
    }

    /**
     * 获取大禹 AI 任务数据
     *
     * @param startDateString 开始日期字符串
     * @param endDateString   结束日期字符串
     * @return {@link List}<{@link AiSprintTaskDTO}>
     */
    private List<AiSprintTaskDTO> getDyAiTaskInfoList(String startDateString, String endDateString) {

        // 请求参数构建
        AiTaskRequestDTO dto = new AiTaskRequestDTO();
        dto.setModifiedTimeFrom(startDateString);
        dto.setModifiedTimeTo(endDateString);
        dto.setDateTypeFlag(1);
        dto.setPageNum(1);
        dto.setPageSize(CommonConstant.INTEGER_100);

        List<AiSprintTaskDTO> aiTasks = new ArrayList<>();

        // 分页多次请求数据
        Long total = getAiTasks(aiTasks, dto);
        if (total / CommonConstant.INTEGER_100 >= 1) {
            for (int i = CommonConstant.INTEGER_2; i < total / CommonConstant.INTEGER_100 + CommonConstant.INTEGER_2; i++) {
                dto.setPageNum(i);
                getAiTasks(aiTasks, dto);
            }
        }

        return aiTasks;

    }

    /**
     * RPC调用，获取 AI 任务列表
     *
     * @param dto     请求体参数
     * @param aiTasks AI 任务列表
     * @return {@link Long}
     */
    private Long getAiTasks(List<AiSprintTaskDTO> aiTasks, AiTaskRequestDTO dto) {
        try {
            log.info("getTaskInfo, request:{}", JsonUtil.toJsonString(dto));
            Result<PageResult<AiSprintTaskDTO>> response = devopsFeignClient.getTaskInfo(dto);
            log.info("getTaskInfo, response:{}", JsonUtil.toJsonString(response));
            if (response.isSuccess()) {
                PageResult<AiSprintTaskDTO> data = response.getData();
                aiTasks.addAll(data.getList());
                return data.getTotal();
            } else {
                throw new TakException("RPC调用返回失败，失败消息：" + response.getMessage());
            }
        } catch (Exception e) {
            log.error("RPC ERROR!", e);
            throw new TakException("开放API的RPC调取失败，请确认服务！");
        }
    }

    /**
     * 同步自动化测试用例信息
     *
     * @param busIdList 项目Id列表
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void syncTbDyAutoCase(List<Long> busIdList, String startDate, String endDate) {

        for (Long busId : busIdList) {
            List<String> caseTypes = Arrays.asList("usecase", "component");
            for (String caseType : caseTypes) {
                log.info("=== 同步自动化测试用例信息 === syncTbDyAutoCase, busId:{}, caseType:{}, startDate:{}, endDate:{}",
                        busId, caseType, startDate, endDate);

                CaseRequest request = new CaseRequest();
                request.setBusinessProjectId(busId);
                request.setStartTime(startDate);
                request.setEndTime(endDate);
                request.setType(caseType);
                request.setPageNum(CommonConstant.INTEGER_1);
                request.setPageSize(CommonConstant.INTEGER_100);
                Result<PageResult<AutoCaseDTO>> result = devopsFeignClient.getAutoCaseInfo(request);

                if (result.isSuccess()) {
                    PageResult<AutoCaseDTO> data = result.getData();
                    Long total = data.getPagination().getTotal();   // 总数
                    if (total != null && total > 0) {
                        insertAutoCase(data.getList());
                        long pageTotal = total % CommonConstant.INTEGER_100 == 0
                                ? (total / CommonConstant.INTEGER_100)
                                : (total / CommonConstant.INTEGER_100 + 1);
                        if (pageTotal > 1) {
                            for (int i = 2; i <= pageTotal; i++) {
                                request.setPageNum(i);
                                Result<PageResult<AutoCaseDTO>> resultNext = devopsFeignClient.getAutoCaseInfo(request);
                                insertAutoCase(resultNext.getData().getList());
                            }
                        }
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    private void insertAutoCase(List<AutoCaseDTO> autoCaseList) {
        if (CollectionUtil.isEmpty(autoCaseList)) {
            return;
        }

        // 删除历史数据
        List<String> caseIdList = autoCaseList.stream().map(AutoCaseDTO::getCaseId).toList();
        autoCaseMapper.delAutoCase(caseIdList);

        List<TbDyAutoCase> autoCasePOList = new ArrayList<>();
        autoCaseList.forEach(autoCaseDTO -> {
            TbDyAutoCase autoCase = new TbDyAutoCase();
            BeanUtils.copyProperties(autoCaseDTO, autoCase);
            autoCase.setBusinessId(autoCaseDTO.getBusinessProjectId());
            autoCase.setBusinessName(autoCaseDTO.getBusinessProjectName());
            autoCasePOList.add(autoCase);
        });
        autoCaseMapper.insertAutoCaseList(autoCasePOList);
    }

    /**
     * 同步测试用例信息
     *
     * @param busIdList 项目Id列表
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public void syncTbDyTestCase(List<Long> busIdList, String startDate, String endDate) {

        for (Long busId : busIdList) {

            CaseRequest request = new CaseRequest();
            request.setBusinessProjectId(busId);
            request.setStartTime(startDate);
            request.setEndTime(endDate);

            // 查询迭代信息
            List<Long> sprintList = tbSprintMapper.getSprintByDate(request);

            for (Long sprintId : sprintList) {
                List<String> caseTypes = Arrays.asList("reviewplan", "testplan");
                for (String caseType : caseTypes) {
                    log.info("=== 同步测试用例信息 === syncTbDyTestCase, busId:{}, sprintId:{}, caseType:{}", busId, sprintId, caseType);

                    request.setSprintId(sprintId);
                    request.setType(caseType);
                    request.setPageNum(CommonConstant.INTEGER_1);
                    request.setPageSize(CommonConstant.INTEGER_100);
                    Result<PageResult<TestCaseDTO>> result = devopsFeignClient.getTestCaseInfo(request);

                    if (result.isSuccess()) {
                        PageResult<TestCaseDTO> data = result.getData();
                        Long total = data.getPagination().getTotal();   // 总数
                        if (total != null && total > 0) {
                            insertTestCase(data.getList());
                            long pageTotal = total % CommonConstant.INTEGER_100 == 0
                                    ? (total / CommonConstant.INTEGER_100)
                                    : (total / CommonConstant.INTEGER_100 + 1);
                            if (pageTotal > 1) {
                                for (int i = 2; i <= pageTotal; i++) {
                                    request.setPageNum(i);
                                    Result<PageResult<TestCaseDTO>> resultNext = devopsFeignClient.getTestCaseInfo(request);
                                    insertTestCase(resultNext.getData().getList());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTestCase(List<TestCaseDTO> testCaseList) {
        if (CollectionUtil.isEmpty(testCaseList)) {
            return;
        }

        // 删除历史数据
        List<String> caseIdList = testCaseList.stream().map(TestCaseDTO::getId).toList();
        testCaseMapper.delTestCase(caseIdList);

        List<TbDyTestCase> testCasePOList = new ArrayList<>();
        testCaseList.forEach(dto -> {
            TbDyTestCase test = new TbDyTestCase();
            BeanUtils.copyProperties(dto, test);
            test.setCaseId(dto.getId());
            test.setBusId(dto.getBusinessProjectId());
            testCasePOList.add(test);
        });
        testCaseMapper.insertTestCaseList(testCasePOList);
    }

    /**
     * 同步冒烟测试用例信息
     * 开始日期
     * 结束日期
     */
    @Override
    public void syncTbDySmokeTest(SmokeTestRequest request) {
        // 查询人员清单数据
        List<SysTeamUser> userList = sysTeamUserMapper.getUniqueUser(QueryUserDTO.builder().userCodeList(request.getAccountList()).build());
        List<List<String>> accounts = groupLoginAccounts(userList);

        for (List<String> accountList : accounts) {
            request.setAccountList(accountList);
            request.setPageNum(CommonConstant.INTEGER_1);
            request.setPageSize(CommonConstant.INTEGER_100);

            Result<PageResult<SmokeTestDTO>> result = devopsFeignClient.getSomkeTestPlan(request);
            log.info("=== 同步冒烟测试用例信息 === syncTbDySmokeTest, request:{}, result:{}", JsonUtil.toJsonString(request), JsonUtil.toJsonString(result));
            if (result.isSuccess()) {
                PageResult<SmokeTestDTO> data = result.getData();
                Long total = data.getPagination().getTotal();   // 总数
                if (total != null && total > 0) {
                    insertSmokeTest(data.getList());
                    long pageTotal = total % CommonConstant.INTEGER_100 == 0
                            ? (total / CommonConstant.INTEGER_100)
                            : (total / CommonConstant.INTEGER_100 + 1);
                    if (pageTotal > 1) {
                        for (int i = 2; i <= pageTotal; i++) {
                            request.setPageNum(i);
                            Result<PageResult<SmokeTestDTO>> resultNext = devopsFeignClient.getSomkeTestPlan(request);
                            log.info("=== 同步冒烟测试用例信息 === syncTbDySmokeTest, request:{}, result:{}",
                                    JsonUtil.toJsonString(request), JsonUtil.toJsonString(resultNext));
                            insertSmokeTest(resultNext.getData().getList());
                        }
                    }
                }
            }
        }
    }

    /**
     * 对账号分组
     */
    public static List<List<String>> groupLoginAccounts(List<SysTeamUser> userList) {
        if (userList == null || userList.isEmpty()) {
            return List.of();
        }

        // 提取所有loginAccount
        List<String> allAccounts = userList.stream()
                .map(SysTeamUser::getLoginAccount)
                .collect(Collectors.toList());

        // 按每组20个分组
        int groupSize = 20;
        return IntStream.range(0, (allAccounts.size() + groupSize - 1) / groupSize)
                .mapToObj(i -> allAccounts.subList(
                        i * groupSize,
                        Math.min((i + 1) * groupSize, allAccounts.size())))
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertSmokeTest(List<SmokeTestDTO> smokeTestList) {
        if (CollectionUtil.isEmpty(smokeTestList)) {
            return;
        }

        // 设置唯一Key
        smokeTestList.forEach(dto -> dto.setOnlyId(dto.getPlanId() + "-" + dto.getCaseId()));
        // 按 onlyId 分组，取创建时间最晚的一条
        List<SmokeTestDTO> result = smokeTestList.stream()
                .collect(Collectors.groupingBy(SmokeTestDTO::getOnlyId, Collectors.maxBy(Comparator.comparing(SmokeTestDTO::getGmtCreate))))
                .values().stream().filter(Optional::isPresent).map(Optional::get).toList();

        // 删除历史数据
        List<String> idList = result.stream().map(SmokeTestDTO::getOnlyId).toList();
        smokeTestMapper.delSmokeTest(idList);

        List<TbDySmokeTest> smokeTests = new ArrayList<>();
        result.forEach(dto -> {
            TbDySmokeTest test = new TbDySmokeTest();
            BeanUtils.copyProperties(dto, test);
            test.setBusId(dto.getBusinessProjectId());
            smokeTests.add(test);
        });
        smokeTestMapper.insertSmokeTestList(smokeTests);
    }

    /**
     * 获取平台下发的整改问题
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int getPlatformIssues(String startDate, String endDate) {

        /*
             TODO
             1. 目前产品未设计整改 + 反馈的机制，仅作滚动180天查询作为指标展示
             2. 此处未根据起止时间插入及更新整改问题，后续平台若直接异步服务调用实时处理，此定时任务可能废止，具体待定
             3. 平台整改问题筛选查询开发赶不上这次迭代了，平台提供的接口只支持全量查询，下面的方法基于此实现
         */

        // 1. 获取平台整改问题列表
        List<RectificationResponse> result = getIssueList();
        if (CollectionUtil.isEmpty(result)) {
            return 0;
        }

        // 2. 获取服务应用信息
        Map<String, Map<String, MetaDataResponse>> metaMap = getMetaDataMap();
        if (metaMap.isEmpty()) {
            return 0;
        }

        // 3. 实体转换
        List<PlatformIssuePO> platformIssueList = new ArrayList<>();
        transferEntity(result, metaMap, platformIssueList);

        metaMap.clear();

        // 4. 批量插入更新数据库
        return saveOrUpdateIssues(platformIssueList);
    }

    /**
     * 获取系统服务稳定性得分
     *
     * @param date 日期
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordApdex(String date) {

        // 获取CSP系统服务元数据信息
        Result<List<MetaDataResponse>> metaDataResult;
        try {
            MetaDataRequest metaDataRequest = new MetaDataRequest();
            metaDataResult = metaDataFeignClient.getAppMicroInfo(metaDataRequest);
        } catch (Exception e) {
            log.warn("调用平台查询微服务接口, 失败报错", e);
            return;
        }

        List<MetaDataResponse> metaDataResponses;
        if (metaDataResult.isSuccess()) {
            metaDataResponses = metaDataResult.getData();
            if (CollectionUtil.isEmpty(metaDataResponses)) {
                return;
            } else {
                metaDataResponses = metaDataResponses.stream()
                        .filter(item -> CSP_SYSTEM.contains(item.getSystemCode()))
                        .toList();
            }
        } else {
            log.warn("调用平台查询微服务接口, 返回失败{}", metaDataResult.getMessage());
            return;
        }
        log.info("元数据信息：{}", JsonUtil.toJsonString(metaDataResponses));

        // 获取接口信息
        List<AppPerformanceMetricPO> records = new ArrayList<>();
        for (MetaDataResponse metaData : metaDataResponses) {
            Double score = getAppRestApi(metaData.getSubCode(), date);
            AppPerformanceMetricPO record = new AppPerformanceMetricPO();
            BeanUtils.copyProperties(metaData, record);
            record.setDateIndex(LocalDate.parse(date));
            record.setServiceCode(metaData.getSubCode());
            record.setServiceType(metaData.getSubType());
            record.setApdex(BigDecimal.valueOf(score).setScale(2, RoundingMode.HALF_UP).doubleValue());
            record.setTeamId(1);
            record.setTeam("CSP");
            records.add(record);
        }

        // 批量存储
        int deletedCount = appPerformanceMetricMapper.deleteByDate(LocalDate.parse(date));

        int addCount = 0;
        for (int i = 0; i < records.size(); ) {
            addCount += appPerformanceMetricMapper.batchInsert(records.subList(i, Math.min(i + 50, records.size())));
            i += 50;
        }
        log.info("删除记录{}条，新增记录{}条", deletedCount, addCount);
    }

    /**
     * 获取服务得分
     *
     * @param subCode 子代码
     * @param date 日期
     * @return {@link MetricScoreDTO }
     */
    private Double getAppRestApi(String subCode, String date) {
        MetricRestApiParam restParam = new MetricRestApiParam();
        restParam.setEndTime(DateUtils.transferFormat(date, DateUtils.FORMAT_DATE, DateUtils.FORMAT_DATE_TIME));
        String startDate = DateUtils.getDateOffset(date, DateUtils.FORMAT_DATE, -1);
        restParam.setStartTime(DateUtils.transferFormat(startDate, DateUtils.FORMAT_DATE, DateUtils.FORMAT_DATE_TIME));
        restParam.setServiceCodes(Collections.singletonList(subCode));

        // 调用接口获取当前一天的数据
        try {
            Result<List<MetricScoreDTO>> result = watchFeignClient.getApdexScore(restParam);
            if (result.isSuccess()) {
                List<MetricScoreDTO> data = result.getData();
                if (CollectionUtil.isNotEmpty(data)) {
                    log.info("获取apdex得分: {}", JsonUtil.toJsonString(data));
                    return data.get(CommonConstant.FIRST_INDEX).getApdexScore();
                } else {
                    log.info("获取apdex得分返回无数据");
                    return 0d;
                }
            } else {
                log.warn("获取apdex得分返回失败，入参：{}, 错误信息：{}", JsonUtil.toJsonString(restParam), result.getMessage());
                return 0d;
            }
        } catch (Exception e) {
            log.warn("RPC调取apdex得分失败");
            return 0d;
        }
    }

    /**
     * 获取服务应用信息
     *
     * @return {@link Map }<{@link String }, {@link Map }<{@link String }, {@link MetaDataResponse }>>
     */
    private Map<String, Map<String, MetaDataResponse>> getMetaDataMap() {
        Result<List<MetaDataResponse>> metaDataResult;
        try {
            MetaDataRequest metaDataRequest = new MetaDataRequest();
            metaDataResult = metaDataFeignClient.getAppMicroInfo(metaDataRequest);
        } catch (Exception e) {
            log.warn("调用平台查询微服务接口失败报错", e);
            return Collections.emptyMap();
        }

        List<MetaDataResponse> metaDataResponses;
        if (metaDataResult.isSuccess()) {
            metaDataResponses = metaDataResult.getData();
            if (CollectionUtil.isEmpty(metaDataResponses)) {
                return Collections.emptyMap();
            }
        } else {
            log.warn("调用平台查询微服务接口返回失败{}", metaDataResult.getMessage());
            return Collections.emptyMap();
        }

        // 组装查询 map
        Map<String, Map<String, MetaDataResponse>> collect = metaDataResponses.stream()
                .filter(response -> StringUtils.isNotBlank(response.getSystemCode()) &&
                        StringUtils.isNotBlank(response.getSubCode())
                )
                .collect(
                        Collectors.groupingBy(
                                MetaDataResponse::getSystemCode,
                                Collectors.toMap(
                                        MetaDataResponse::getSubCode,
                                        Function.identity(),
                                        (existing, replacement) -> existing
                                )
                        )
                );

        log.info("获取服务应用信息成功");

        return collect;
    }

    /**
     * 获取平台整改问题列表
     *
     * @return {@link List }<{@link RectificationResponse }>
     */
    private List<RectificationResponse> getIssueList() {
        RectificationRequest request = new RectificationRequest();
        Result<List<RectificationResponse>> listResult;
        try {
            listResult = metaDataFeignClient.queryRectificationList(request);
        } catch (Exception e) {
            log.warn("调用平台查询整改问题清单接口失败报错", e);
            return Collections.emptyList();
        }

        List<RectificationResponse> rectificationResponses;
        if (listResult.isSuccess()) {
            rectificationResponses = listResult.getData();
            if (CollectionUtil.isEmpty(rectificationResponses)) {
                return Collections.emptyList();
            }
        } else {
            log.warn("调用平台查询整改问题清单接口返回失败{}", listResult.getMessage());
            return Collections.emptyList();
        }

        // 战队过滤及转换
        Set<String> allMetaDataTeamsUnique = MetaDataTeamTransferEnum.getAllMetaDataTeamsUnique();
        rectificationResponses.stream()
                .filter(item -> allMetaDataTeamsUnique.contains(item.getTeam()))
                .forEach(item -> {
                    item.setTeam(MetaDataTeamTransferEnum.getTeamFromItPmDesc(item.getTeam()));
                });
        List<RectificationResponse> list = rectificationResponses.stream()
                .flatMap(response -> {
                    String originalAppCode = Optional.ofNullable(response.getAppCode()).orElse("");

                    List<String> appCodes = Arrays.stream(originalAppCode.split(","))
                            .map(String::trim)
                            .filter(code -> !code.isEmpty())
                            .distinct()
                            .collect(Collectors.toList());

                    if (appCodes.isEmpty()) {
                        appCodes.add("");
                    }

                    return appCodes.stream()
                            .map(code -> {
                                RectificationResponse newResponse = new RectificationResponse();
                                BeanUtils.copyProperties(response, newResponse);
                                newResponse.setAppCode(code);
                                return newResponse;
                            });
                })
                .toList();

        log.info("查询平台整改问题清单成功");

        return list;
    }

    /**
     * 批量保存或更新问题清单
     *
     * @param platformIssueList 平台问题列表
     * @return int
     */
    private int saveOrUpdateIssues(List<PlatformIssuePO> platformIssueList) {

        // 1. 设置批次大小（根据数据库性能调整，通常 500~2000）
        int batchSize = CommonConstant.INTEGER_1000;
        int totalAffectedRows = CommonConstant.INTEGER_0;

        // 2. 将数据分批次处理， 使用 Guava 分片
        List<List<PlatformIssuePO>> batches = Lists.partition(platformIssueList, batchSize);

        for (List<PlatformIssuePO> batch : batches) {
            // 3. 提取当前批次的 issueNumbers
            List<String> issueNumbers = batch.stream()
                    .map(PlatformIssuePO::getIssueNumber)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            // 4. 批量删除当前批次的旧数据
            if (!issueNumbers.isEmpty()) {
                platformIssueMapper.batchDeleteByIssueNumbers(issueNumbers);
            }

            // 5. 批量插入当前批次的新数据
            if (!batch.isEmpty()) {
                totalAffectedRows += platformIssueMapper.batchInsert(batch);
            }
            log.info("已经批量处理 {} 条数据", totalAffectedRows);
        }

        return totalAffectedRows;
    }

    /**
     * 问题清单的实体转换
     *
     * @param result            结果
     * @param metaMap           元数据映射
     * @param platformIssueList 平台问题列表
     */
    private void transferEntity(List<RectificationResponse> result, Map<String, Map<String, MetaDataResponse>> metaMap,
                                List<PlatformIssuePO> platformIssueList) {

        result.forEach(response -> {
            PlatformIssuePO platformIssuePO = new PlatformIssuePO();
            BeanUtils.copyProperties(response, platformIssuePO);
            platformIssuePO.setIssueStatus(response.getState());
            Map<String, MetaDataResponse> stringMetaDataResponseMap = metaMap.get(response.getSystemCode());
            if (CollectionUtil.isNotEmpty(stringMetaDataResponseMap)) {
                MetaDataResponse metaDataResponse = stringMetaDataResponseMap.get(response.getAppCode());
                if (metaDataResponse != null) {
                    platformIssuePO.setSubCode(metaDataResponse.getSubCode());
                    platformIssuePO.setSubName(metaDataResponse.getSubName());
                    platformIssuePO.setSubLeader(metaDataResponse.getSubLeader());
                    platformIssuePO.setSubLeaderAccount(metaDataResponse.getSubLeaderAccount());
                    platformIssuePO.setSubImplementLeader(metaDataResponse.getSubImplementLeader());
                    platformIssuePO.setSubImplementLeaderAccount(metaDataResponse.getSubImplementLeaderAccount());
                    platformIssuePO.setSubType(metaDataResponse.getSubType());
                    platformIssueList.add(platformIssuePO);
                }
            }
        });

    }
}
