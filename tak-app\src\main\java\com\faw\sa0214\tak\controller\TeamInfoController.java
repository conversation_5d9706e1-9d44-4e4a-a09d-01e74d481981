package com.faw.sa0214.tak.controller;


import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.dto.project.ProjectListDataDTO;
import com.faw.sa0214.tak.client.dto.project.ProjectListRequest;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.UserThreadLocalUtil;
import com.faw.sa0214.tak.mapper.PersonalMapper;
import com.faw.sa0214.tak.model.dto.TakBusinessUpdateDTO;
import com.faw.sa0214.tak.model.dto.TeamInfoUpdateDTO;
import com.faw.sa0214.tak.model.vo.TakBusinessVO;
import com.faw.sa0214.tak.model.vo.TeamInfoVO;
import com.faw.sa0214.tak.po.TeamInfo;
import com.faw.sa0214.tak.service.TeamInfoService;
import com.faw.sa0214.tak.service.impl.TakBusinessServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 战队管理类
 *
 * @version v1.0.1
 * @Copyright: Copyright (c) 2024$
 * @Author: thought1231
 * @Date: 2024/1/23
 * @Time: 17:13
 * @Project sa-0214_msa_tak_be
 */
@Tag(name = "战队管理类", description = "战队管理类")
@RestController
@RequestMapping("/teamManager")
@Slf4j
public class TeamInfoController {

    @Autowired
    private TeamInfoService teamInfoService;
    @Autowired
    private TakBusinessServiceImpl takBusinessService;
    @Autowired
    private PersonalMapper personalMapper;

    /**
     * 查询所有战队,由于战队信息不会太多,所以没有分页查询
     *
     * @return {@link Result }<{@link List }<{@link TeamInfoVO }>>
     */
    @Operation(summary = "查询所有战队,由于战队信息不会太多,所以没有分页查询", description = "[author:10027705]")
    @PostMapping("/listAll")
    public Result<List<TeamInfoVO>> listTeams() {
        List<TeamInfoVO> teamInfos;
        try {
            teamInfos = teamInfoService.getTeamInfoList();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failed("查询角色集合失败!!");
        }
        if (CollectionUtils.isNotEmpty(teamInfos)) {
            return Result.success(teamInfos);
        }
        return null;
    }

    /**
     * 根据teamId查询单个战队信息
     *
     * @param teamId 团队 ID
     * @return {@link Result}<{@link TeamInfo}>
     */
    @Operation(summary = "根据teamId查询单个战队信息", description = "[author:10027705]")
    @PostMapping("/getTeamInfo")
    public Result<TeamInfo> getTeamInfoById(@RequestParam("teamId") String teamId) {
        if (teamId != null && !teamId.equals("")) {
            TeamInfo teamInfo = null;
            try {
                teamInfo = teamInfoService.getById(teamId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("查询相关战队失败!!!");
            }
            if (teamInfo != null) {
                return Result.success(teamInfo);
            }
        }
        return null;
    }

    /**
     * 根据战队id查询子战队信息
     *
     * @param teamId 团队 ID
     * @return {@link Result}<{@link List}<{@link String}>>
     */
    @Operation(summary = "根据战队id查询子战队信息", description = "[author:10027705]")
    @PostMapping("/getChildTeamInfo")
    public Result<List<String>> getChildTeamInfo(@RequestParam("teamId") String teamId) {
        if (StringUtils.isNotBlank(teamId)) {
            try {
                List<String> list = teamInfoService.getChildTeamInfo(teamId);
                if (CollectionUtils.isNotEmpty(list)) {
                    return Result.success(list);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("查询相关子战队信息失败!!!");
            }
        }
        return null;
    }

    /**
     * 保存team信息
     *
     * @param teamInfo 团队信息
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "保存team信息", description = "[author:10027705]")
    @PostMapping("/addTeamInfo")
    public Result<String> addTeamInfo(@RequestBody TeamInfo teamInfo) {
        if (teamInfo != null && !teamInfo.equals("")) {
            teamInfo.setCreatedBy(UserThreadLocalUtil.getCurrentUserCode());
            teamInfo.setCreatedTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
//            if (ContextUtil.getCurrentUser() != null && ContextUtil.getCurrentUser().getUserId() != null && !ContextUtil.getCurrentUser().getUserId().equals("")) {
//                teamInfo.setCreatedBy(ContextUtil.getCurrentUser().getUserId().toString());
//            }
            boolean isSave = false;
            try {
                isSave = teamInfoService.save(teamInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("添加团队失败!");
            }
            if (isSave) {
                return Result.success(String.valueOf(isSave));
            } else {
                return Result.failed("添加团队失败!!!");
            }
        }
        return null;
    }

    /**
     * 根据团队id更新团队信息
     *
     * @param teamInfo 团队信息
     * @return {@link Result}<{@link String}>
     */
    @PostMapping("/updateTeamInfo")
    @Operation(summary = "根据团队id更新团队信息", description = "[author:10027705]")
    public Result<String> updateTeamInfo(@RequestBody TeamInfoUpdateDTO teamInfo) {
        if (teamInfo != null && teamInfo.getTeamId() != null && !teamInfo.getTeamId().equals("")) {
            teamInfo.setUpdatedTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            teamInfo.setUpdatedBy(UserThreadLocalUtil.getCurrentUserCode());
//            if (ContextUtil.getCurrentUser() != null && ContextUtil.getCurrentUser().getUserId() != null && !ContextUtil.getCurrentUser().getUserId().equals("")) {
//                teamInfo.setUpdatedBy(ContextUtil.getCurrentUser().getUserId().toString());
//            }
            try {
                return teamInfoService.updateTeamInfo(teamInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("更新团队信息失败!");
            }
        }
        return null;
    }

    /**
     * 逻辑删除 根据团队id逻辑删除团队信息
     * @param teamId 团队 ID
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "逻辑删除 根据团队id逻辑删除团队信息", description = "[author:10027705]")
    @PostMapping("/deleteTeamInfo")
    public Result<String> deleteTeamInfo(@RequestParam("teamId") Integer teamId) {
        if (teamId != null) {
            int count = personalMapper.checkPersonTeam(teamId);
            if (count > 0) {
                return Result.failed("删除团队信息失败!战队正在被使用!");
            }
            TeamInfo teamInfo = new TeamInfo();
            teamInfo.setTeamId(teamId);
            teamInfo.setIsDelete(1);
            boolean isDelete = false;
            try {
                isDelete = this.teamInfoService.updateById(teamInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("删除团队信息失败!");
            }
            if (isDelete) {
                return Result.success(String.valueOf(isDelete));
            } else {
                return Result.failed("删除团队失败!!!");
            }
        }
        return null;
    }

    /**
     * 查询站队长信息
     *
     * @return
     */
    @Operation(summary = "查询站队长信息", description = "[author:10027705]")
    @GetMapping("listTeamLeader")
    public Result<List<Map<String, String>>> listTeamLeader() {
        try {
            List<Map<String, String>> mapList = teamInfoService.listTeamLeader();
            if (mapList != null && mapList.size() > 0) {
                return Result.success(mapList);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failed("查询战队长信息失败!!!");
        }
        return null;
    }

    /**
     * 查询战队下的项目信息
     *
     * @param dto DTO
     * @return {@link Result}<{@link List}<{@link TakBusinessVO}>>
     */
    @Operation(summary = "查询战队下的项目信息", description = "[author:10027705]")
    @PostMapping("/getTakBusinessList")
    public Result<List<TakBusinessVO>> getTakBusinessList(@RequestBody TakBusinessUpdateDTO dto) {
        List<TakBusinessVO> teamInfoVOS = new ArrayList<>();
        try {
            teamInfoVOS = teamInfoService.getTakBusinessList(dto.getTeamName());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failed("查询战队下的项目信息失败!!");
        }
        return Result.success(teamInfoVOS);
    }

    /**
     * 更新战队下的项目信息
     *
     * @param dto DTO
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "更新战队下的项目信息", description = "[author:10027705]")
    @PostMapping("/updateTakBusiness")
    public Result<String> updateTakBusiness(@RequestBody TakBusinessUpdateDTO dto) {
        boolean isUpdate = false;
        try {
            isUpdate = takBusinessService.updateTakBusiness(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failed("更新战队下的项目失败!");
        }
        if (isUpdate) {
            return Result.success(String.valueOf(isUpdate));
        } else {
            return Result.failed("更新战队下的项目失败!!!");
        }

    }

    /**
     * 按战队返回项目列表（调用deveops接口）
     *
     * @param dto DTO
     * @return {@link Result}<{@link List}<{@link ProjectListDataDTO}>>
     */
    @Operation(summary = "按战队返回项目列表", description = "按战队返回项目列表[author:10027705]")
    @PostMapping("/selectProjectListByTeamId")
    public Result<List<ProjectListDataDTO>> selectProjectListByTeamId(@RequestBody ProjectListRequest dto) {
        List<ProjectListDataDTO> response = new ArrayList<>();
        try {
            response = teamInfoService.selectProjectListByTeamId(dto.getTeamCode());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failed("按战队返回项目列表失败!!");
        }
        return Result.success(response);
    }
}
