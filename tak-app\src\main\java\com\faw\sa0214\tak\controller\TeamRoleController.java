package com.faw.sa0214.tak.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.UserThreadLocalUtil;
import com.faw.sa0214.tak.mapper.PersonalMapper;
import com.faw.sa0214.tak.model.dto.TeamRoleUpdateDTO;
import com.faw.sa0214.tak.po.TeamRole;
import com.faw.sa0214.tak.service.TeamRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * 角色管理
 *
 * @version v1.0.1
 * @author: thought1231
 * @date: 2024/1/23
 * @time: 17:13
 */
@Tag(name = "角色管理", description = "角色管理")
@RestController
@RequestMapping("/roleManager")
@Slf4j
public class TeamRoleController {

    @Autowired
    private TeamRoleService teamRoleService;
    @Autowired
    private PersonalMapper personalMapper;

    /**
     *
     * 查询所有角色,由于战队信息不会太多,所以没有分页查询
     *
     */
    @Operation(summary = "查询所有角色,由于战队信息不会太多,所以没有分页查询", description = "[author:10027705]")
    @PostMapping("/listAll")
    public Result<List<TeamRole>> listTeamRole() {
        List<TeamRole> teamRoles;
        try {
            QueryWrapper<TeamRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.ne("is_delete", 1);
            queryWrapper.or();
            queryWrapper.isNull("is_delete");
            teamRoles = teamRoleService.list(queryWrapper);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failed("查询角色集合失败!!");
        }

        return Result.success(teamRoles);
    }

    /**
     * 查询所有角色
     *
     * @return {@link Result}<{@link List}<{@link TeamRole}>>
     */
    @Operation(summary = "查询所有角色, get方法（devops自定义属性需要）", description = "[author:10027705]")
    @GetMapping("/listAll")
    public Result<List<TeamRole>> getAllRole() {
        return listTeamRole();
    }

    /**
     * 根据Id查询单个角色信息
     *
     * @param roleId 角色 ID
     * @return {@link Result}<{@link TeamRole}>
     */
    @Operation(summary = "根据Id查询单个角色信息", description = "[author:10027705]")
    @PostMapping("/getRoleInfo")
    public Result<TeamRole> getTeamRoleById(@RequestParam("roleId") String roleId) {
        TeamRole teamInfo = null;

        if (StringUtils.isNotBlank(roleId)) {
            try {
                teamInfo = teamRoleService.getById(roleId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("查询相关角色失败!!!");
            }
        }

        return Result.success(teamInfo);
    }

    /**
     * 根据角色id查询子角色集合
     *
     * @param roleId 角色 ID
     * @return {@link Result}<{@link List}<{@link String}>>
     */
    @Operation(summary = "根据角色id查询子角色集合", description = "[author:10027705]")
    @PostMapping("/getChildRoleInfo")
    public Result<List<String>> getChildRoleInfo(@RequestParam("roleId") String roleId) {
        if (StringUtils.isNotBlank(roleId)) {
            try {
                List<String> list = teamRoleService.getChildRoleInfo(roleId);
                log.info("查询相关子角色:{}", JSONObject.toJSONString(list));
                return Result.success(list);
            } catch (Exception e) {
                log.error("根据角色id查询子角色集合失败，入参:{}", roleId, e);
                return Result.failed("查询相关子角色失败!!!");
            }
        } else {
            return Result.success(Collections.emptyList());
        }
    }

    /**
     * 保存团队角色信息
     *
     * @param teamRole 团队角色
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "保存团队角色信息", description = "[author:10027705]")
    @PostMapping("/addRoleInfo")
    public Result<String> addRoleInfo(@RequestBody TeamRole teamRole) {
        if (teamRole != null) {
            teamRole.setCreatedTime(DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE));
            teamRole.setCreatedBy(UserThreadLocalUtil.getCurrentUserCode());
            boolean isSave = false;
            try {
                isSave = teamRoleService.save(teamRole);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("添加团队角色失败!");
            }
            if (isSave) {
                return Result.success(String.valueOf(isSave));
            } else {
                return Result.failed("添加团队角色失败!!!");
            }
        }
        return null;
    }

    /**
     * 根据团队id更新角色信息
     *
     * @param teamRole 团队角色
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "根据团队id更新角色信息", description = "[author:10027705]")
    @PostMapping("/updateRoleInfo")
    public Result<String> updateRoleInfo(@RequestBody TeamRoleUpdateDTO teamRole) {
        if (teamRole != null && teamRole.getRoleId() != null) {
            teamRole.setUpdatedTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            teamRole.setUpdatedBy(UserThreadLocalUtil.getCurrentUserCode());

            try {
                return teamRoleService.updateRoleInfo(teamRole);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("更新团队角色信息失败!");
            }
        }
        return null;
    }

    /**
     * 逻辑删除 根据团队id逻辑删除角色信息
     *
     * @param teamId 团队 ID
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "逻辑删除 根据团队id逻辑删除角色信息", description = "[author:10027705]")
    @PostMapping("/deleteRoleInfo")
    public Result<String> deleteRoleInfo(@RequestParam("teamId") Integer teamId) {
        if (teamId != null) {
            int count = personalMapper.checkPersonRole(teamId);
            if (count > 0) {
                return Result.failed("删除团队角色失败!角色正在被使用!");
            }
            TeamRole teamInfo = new TeamRole();
            teamInfo.setRoleId(teamId);
            teamInfo.setIsDelete(1);
            boolean isDelete = false;
            try {
                isDelete = teamRoleService.updateById(teamInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("删除团队角色失败!");
            }
            if (isDelete) {
                return Result.success(String.valueOf(isDelete));
            } else {
                return Result.failed("删除团队角色失败!!!");
            }
        }
        return null;
    }

    /**
     * 逻辑删除 根据团队id逻辑删除角色信息
     *
     * @param teamId 团队 ID
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "逻辑删除 根据团队id逻辑删除角色信息", description = "[author:10027705]")
    @PostMapping("/deleteRoleInfoWithChackPerson")
    public Result<String> deleteRoleInfoWithChackPerson(@RequestParam("teamId") Integer teamId) {
        if (teamId != null) {

            TeamRole teamInfo = new TeamRole();
            teamInfo.setRoleId(teamId);
            teamInfo.setIsDelete(1);
            boolean isDelete = false;
            try {
                isDelete = teamRoleService.updateById(teamInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("删除团队角色失败!");
            }
            if (isDelete) {
                return Result.success(String.valueOf(isDelete));
            } else {
                return Result.failed("删除团队角色失败!!!");
            }
        }
        return null;
    }
}
