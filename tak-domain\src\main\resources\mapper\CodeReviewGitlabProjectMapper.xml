<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.sa0214.tak.mapper.CodeReviewGitlabProjectMapper">

    <resultMap id="CodeReviewGitlabProjectPO" type="com.faw.sa0214.tak.po.CodeReviewGitlabProjectPO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="project_id" property="projectId" jdbcType="INTEGER"/>
        <result column="system_id" property="systemId" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="token" property="token" jdbcType="VARCHAR"/>
        <result column="language_type" property="languageType" jdbcType="VARCHAR"/>
        <result column="scan_branch" property="scanBranch" jdbcType="VARCHAR"/>
        <result column="scan_type" property="scanType" jdbcType="VARCHAR"/>
        <result column="git_url" property="gitUrl" jdbcType="VARCHAR"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user_code" property="createUserCode" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_code" property="updateUserCode" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="BaseColumnList">
        `id`, `project_id`, `system_id`, `project_code`, `token`, `language_type`, `scan_branch`, `scan_type`,
        `git_url`, `note`, `del_flag`, `create_time`, `create_user_code`, `create_user_name`,
        `update_time`, `update_user_code`, `update_user_name`
    </sql>

    <insert id="insert" parameterType="com.faw.sa0214.tak.po.CodeReviewGitlabProjectPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO code_review_gitlab_project
        (`project_id`, `system_id`, `project_code`, `token`, `language_type`, `scan_branch`, `scan_type`,
         `git_url`, `note`, `del_flag`, `create_user_code`, `create_user_name`)
        VALUES
            (#{projectId,jdbcType=INTEGER}, #{systemId,jdbcType=VARCHAR}, #{projectCode,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR},
             #{languageType,jdbcType=VARCHAR}, #{scanBranch,jdbcType=VARCHAR}, #{scanType,jdbcType=VARCHAR},
             #{gitUrl,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER},
             #{createUserCode,jdbcType=VARCHAR}, #{createUserName,jdbcType=VARCHAR})
    </insert>

    <update id="update" parameterType="com.faw.sa0214.tak.po.CodeReviewGitlabProjectPO">
        UPDATE code_review_gitlab_project
        <set>
            <if test="systemId != null and systemId != ''">
                `system_id` = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="projectCode != null and projectCode != ''">
                `project_code` = #{projectCode,jdbcType=VARCHAR},
            </if>
            <if test="token != null and token != ''">
                `token` = #{token,jdbcType=VARCHAR},
            </if>
            <if test="languageType != null and languageType != ''">
                `language_type` = #{languageType,jdbcType=VARCHAR},
            </if>
            <if test="scanBranch != null and scanBranch != ''">
                `scan_branch` = #{scanBranch,jdbcType=VARCHAR},
            </if>
            <if test="scanType != null and scanType != ''">
                `scan_type` = #{scanType,jdbcType=VARCHAR},
            </if>
            <if test="gitUrl != null and gitUrl != ''">
                `git_url` = #{gitUrl,jdbcType=VARCHAR},
            </if>
            <if test="note != null and note != ''">
                `note` = #{note,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                `del_flag` = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateUserCode != null and updateUserCode != ''">
                `update_user_code` = #{updateUserCode,jdbcType=VARCHAR},
            </if>
            <if test="updateUserName != null and updateUserName != ''">
                `update_user_name` = #{updateUserName,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE `id` = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateTokenById">
        UPDATE code_review_gitlab_project
        SET `token` = #{encodedToken}, `update_user_name` = #{updateUserName}
        WHERE `id` = #{id}
    </update>

    <select id="selectOne" parameterType="java.lang.Integer" resultType="com.faw.sa0214.tak.po.CodeReviewGitlabProjectPO">
        SELECT crgp.`token` AS token,
               crgp.`language_type` AS languageType,
               crgp.`scan_branch` AS scanBranch,
               crgp.`scan_type` AS scanType,
               crfwl.`file_suffix` AS fileSuffix
        FROM code_review_gitlab_project AS crgp
                 LEFT JOIN code_review_file_white_list AS crfwl ON crgp.`language_type` = crfwl.`language_type`
        WHERE crgp.`project_id` = #{projectId,jdbcType=INTEGER}
          AND crgp.`del_flag` = 0
    </select>

    <select id="selectList" parameterType="com.faw.sa0214.tak.model.dto.CodeReviewGitlabProjectDto" resultMap="CodeReviewGitlabProjectPO">
        SELECT <include refid="BaseColumnList"/>
        FROM code_review_gitlab_project
        <where>
            <if test="projectId != null">
                AND `project_id` = #{projectId,jdbcType=INTEGER}
            </if>
            <if test="systemId != null and systemId != ''">
                AND `system_id` = #{systemId,jdbcType=VARCHAR}
            </if>
            <if test="systemIdList != null">
                AND `system_id` IN
                <foreach collection="systemIdList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="projectCode != null and projectCode != ''">
                AND `project_code` LIKE concat('%', #{projectCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="languageType != null and languageType != ''">
                AND `language_type` = #{languageType,jdbcType=VARCHAR}
            </if>
            <if test="scanBranch != null and scanBranch != ''">
                AND `scan_branch` LIKE concat('%', #{scanBranch,jdbcType=VARCHAR}, '%')
            </if>
            <if test="scanType != null and scanType != ''">
                AND `scan_type` LIKE concat('%', #{scanType,jdbcType=VARCHAR}, '%')
            </if>
            <if test="delFlag != null">
                AND `del_flag` = #{delFlag,jdbcType=INTEGER}
            </if>
            <if test="createUserCode != null and createUserCode != ''">
                AND `create_user_code` = #{createUserCode,jdbcType=VARCHAR}
            </if>
            <if test="createUserName != null and createUserName != ''">
                AND `create_user_name` LIKE concat('%', #{createUserName,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        ORDER BY `update_time` desc
    </select>

    <select id="selectByIds" resultType="com.faw.sa0214.tak.po.CodeReviewGitlabProjectPO">
        SELECT <include refid="BaseColumnList"/>
        FROM code_review_gitlab_project
        WHERE id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectListByProjectIds" resultMap="CodeReviewGitlabProjectPO">
        SELECT <include refid="BaseColumnList"/>
        FROM code_review_gitlab_project
        <where>
            <if test="projectIds != null">
                project_id IN
                <foreach collection="projectIds" item="id" open="(" close=")" separator=",">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </select>

</mapper>