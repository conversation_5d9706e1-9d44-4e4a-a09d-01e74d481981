package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.user.UserCheck;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.request.*;
import com.faw.sa0214.tak.model.response.*;
import com.faw.sa0214.tak.po.GroupInfoPO;
import com.faw.sa0214.tak.service.RequirementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/28
 */
@Tag(name = "需求池管理", description = "需求池管理")
@RequestMapping("/requirement")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RequirementController {

    private final RequirementService requirementService;

    @Operation(summary = "获取需求筛选的下拉枚举", description = "[author:10027705]")
    @GetMapping("/getRequirementDict")
    public Result<List<EnumDicResponse>> getRequirementDict() {
        return Result.success(requirementService.getRequirementDict());
    }

    @Operation(summary = "根据业务主线级联查询业务流程", description = "[author:10027705]")
    @GetMapping("/getFlowByMainLine")
    public Result<List<FlowArchitectureResponse>> getFlowByMainLine(String mainLine) {
        return Result.success(requirementService.getFlowByMainLine(mainLine));
    }

    @Operation(summary = "根据当前日期获取默认迭代（当前及历史2个和未来1个）", description = "[author:10027705]")
    @GetMapping("/getDefaultSprint")
    public Result<List<SprintInfoDTO>> getDefaultSprint() {
        return Result.success(requirementService.getDefaultSprint());
    }

    @Operation(summary = "新建需求", description = "新建需求[author:10027705]")
    @PostMapping("/insertRequirement")
    public Result<String> insertRequirement(@Validated @RequestBody RequirementSaveRequest request) {
        return requirementService.insertRequirement(request);
    }

    @Operation(summary = "查询需求", description = "[author:10027705]")
    @PostMapping("/getRequirement")
    public Result<RequirementManagementDTO> getRequirement(@RequestBody RequirementSearchDTO request) {
        return requirementService.getRequirement(request);
    }

    @Operation(summary = "需求统计-创建人下拉框", description = "[author:50012536]")
    @GetMapping("/getReqStatsCreatorList")
    public Result<List<CreatorDTO>> getReqStatsCreatorList() {
        return requirementService.getReqStatsCreatorList();
    }

    @Operation(summary = "需求统计-小看板卡片", description = "[author:50012536]")
    @PostMapping("/getRequirementCard")
    @Deprecated
    public Result<RequirementCardResponse> getRequirementCard(@RequestBody RequirementRequest request) {
        return requirementService.getRequirementCard(request);
    }

    @Operation(summary = "需求统计-列表", description = "[author:50012536]")
    @PostMapping("/getRequirementList")
    public Result<PageResult<RequirementResponse>> getRequirementList(@RequestBody RequirementRequest request) {
        return requirementService.getRequirementList(request);
    }

    @Operation(summary = "编辑需求备注", description = "[author:10027705]")
    @PostMapping("/editReqRemark")
    public Result<Boolean> editReqRemark(@RequestBody ReqRemarkRequest request) {
        return requirementService.editReqRemark(request);
    }

    @Operation(summary = "需求统计-修改需求提醒(是否异常)", description = "[author:50012536]")
    @PostMapping("/changeRequirementLight")
    public Result<String> changeRequirementLight(@RequestBody ReqLightRequest request) {
        return requirementService.changeRequirementLight(request);
    }

    @Operation(summary = "定时任务-需求亮灯逻辑", description = "[author:50012536]")
    @PostMapping("/dealRequirementLight")
    public Result<String> dealRequirementLight() {
        return requirementService.dealRequirementLight();
    }

    @Operation(summary = "需求统计-详情-进度条", description = "[author:50012536]")
    @PostMapping("/getProgressInfo")
    public Result<List<ProgressResponse>> getProgressInfo(@RequestBody RequirementRequest request) {
        return requirementService.getProgressInfo(request.getRequirementId());
    }

    @Operation(summary = "需求池管理-查看需求", description = "[author:50012536]")
    @PostMapping("/getRequirementListBySprintId")
    public Result<List<RequirementResponse>> getRequirementListBySprintId(@RequestBody RequirementRequest request) {
        return requirementService.getRequirementListBySprintId(request.getSprintId(), request.getGroupId());
    }

    @Operation(summary = "需求池管理-需求编辑查询", description = "[author:50012536]")
    @PostMapping("/getChangeRequirementInfo")
    public Result<ReqChangeResponse> getChangeRequirementInfo(@RequestBody RequirementRequest request) {
        return requirementService.getChangeRequirementInfo(request.getRequirementId());
    }

    @Operation(summary = "需求池管理-需求编辑保存", description = "[author:50012536]")
    @PostMapping("/changeRequirement")
    public Result<String> changeRequirement(@Valid @RequestBody ReqChangeRequest request) {
        return requirementService.changeRequirement(request);
    }

    @Operation(summary = "修改需求详情", description = "[author:50012536]")
    @PostMapping("/changeRequirementInfo")
    public Result<String> changeRequirementInfo(@Valid @RequestBody ReqChangeRequest request) {
        return requirementService.changeRequirementInfo(request);
    }

    @Operation(summary = "查询上线预排", description = "[author:10027705]")
    @PostMapping("/getOnlinePlan")
    public Result<List<OnlinePlanInfoDTO>> getOnlinePlan(@RequestBody OnlinePlanInfoSearchDTO request) {
        return requirementService.getOnlinePlan(request);
    }

    @Operation(summary = "上线预排-查询产品经理列表及业务经理列表", description = "[author:10027705]")
    @PostMapping("/getPMAndBizManagerList")
    public Result<PMAndBizManagerDTO> getPMAndBizManagerList(@RequestBody PMAndBizManagerSearchDTO request) {
        return requirementService.getPMAndBizManagerList(request);
    }

    @Operation(summary = "查询迭代下拉菜单", description = "[author:10027705]")
    @PostMapping("/getSprintList")
    public Result<List<SprintInfoDTO>> getSprintList() {
        return requirementService.getSprintList();
    }

    @Operation(summary = "上线预排-查询研发团队列表", description = "[author:10027705]")
    @PostMapping("/getGroupInfoList")
    public Result<List<GroupInfoPO>> getGroupInfoList(@RequestBody GroupInfoSearchDTO request) {
        return requirementService.getGroupInfoList(request);
    }

    @Operation(summary = "产品设计", description = "产品设计[author:10027705]")
    @PostMapping("/productDesign")
    public Result<String> productDesign(@RequestBody ProductDesignRequest request) {
        return Result.success(requirementService.productDesign(request));
    }

    @Operation(summary = "产品设计详情查看", description = "产品设计详情查看[author:10027705]")
    @GetMapping("/productDesignDetail")
    public Result<ProductDesignResponse> productDesignDetail(Integer requirementId) {
        return Result.success(requirementService.productDesignDetail(requirementId));
    }

    @Operation(summary = "验收信息-新建或修改", description = "验收信息-新建或修改[author:10027705]")
    @PostMapping("/insertOrUpdateAcceptance")
    @Deprecated
    public Result<Boolean> insertOrUpdateAcceptance(@Validated @RequestBody AcceptanceInfoListRequest request) {
        return Result.success(requirementService.insertOrUpdateAcceptance(request));
    }

    @Operation(summary = "验收信息-展示", description = "验收信息-展示[author:10027705]")
    @Deprecated
    @GetMapping("/getAcceptance")
    public Result<List<AcceptanceInfoResponse>> getAcceptance(@RequestParam(name = "requirementId") Integer requirementId) {
        return Result.success(requirementService.getAcceptance(requirementId));
    }

    @Operation(summary = "产品设计（设计+验收信息综合提交接口）", description = "产品设计（设计+验收信息综合提交接口）[author:10027705]")
    @PostMapping("/productDesignAndAcceptance")
    public Result<String> productDesignAndAcceptance(@Validated @RequestBody ProductDesignRequestV2 request) {
        return Result.success(requirementService.productDesignV2(request));
    }

    @Operation(summary = "查询当前故事上线情况", description = "查看评审表[author:10027705]")
    @GetMapping("/getStoryOnlineNum")
    public Result<StoryOnlineNumResponse> getStoryOnlineNum(@RequestParam("requirementId") Integer requirementId) {
        return Result.success(requirementService.getStoryOnlineNum(requirementId));
    }

    @Operation(summary = "新建产品上线", description = "[author:10027705]")
    @PostMapping("/insertProductOnline")
    public Result<String> insertProductOnline(@Valid @RequestBody ProductOnlineSaveRequest request) {
        return Result.success(requirementService.insertProductOnline(request));
    }

    @Operation(summary = "产品上线列表", description = "[author:10027705]")
    @PostMapping("/productOnlineList")
    public Result<PageResult<ProductOnlineInfoListResponse>> productOnlineList(@RequestBody ProductOnlineListRequest request) {
        return Result.success(requirementService.productOnlineList(request));
    }

    @Operation(summary = "产品上线详情查看", description = "[author:10027705]")
    @GetMapping("/productOnlineDetail")
    public Result<RequireProductOnlineInfoResponse> productOnlineDetail(@RequestParam(name = "requirementId") Integer requirementId) {
        return Result.success(requirementService.productOnlineDetail(requirementId));
    }

    @Operation(summary = "根据项目查询需求---开放给devops的接口", description = "[author:10027705]")
    @GetMapping("/getRequirementsByBusId")
    public Result<List<ReqInfoDTO>> getRequirementsByBusId(@RequestParam(name = "busId") Long busId) {
        return Result.success(requirementService.getRequirementsByBusId(busId));
    }

    @Operation(summary = "保存或更新需求映射关系---开放给devops的回调接口", description = "[author:10027705]")
    @PostMapping("/saveOrUpdateMap")
    public Result<Integer> saveOrUpdateRequireMap(@RequestBody @Validated RequirementMapDTO dto) {
        return Result.success(requirementService.saveOrUpdateRequireMap(dto));
    }

    @Operation(summary = "回调接口修改实际耗时", description = "[author:10027705]")
    @PostMapping("/requireCallback")
    public Result<String> requireCallback(@RequestBody RequireCallbackRequest request) {
        return requirementService.requireCallback(request);
    }

    @Operation(summary = "删除需求（物理删除）", description = "[author:10027705]")
    @GetMapping("/delete")
    public Result<Boolean> deleteRequirement(@RequestParam(name = "id") Integer id) {
        return requirementService.deleteRequirement(id);
    }

    @Operation(summary = "撤回", description = "[author:10027705]")
    @GetMapping("/withdraw")
    public Result<Boolean> withdrawRequirement(@RequestParam(name = "id") Integer id) {
        return requirementService.withdrawRequirement(id);
    }
}
