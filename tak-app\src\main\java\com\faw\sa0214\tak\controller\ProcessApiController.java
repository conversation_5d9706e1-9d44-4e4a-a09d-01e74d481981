package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.dto.bpm.StartProcessDTO;
import com.faw.sa0214.tak.model.dto.BpmEndCallBackDTO;
import com.faw.sa0214.tak.model.request.*;
import com.faw.sa0214.tak.service.ProcessApiService;
import com.faw.sa0214.tak.service.ProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@Tag(name = "对接工作流回调", description = "对接工作流回调")
@RestController
@RequestMapping("/userChangeHistory")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProcessApiController {

    private final ProcessApiService processApiService;
    private final ProcessService processService;

    /**
     * 补偿启动工作流
     *
     * @param dto 启动工作流实体
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "补偿启动工作流", description = "[author:10027705]")
    @RequestMapping(value = "/restartWorkflow", method = RequestMethod.POST)
    public Result<String> startProcess(@RequestBody StartProcessDTO dto) {
        processService.startProcess(dto);
        return Result.success(null, "restart workflow success");
    }

    /**
     * listener bpm回调
     * 监听工作流回调
     *
     * @param callbackDTO 监听回调数据
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "监听工作流回调", description = "[author:10027705]")
    @RequestMapping(value = "/listenerBPMCallBack", method = RequestMethod.POST)
    public Result<String> listenerBpmCallBack(@RequestBody BpmEndCallBackDTO callbackDTO) {
        processApiService.listenerBpmCallBack(callbackDTO);
        return Result.success(null, "callback success");
    }

    /**
     * 个人信息修改的审批流使用
     * 流程模板需要的API字段，不需要逻辑，只需要出入参，利用扫描导入
     *
     * @param request 请求
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "个人信息修改（审批流待办字段使用）", description = "[author:10027705]")
    @RequestMapping(value = "/changePersonalInfo", method = RequestMethod.POST)
    public Result<String> changePersonalInfo(@RequestBody PersonalInfoRequest request) {
        return Result.success(null, "success");
    }

    /**
     * 需求创建与申请的审批流使用
     * 流程模板需要的API字段，不需要逻辑，只需要出入参，利用扫描导入
     *
     * @param request 请求
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "需求创建和设计（审批流待办字段使用）", description = "[author:10027705]")
    @RequestMapping(value = "/requirementCreate", method = RequestMethod.POST)
    public Result<String> requirementBody(@RequestBody RequirementApproveRequest request) {
        return Result.success(null, "success");
    }

    /**
     * 产品设计的审批流使用
     * 流程模板需要的API字段，不需要逻辑，只需要出入参，利用扫描导入
     *
     * @param request 请求
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "产品设计（审批流待办字段使用）", description = "[author:10027705]")
    @RequestMapping(value = "/productDesign", method = RequestMethod.POST)
    public Result<String> productDesign(@RequestBody ProductDesignApproveRequest request) {
        return Result.success(null, "success");
    }

    /**
     * 产设的审批流使用
     * 流程模板需要的API字段，不需要逻辑，只需要出入参，利用扫描导入
     *
     * @param request 请求
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "产设审批（审批流待办字段使用）", description = "[author:10027705]")
    @RequestMapping(value = "/productAudit", method = RequestMethod.POST)
    public Result<String> productDesignAudit(@RequestBody ProductDesignAuditRequest request) {
        return Result.success(null, "success");
    }

    /**
     * 产品上线的审批流使用
     * 流程模板需要的API字段，不需要逻辑，只需要出入参，利用扫描导入
     *
     * @param request 请求
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "产品上线（审批流待办字段使用）", description = "[author:10027705]")
    @RequestMapping(value = "/productOnline", method = RequestMethod.POST)
    public Result<String> productOnline(@RequestBody ProductOnlineApproveRequest request) {
        return Result.success(null, "success");
    }

    /**
     * 故事上线时间变更的审批流使用
     * 流程模板需要的API字段，不需要逻辑，只需要出入参，利用扫描导入
     *
     * @param request 请求
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "故事上线时间变更（审批流待办字段使用）", description = "[author:10027705]")
    @RequestMapping(value = "/onlineDateChange", method = RequestMethod.POST)
    public Result<String> onlineDateChange(@RequestBody OnlineDateChangeApproveRequest request) {
        return Result.success(null, "success");
    }

}
