package com.faw.sa0214.tak.model.dto.baseCenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "基础处用户中心返回用户基础信息")
public class UserCenterInfo {
    @Schema(description = "主键")
    private String id;
    @Schema(description = "用户id")
    private String code;
    @Schema(description = "用户域账号")
    private String loginName;
    @Schema(description = "用户名")
    private String userName;
    @Schema(description = "用户手机号")
    private String mobile;
    @Schema(description = "用户邮箱")
    private String email;
    @Schema(description = "岗位信息")
    private List<UserPosition> positionList;
}
