package com.faw.sa0214.tak.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.sa0214.tak.model.dto.BpmEndCallBackDTO;
import com.faw.sa0214.tak.po.UserChangeHistroy;

import java.util.List;
import java.util.Map;

/**
 * 业务名称: XXX
 *
 * @version v1.0.1
 * To change this template use File | Settings | Editor | Color Scheme | File and Code Templates.
 * @Copyright: Copyright (c) 2024$
 * @Author: thought1231
 * @Date: 2024/1/23
 * @Time: 15:31
 * @Project sa-0214_msa_tak_be
 */

public interface UserChangeHistoryService extends IService<UserChangeHistroy>  {
     String listenerBPMCallBack(Object entity);

    List<UserChangeHistroy>   getUserChangeHistoryUserId(String userId);


    List<UserChangeHistroy>   getUserChangeHistory(UserChangeHistroy userChangeHistroy);

    public Map getBusinessDetail(String taskId, String busId,String json);

    void handleRejectOrWithdraw(BpmEndCallBackDTO.Execution execution);

    void handleApprovePass(BpmEndCallBackDTO.Execution execution);

    /**
     * 人员变更审批
     *
     * @param userChangeHistory 用户更改历史
     * @return {@link String }
     */
    String submitAdjustFlow(UserChangeHistroy userChangeHistory);
}
