package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.request.ProductFlowPageRequest;
import com.faw.sa0214.tak.po.ProductFlow;
import com.faw.sa0214.tak.service.ProductFlowService;
import com.faw.sa0214.tak.service.TeamPlatformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 产品流程管理
 * <AUTHOR>
 * @date 2025/05/12
 */
@Tag(name = "产品流程管理", description = "产品流程管理")
@RestController
@RequestMapping("/projectFlow")
@Slf4j
public class ProductFlowController {

    @Autowired
    private TeamPlatformService teamPlatformService;
    @Autowired
    private ProductFlowService productFlowService;

    /**
     * 修改产品流程配置
     */
    @Operation(summary = "修改产品流程配置", description = "[author:50012536]")
    @PostMapping("/updateProductFlow")
    public Result<String> updateProductFlow(@Validated @RequestBody ProductFlow productFlow) {
        return productFlowService.updateProductFlow(productFlow);
    }

    /**
     * 根据产品链查询流程信息
     */
    @Operation(summary = "根据产品链查询流程信息", description = "[author:50012536]")
    @PostMapping("/getFlowByProductChain")
    public Result<PageResult<ProductFlow>> getFlowByProductChain(@Validated @RequestBody ProductFlowPageRequest request) {
        return Result.success(productFlowService.getFlowByProductChain(request));
    }

    /**
     * 根据流程id查询流程信息
     */
    @Operation(summary = "根据流程id查询流程信息", description = "[author:10027705]")
    @GetMapping("/getFlowByFlowId")
    public Result<ProductFlow> getFlowByFlowId(String flowId) {
        return Result.success(productFlowService.getFlowByFlowId(flowId));
    }

}
