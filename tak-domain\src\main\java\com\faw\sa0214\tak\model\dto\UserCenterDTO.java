package com.faw.sa0214.tak.model.dto;

import com.faw.sa0214.tak.model.dto.baseCenter.UserPosition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName UserCenterDTO
 * @Description 用户中心返回
 */
@Schema(description = "用户中心返回")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserCenterDTO {
    @Schema(description = "用户id")
    private String id;
    @Schema(description = "登录名")
    private String loginName;
    @Schema(description = "姓名")
    private String name;
    @Schema(description = "用户id")
    private String idmid;
    @Schema(description = "手机号")
    private String mobile;
    @Schema(description = "编码")
    private String code;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "性别")
    private String gender;
    @Schema(description = "昵称")
    private String nickName;
    @Schema(description = "生日")
    private String birthday;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "座机电话")
    private String tel;
    @Schema(description = "腾讯QQ")
    private String qq;
    @Schema(description = "微信")
    private String wechat;
    @Schema(description = "办公电话")
    private String officetel;
    @Schema(description = "证件类型")
    private String certType;
    @Schema(description = "证件号")
    private String certNo;
    @Schema(description = "版本")
    private String version;
    @Schema(description = "岗位信息")
    private List<UserCenterMainJobDTO> centerMainJobList;

    @Schema(description = "姓名")
    private String userName;
    @Schema(description = "岗位信息")
    private List<UserPosition> positionList;

}
