package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.model.response.PlatformResponse;
import com.faw.sa0214.tak.po.TeamPlatform;
import com.faw.sa0214.tak.service.TeamPlatformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 平台管理
 *
 * <AUTHOR>
 * @date 2024/04/12
 */
@Tag(name = "平台管理", description = "平台管理")
@RestController
@RequestMapping("/platformManager")
@Slf4j
public class TeamPlatformController {

    @Autowired
    private TeamPlatformService teamPlatformService;

    /**
     * 添加单体平台配置信息
     *
     * @param teamPlatform 团队平台
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "添加单体平台配置信息", description = "[author:50012536]")
    @PostMapping("/addPlatform")
    public Result<String> addTeamPlatform(@Validated @RequestBody TeamPlatform teamPlatform) {
        return teamPlatformService.addTeamPlatform(teamPlatform);
    }

    /**
     * 根据单体平台删除配置
     *
     * @param teamPlatform 团队平台
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "根据单体平台删除配置", description = "[author:50012536]")
    @PostMapping("/deletePlatform")
    public Result<String> deletePlatform(@Validated @RequestBody TeamPlatform teamPlatform) {
        return teamPlatformService.deletePlatform(teamPlatform);
    }

    /**
     * 修改单体平台配置
     *
     * @param teamPlatform 团队平台
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "修改单体平台配置", description = "[author:50012536]")
    @PostMapping("/updatePlatform")
    public Result<String> updatePlatform(@Validated @RequestBody TeamPlatform teamPlatform) {
        return teamPlatformService.updatePlatform(teamPlatform);
    }

    /**
     * 查询所有单体平台配置信息
     */
    @Operation(summary = "查询所有单体平台配置信息", description = "[author:50012536]")
    @PostMapping("/listAll")
    public Result<List<TeamPlatform>> getTeamPlatformList() {
        return Result.success(teamPlatformService.getTeamPlatformList());
    }

    /**
     * 获取全部单体平台信息
     */
    @Operation(summary = "获取全部单体平台信息", description = "获取全部单体平台信息[author:50012536]")
    @GetMapping("/getPlatformList")
    public Result<List<PlatformResponse>> getPlatformList() {
        return Result.success(teamPlatformService.getPlatformList());
    }

    /**
     * 根据单体平台信息查询功能模块配置
     *
     * @param platform 平台
     * @return {@link Result}<{@link List}<{@link PlatformResponse}>>
     */
    @Operation(summary = "根据单体平台信息查询功能模块配置", description = "根据单体平台信息查询功能模块配置[author:50012536]")
    @GetMapping("/getModuleByPlatform")
    public Result<List<PlatformResponse>> getModuleByPlatform(@RequestParam(value = "platform", required = false) String platform) {
        return Result.success(teamPlatformService.getModuleByPlatform(platform));
    }

}
