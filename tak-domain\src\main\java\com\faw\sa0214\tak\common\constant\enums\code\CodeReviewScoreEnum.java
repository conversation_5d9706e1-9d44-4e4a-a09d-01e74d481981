package com.faw.sa0214.tak.common.constant.enums.code;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 代码审核评分枚举
 *
 * <AUTHOR>
 * @since 2024-11-29 14:59
 */
@Getter
@AllArgsConstructor
public enum CodeReviewScoreEnum {

    /**
     * 无需审核
     */
    NO_NEED_TO_REVIEW      (0, "无需审核", new BigDecimal("80")),

    /**
     * 调用大模型失败
     */
    CALL_BIG_MODEL_FAILED  (1, "调用大模型失败", new BigDecimal("100")),

    /**
     * 代码审核成功
     */
    CODE_REVIEW_SUCCESS    (2, "代码审核成功", new BigDecimal("100"));

    private final Integer code;
    private final String desc;
    private final BigDecimal defaultScore;

}
