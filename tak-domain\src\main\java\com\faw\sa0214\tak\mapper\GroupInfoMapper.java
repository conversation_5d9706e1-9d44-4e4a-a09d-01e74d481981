package com.faw.sa0214.tak.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.faw.sa0214.tak.model.dto.GroupInfoSearchDTO;
import com.faw.sa0214.tak.po.GroupInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队信息线下表
 *
 * <AUTHOR>
 * @date 2025/01/10
 */
@InterceptorIgnore(tenantLine = "true")
public interface GroupInfoMapper {
    /**
     * 按主键删除
     *
     * @param id 身份证
     * @return int
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入
     *
     * @param record 记录
     * @return int
     */
    int insert(GroupInfoPO record);

    /**
     * 选择性插入
     *
     * @param record 记录
     * @return int
     */
    int insertSelective(GroupInfoPO record);

    /**
     * 按主键选择
     *
     * @param id 身份证
     * @return {@link GroupInfoPO }
     */
    GroupInfoPO selectByPrimaryKey(Integer id);

    /**
     * 按主键选择性更新
     *
     * @param record 记录
     * @return int
     */
    int updateByPrimaryKeySelective(GroupInfoPO record);

    /**
     * 按主键更新
     *
     * @param record 记录
     * @return int
     */
    int updateByPrimaryKey(GroupInfoPO record);

    /**
     * 查询团队信息
     */
    GroupInfoPO getGroupInfoById(@Param("groupId") Integer groupId);

    /**
     * 按战队id获取团队信息
     *
     * @param request 请求
     * @return {@link List }<{@link GroupInfoPO }>
     */
    List<GroupInfoPO> getGroupInfoList(GroupInfoSearchDTO request);

    /**
     * 按团队id或团队信息
     *
     * @param groupIds 组 ID
     * @return {@link List }<{@link GroupInfoPO }>
     */
    List<GroupInfoPO> selectByIds(@Param("groupIds") List<Integer> groupIds);

    /**
     * 按战队名称获取团队信息
     *
     * @param teamName 团队名称
     * @return {@link List }<{@link GroupInfoPO }>
     */
    List<GroupInfoPO> getGroupInfoByTeamName(String teamName);

    /**
     * 根据敏捷教练查询团队信息
     */
    List<GroupInfoPO> selectByCoachList(List<String> coachList);
}