package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.common.constant.enums.metadata.IssueTypeEnum;
import com.faw.sa0214.tak.model.dto.EnumDTO;
import com.faw.sa0214.tak.model.request.ConfirmPlatformIssueRequest;
import com.faw.sa0214.tak.model.request.CorrectionPlatformIssueRequest;
import com.faw.sa0214.tak.model.request.PlatformIssueManageRequest;
import com.faw.sa0214.tak.model.response.PlatformIssueManageResponse;
import com.faw.sa0214.tak.service.PlatformIssueManageService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "技术管理", description = "技术管理接口")
@RequestMapping("/platformIssueManage")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PlatformIssueManageController {

    @Resource
    private PlatformIssueManageService platformIssueManageService;

    @Operation(summary = "查询技术管理列表", description = "查询技术管理列表[author:10027705]")
    @PostMapping("/findPlatformIssueManage")
    public Result<PageInfo<PlatformIssueManageResponse>> findPlatformIssueManage(@RequestBody PlatformIssueManageRequest request) {
        return Result.success(platformIssueManageService.findPlatformIssueManage(request));
    }

    @Operation(summary = "问题确认", description = "问题确认[author:10027705]")
    @PostMapping("/confirmPlatformIssueManage")
    public Result<String> confirmPlatformIssue(@RequestBody ConfirmPlatformIssueRequest request) {
        platformIssueManageService.confirmPlatformIssue(request);
        return Result.success(null, "确认成功");
    }

    @Operation(summary = "问题整改", description = "问题整改[author:10027705]")
    @PostMapping("/correctionPlatformIssue")
    public Result<String> correctionPlatformIssue(@RequestBody CorrectionPlatformIssueRequest request) {
        platformIssueManageService.correctionPlatformIssue(request);
        return Result.success(null, "整改完成");
    }

    @Operation(summary = "问题类型枚举", description = "问题类型枚举[author:10027705]")
    @PostMapping("/getIssueTypeEnums")
    public Result<List<EnumDTO>> getIssueTypeEnums() {
        return Result.success(Arrays.stream(IssueTypeEnum.values())
               .map(issueType -> new EnumDTO(issueType.getCode(), issueType.getDesc()))
               .collect(Collectors.toList()));
    }

    @Operation(summary = "微服务名称枚举", description = "微服务名称枚举[author:10027705]")
    @PostMapping("/getSubNameEnums")
    public Result<List<EnumDTO>> getSubNameEnums() {
        return Result.success(platformIssueManageService.subNameEnums());
    }

}
