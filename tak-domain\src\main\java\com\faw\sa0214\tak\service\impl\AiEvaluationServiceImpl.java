package com.faw.sa0214.tak.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dcp.common.exception.BusinessException;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.common.constant.CalcProductManageConstant;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.common.constant.enums.SearchEvaluationEnum;
import com.faw.sa0214.tak.common.constant.enums.SubRoleEnum;
import com.faw.sa0214.tak.common.constant.enums.devops.ScrumDefectSeriousLevelEnum;
import com.faw.sa0214.tak.common.constant.enums.devops.ScrumItemTypeEnum;
import com.faw.sa0214.tak.common.constant.enums.metadata.RectificationStatusEnum;
import com.faw.sa0214.tak.common.constant.enums.safe.SafeRiskLevelEnum;
import com.faw.sa0214.tak.common.constant.enums.safe.SafeRiskStatusEnum;
import com.faw.sa0214.tak.common.util.CommonUtils;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.DecimalUtils;
import com.faw.sa0214.tak.common.util.JsonUtil;
import com.faw.sa0214.tak.mapper.*;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.dto.operation.OperationSupportInfoDTO;
import com.faw.sa0214.tak.model.request.CalculateUserRequest;
import com.faw.sa0214.tak.model.request.PMIndicatorRequest;
import com.faw.sa0214.tak.model.request.ThirdPartyUserRequest;
import com.faw.sa0214.tak.model.request.UserCodeRequest;
import com.faw.sa0214.tak.model.response.*;
import com.faw.sa0214.tak.po.*;
import com.faw.sa0214.tak.po.operation.OperationSupportInfo;
import com.faw.sa0214.tak.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.faw.sa0214.tak.common.util.DateUtils.FORMAT_DATE_TIME;

@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
@Slf4j
public class AiEvaluationServiceImpl implements AiEvaluationService {

    private final EvaluationReportService evaluationReportService;
    private final AiTaskScoreEvaluationService aiTaskScoreEvaluationService;
    private final MetricsService metricsService;
    private final ItBuService itBuService;
    private final AutoCaseMapper autoCaseMapper;
    private final TestCaseMapper testCaseMapper;
    private final TeamInfoMapper teamInfoMapper;
    private final TeamRoleMapper teamRoleMapper;
    private final SafeRiskMapper safeRiskMapper;
    private final TbDySprintMapper sprintMapper;
    private final PersonalMapper personalMapper;
    private final GroupInfoMapper groupInfoMapper;
    private final OperationMapper operationMapper;
    private final SmokeTestMapper smokeTestMapper;
    private final EvaluationMapper evaluationMapper;
    private final SprintInfoMapper sprintInfoMapper;
    private final TbDySprintMapper tbDySprintMapper;
    private final ProductFlowMapper productFlowMapper;
    private final SysTeamUserMapper sysTeamUserMapper;
    private final TakBusinessMapper takBusinessMapper;
    private final TbDyProjectMapper tbDyProjectMapper;
    private final PlatformIssueMapper platformIssueMapper;
    private final DigitalIndicatorScoreDao digitalIndicatorScoreDao;
    private final AppPerformanceMetricMapper appPerformanceMetricMapper;
    private final OperationSupportInfoMapper operationSupportInfoMapper;
    private final AITaskScoreEvaluationMapper aiTaskScoreEvaluationMapper;

    @Override
    public Result<List<String>> getSubRoleList(Integer teamId, String team) {

        List<SysTeamUser> userList = new ArrayList<>();
        if (teamId != null || StringUtils.isNotBlank(team)) {
            SysTeamUser query = new SysTeamUser();
            query.setTeamId(teamId);
            query.setTeam(team);
            userList = sysTeamUserMapper.getTeamUserList(query);
        }

        List<String> roleName = userList.stream().map(SysTeamUser::getRoleName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        // 如果入参teamId或team不为空但角色名为空，说明该战队没有角色信息，并非全查询
        if ((teamId != null || StringUtils.isNotBlank(team)) && CollectionUtil.isEmpty(roleName)) {
            return Result.success(Collections.emptyList());
        }

        List<TeamRole> roleList = teamRoleMapper.getRoleByRoleName(roleName);
        List<String> subRoleList = splitAndFlatten(roleList, TeamRole::getChildRoleName);

        return Result.success(subRoleList.stream()
                .distinct()
                .sorted(Comparator.comparingInt(role -> {
                    int index = CommonConstant.ROLE_ORDER.indexOf(role);
                    return index == -1 ? Integer.MAX_VALUE : index;
                }))
                .collect(Collectors.toList()));
    }

    @Override
    public Result<List<String>> getSubRoleListForBus() {
        List<String> subRoleList = CommonConstant.ROLE_ORDER_STATIC;
        return Result.success(subRoleList.stream()
                .distinct()
                .sorted(Comparator.comparingInt(role -> {
                    int index = CommonConstant.ROLE_ORDER.indexOf(role);
                    return index == -1 ? Integer.MAX_VALUE : index;
                }))
                .collect(Collectors.toList()));
    }

    @Override
    public Result<List<String>> getSubTeamList(String teamId) {

        List<TeamInfo> teamInfoList = teamInfoMapper.getTeamByTeamId(teamId);
        List<String> subTeamList = splitAndFlatten(teamInfoList, TeamInfo::getTeamSecondInfo);

        return Result.success(subTeamList.stream().distinct().collect(Collectors.toList()));
    }

    /**
     * 团队和角色的子项处理
     *
     * @param itemList         项目清单
     * @param getFieldFunction 获取字段函数
     * @return {@link List}<{@link String}>
     */
    private static <T> List<String> splitAndFlatten(List<T> itemList, Function<T, String> getFieldFunction) {
        List<String> subList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(itemList)) {
            itemList.stream()
                    .map(getFieldFunction)
                    .filter(StringUtils::isNotBlank)
                    .map(item -> item.split("、"))
                    .forEach(strings -> subList.addAll(Arrays.asList(strings)));
        }
        return subList;
    }

    /**
     * 计算效能评价，人员维度列表
     */
    @Override
    public Result<PageResult<SearchEvaluationResultDTO>> getAllEvaluation(SearchEvaluationDTO searchEvaluationDTO) {
        log.info("getAllEvaluation params is {}", searchEvaluationDTO);

        // 处理参数
        int pageNumber = searchEvaluationDTO.getPageNum() != null ? searchEvaluationDTO.getPageNum() : 1;
        int pageSize = searchEvaluationDTO.getPageSize() != null ? searchEvaluationDTO.getPageSize() : 10;
        if (StringUtils.isEmpty(searchEvaluationDTO.getTeamId())) {
            return Result.failed("战队id不能为空");
        }

        // 不存在跨月计算得情况，取开始时间所属得年月时间计算
        String yearMonth = StringUtils.substring(searchEvaluationDTO.getStartDate(), 0, 7);

        // 获取员工效能数据
        List<AITaskScoreEvaluationPO> aiTaskScoreEvaluationPOList = getHistoryEvaluation(yearMonth, searchEvaluationDTO);

        List<SearchEvaluationResultDTO> resultDTOList = new ArrayList<>();
        for (AITaskScoreEvaluationPO po : aiTaskScoreEvaluationPOList) {
            SearchEvaluationResultDTO dto = new SearchEvaluationResultDTO();
            BeanUtils.copyProperties(po, dto);
            formatMetricData(dto);
            resultDTOList.add(dto);
        }

        // 处理排序
        List<Integer> orderItemList = searchEvaluationDTO.getOrderItemList();
        List<Integer> orderList = searchEvaluationDTO.getOrderList();
        List<SearchEvaluationResultDTO> sortedListsFinal = new ArrayList<>();
        if (!CollectionUtil.isEmpty(orderItemList) && !CollectionUtil.isEmpty(orderList)) {
            int orderTime = Math.min(orderList.size(), orderItemList.size());
            sortedListsFinal = sortedResponse(resultDTOList, orderItemList.subList(0, orderTime), orderList.subList(0, orderTime));
        } else {
            sortedListsFinal = resultDTOList;
        }

        // 处理分页
        List<SearchEvaluationResultDTO> finalResultList = CommonUtils.paginateData(sortedListsFinal, pageNumber, pageSize);
        log.info("getAllEvaluation after page final ret size is {},data is {}", finalResultList.size(), finalResultList);

        PageResult<SearchEvaluationResultDTO> pageResult = new PageResult<>();
        pageResult.setPageSize(pageSize);
        pageResult.setPageNum(pageNumber);
        pageResult.setTotal((long) resultDTOList.size());
        pageResult.setData(finalResultList);
        return Result.success(pageResult);
    }

    private List<SearchEvaluationResultDTO> sortedResponse(List<SearchEvaluationResultDTO> responseList, List<Integer> fieldList, List<Integer> orderList) {
        if (CollectionUtil.isEmpty(fieldList)) {
            return responseList;
        }

        Comparator<SearchEvaluationResultDTO> finalComparator = null;
        for (int i = 0; i < fieldList.size(); i++) {
            SearchEvaluationEnum orderEnum = SearchEvaluationEnum.getByCode(fieldList.get(i));
            if (orderEnum == null) {
                continue;
            }

            Comparator<SearchEvaluationResultDTO> comparator = null;
//            Comparator<SearchEvaluationResultDTO> comparator = Comparator.comparing(SearchEvaluationResultDTO::getTaskTotalScore, Comparator.reverseOrder());
            if (orderEnum.getCode() == 1 || orderEnum.getCode() == 2 || orderEnum.getCode() == 3) {
                comparator = Comparator.comparing(
                        response -> {
                            Integer code = orderEnum.getCode();
                            if (code == 1) {
                                return response.getTotalCount();
                            } else if (code == 2) {
                                return response.getSingleScore();
                            } else {
                                return response.getQualifiedCount();
                            }
                        }
                );
            } else if (orderEnum.getCode() == 5 || orderEnum.getCode() == 6) {
                comparator = Comparator.comparing(
                        response -> {
                            Integer code = orderEnum.getCode();
                            if (code == 5) {
                                return response.getWorkHourRecognitionRate();
                            } else {
                                return response.getWorkHourSingleScore();
                            }
                        }
                );
            } else {
                comparator = Comparator.comparing(
                        response -> {
                            Integer code = orderEnum.getCode();
                            if (code == 0) {
                                return response.getTaskTotalScore();
                            } else if (code == 4) {
                                return response.getQualifiedSingleScore();
                            } else if (code == 7) {
                                return response.getRunIndex1();
                            } else if (code == 8) {
                                return response.getRunIndex1Score();
                            } else if (code == 9) {
                                return response.getRunIndex2();
                            } else if (code == 10) {
                                return response.getRunIndex2Score();
                            } else if (code == 11) {
                                return response.getRunIndex3();
                            } else if (code == 12) {
                                return response.getRunIndex3Score();
                            } else if (code == 13) {
                                return response.getRunIndex4();
                            } else {
                                return response.getRunIndex4Score();
                            }
                        }
                );
            }

            if (orderList.get(i) == 1) {
                comparator = comparator.reversed();
            }
            if (finalComparator == null) {
                finalComparator = comparator;
            } else {
                finalComparator = finalComparator.thenComparing(comparator);
            }

        }

        if (finalComparator == null) {
            return responseList;
        }
        return responseList.stream()
                .sorted(finalComparator)
                .collect(Collectors.toList());
    }

    /**
     * 获取历史效能数据
     */
    private List<AITaskScoreEvaluationPO> getHistoryEvaluation(String dateTmp, SearchEvaluationDTO searchEvaluationDTO) {

        LambdaQueryWrapper<AITaskScoreEvaluationPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AITaskScoreEvaluationPO::getYearMonthTime, dateTmp);
        lambdaQueryWrapper.eq(AITaskScoreEvaluationPO::getTeamId, searchEvaluationDTO.getTeamId());
        if (!StringUtils.isEmpty(searchEvaluationDTO.getName())) {
            lambdaQueryWrapper.like(AITaskScoreEvaluationPO::getName, searchEvaluationDTO.getName());
        }
        if (CollectionUtil.isNotEmpty(searchEvaluationDTO.getUserCodeList())) {
            lambdaQueryWrapper.in(AITaskScoreEvaluationPO::getUserCode, searchEvaluationDTO.getUserCodeList());
        }
        if (!StringUtils.isEmpty(searchEvaluationDTO.getRole())) {
            lambdaQueryWrapper.eq(AITaskScoreEvaluationPO::getSubRole, searchEvaluationDTO.getRole());
        }
        if (!StringUtils.isEmpty(searchEvaluationDTO.getSubTeamName())) {
            lambdaQueryWrapper.eq(AITaskScoreEvaluationPO::getSubTeam, searchEvaluationDTO.getSubTeamName());
        }

        return Optional.ofNullable(aiTaskScoreEvaluationMapper.selectList(lambdaQueryWrapper))
                .filter(list -> !list.isEmpty()).orElseGet(ArrayList::new);
    }

    /**
     * 处理运营结果 的数据格式
     */
    private void formatMetricData(SearchEvaluationResultDTO dto) {
        if ("产品管理".equals(dto.getSubRole())) {
            dto.setRunIndex1Str(dto.getRunIndex1() == -1 ? "-" : dto.getRunIndex1() + "%");
            dto.setRunIndex2Str(dto.getRunIndex2() == -1 ? "-" : dto.getRunIndex2() + "");
            dto.setRunIndex3Str(dto.getRunIndex3() == -1 ? "-" : dto.getRunIndex3() + "");
        }
        if ("敏捷教练".equals(dto.getSubRole())) {
            dto.setRunIndex1Str(dto.getRunIndex1() == -1 ? "-" : dto.getRunIndex1() + "%");
            dto.setRunIndex2Str(dto.getRunIndex2() == -1 ? "-" : dto.getRunIndex2() + "");
            dto.setRunIndex3Str(dto.getRunIndex3() == -1 ? "-" : dto.getRunIndex3() + "%");
        }
        if ("技术架构师".equals(dto.getSubRole())) {
            dto.setRunIndex1Str(dto.getRunIndex1() == -1 ? "-" : dto.getRunIndex1() + "%");
            dto.setRunIndex2Str(dto.getRunIndex2() == -1 ? "-" : dto.getRunIndex2() + "%");
            dto.setRunIndex3Str(dto.getRunIndex3() == -1 ? "-" : dto.getRunIndex3() + "%");
            dto.setRunIndex4Str(dto.getRunIndex4() == -1 ? "-" : dto.getRunIndex4() + "");
        }
        if ("前端".equals(dto.getSubRole()) || "后端".equals(dto.getSubRole())) {
            dto.setRunIndex1Str(dto.getRunIndex1() == -1 ? "-" : dto.getRunIndex1() + "");
            dto.setRunIndex2Str(dto.getRunIndex2() == -1 ? "-" : dto.getRunIndex2() + "%");
            dto.setRunIndex3Str(dto.getRunIndex3() == -1 ? "-" : dto.getRunIndex3() + "%");
        }
        if ("测试".equals(dto.getSubRole())) {
            dto.setRunIndex1Str(dto.getRunIndex1() == -1 ? "-" : dto.getRunIndex1() + "");
            dto.setRunIndex2Str(dto.getRunIndex2() == -1 ? "-" : dto.getRunIndex2() + "%");
            dto.setRunIndex3Str(dto.getRunIndex3() == -1 ? "-" : dto.getRunIndex3() + "%");
        }
    }

    @Override
    public Result<String> getRoleBaseTaskCount(SearchEvaluationDTO searchEvaluationDTO) {
        log.info("getRoleBaseTaskCount params is {}", searchEvaluationDTO);
        String res = null;
        if (StringUtils.isNotEmpty(searchEvaluationDTO.getStartDate())) {
            String dateTmp = searchEvaluationDTO.getStartDate().substring(0, 7);
            String lastMonthStart = DateUtils.getLastMonthStartTime(dateTmp);
            String lastMonthEnd = DateUtils.getLastMonthEndTime(dateTmp);
            Map<String, Double> baseCountMap = metricsService.calcBaseCountBySubRole(lastMonthStart, lastMonthEnd);
            res = JsonUtil.toJsonString(baseCountMap);
        }
        return Result.success(res);
    }

    @Override
    public void calcOverallScore(ReportOverallDTO overallDTO, Map<String, Map<Integer, List<String>>> accountList, String createdDate) {

        // 每个战队单独计算
        int idmNum = 0;
        int outsourcedNum = 0;
        double idmTotalScore = 0;
        double outsourcedTotalScore = 0;
        for (Map.Entry<String, Map<Integer, List<String>>> entry : accountList.entrySet()) {
            String teamId = entry.getKey();
            Map<Integer, List<String>> categoryMap = entry.getValue();
            List<String> idmUserCodeList = categoryMap.get(CommonConstant.INTEGER_0);
            Double idmScore = getTotalScore(teamId, idmUserCodeList, createdDate);
            idmNum += idmUserCodeList == null ? 0 : idmUserCodeList.size();
            idmTotalScore += idmScore;

            List<String> outsourcedUserCodeList = categoryMap.get(CommonConstant.INTEGER_1);
            Double outsourcedScore = getTotalScore(teamId, outsourcedUserCodeList, createdDate);
            outsourcedNum += outsourcedUserCodeList == null ? 0 : outsourcedUserCodeList.size();
            outsourcedTotalScore += outsourcedScore;
        }

        // 平均分计算
        double idmAvgScore = (idmNum == 0) ? 0 : Math.round(idmTotalScore * 10 / idmNum) / 10.0;
        double outsourcedAvgScore = (outsourcedNum == 0) ? 0 : Math.round(outsourcedTotalScore * 10 / outsourcedNum) / 10.0;
        double avgScore = (idmNum + outsourcedNum == 0) ? 0 : Math.round((idmTotalScore + outsourcedTotalScore) * 10 / (idmNum + outsourcedNum)) / 10.0;

        overallDTO.setIdmAvgNum(String.valueOf(idmAvgScore));
        overallDTO.setOutsourcedAvgNum(String.valueOf(outsourcedAvgScore));
        overallDTO.setAvgScore(String.valueOf(avgScore));

    }

    private Double getTotalScore(String teamId, List<String> userCodeList, String createdDate) {
        if (CollectionUtil.isEmpty(userCodeList)) {
            return 0d;
        }

        DateTimeFormatter compactDateFormatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_COMPACT);
        LocalDate today = LocalDate.parse(createdDate, compactDateFormatter);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE);

        LocalDate startDate;
        LocalDate endDate;

        // 如果今天是当月的7号之前，取上个月的日期范围
        if (today.getDayOfMonth() <= 7) {
            startDate = today.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            endDate = today.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        } else {
            // 否则，取本月从1号到今天的日期范围
            startDate = today.with(TemporalAdjusters.firstDayOfMonth());
            endDate = today;
        }

        SearchEvaluationDTO searchEvaluationDTO = new SearchEvaluationDTO();
        searchEvaluationDTO.setTeamId(teamId);
        searchEvaluationDTO.setPageNum(1);
        searchEvaluationDTO.setPageSize(1000);
        searchEvaluationDTO.setStartDate(startDate.format(formatter));
        searchEvaluationDTO.setEndDate(endDate.format(formatter));
        searchEvaluationDTO.setUserCodeList(userCodeList);
        Result<PageResult<SearchEvaluationResultDTO>> allEvaluation = getAllEvaluation(searchEvaluationDTO);
        List<SearchEvaluationResultDTO> data = allEvaluation.getData().getData();

        return data.stream().mapToDouble(SearchEvaluationResultDTO::getTaskTotalScore).sum();
    }

    @Override
    public PageInfo<TaskInfoDTO> getTaskInfo(SearchTaskDTO dto) {
        // 校验分页大小
        Integer pageSize = dto.getPageSize();
        Integer pageNum = dto.getPageNum();
        if (pageSize == null) {
            dto.setPageSize(100);
        } else if (pageSize > CommonConstant.INTEGER_1000) {
            throw new BusinessException("分页大小不能超过1000!");
        }
        if (pageNum == null) {
            dto.setPageNum(1);
        }

        // 先确认子角色，非7个子角色的不进行查询服务
        SysTeamUserDTO user = personalMapper.getRecordByLoginAccount(dto.getAccount());
        if (user == null) {
            throw new BusinessException("未找到该账户!");
        }
        String subdividedRole = user.getSubdividedRole();
        String minorSubdividedRole = user.getMinorSubdividedRole();

        boolean isSubdividedRoleValid = subdividedRole != null && CommonConstant.ROLE_ORDER_STATIC.contains(subdividedRole);
        boolean isMinorSubdividedRoleValid = minorSubdividedRole != null && CommonConstant.ROLE_ORDER_STATIC.contains(minorSubdividedRole);

        if (!isSubdividedRoleValid && !isMinorSubdividedRoleValid) {
            throw new BusinessException("该账户非IT工程师角色，请确认账户是否正确!");
        }

        // 业务单元校正（devops上任务的业务可能会被篡改）
        List<ItBusinessUnitResponse> itBusinessUnit = itBuService.getItBusinessUnit(null);
        Map<String, BigDecimal> standardHoursMap = itBusinessUnit.stream()
                .collect(Collectors.toMap(ItBusinessUnitResponse::getBusinessUnit, item -> BigDecimal.valueOf(item.getStandardTime())));

        // 数据查询
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        dto.setEndTime(dto.getEndTime() + " 23:59:59");
        List<TaskInfoDTO> taskInfoList = tbDyProjectMapper.selectTaskInfo(dto.getAccount(), dto.getStartTime(), dto.getEndTime());
        taskInfoList.forEach(item -> {
            ScrumItemTypeEnum scrumType = ScrumItemTypeEnum.getByCode(item.getTaskType());
            if (scrumType != null) {
                item.setTaskType(scrumType.getDescription());
            }
            item.setTalentName(user.getUserName());
            item.setTaskStandardHours(standardHoursMap.getOrDefault(item.getBusinessCode(), item.getTaskStandardHours()));
        });

        return new PageInfo<>(taskInfoList);
    }


    @Override
    public List<TaskEvaluationResponseDTO> getEvaluationInfo(List<SearchTaskEvaluationDTO> list) {
        log.info("getEvaluationInfo：{}", JsonUtil.toJsonString(list));
        List<TaskEvaluationResponseDTO> result = new ArrayList<>();
        for (SearchTaskEvaluationDTO dto : list) {
            String startAll = dto.getStartTime().substring(0, 10) + " 00:00:00";
            String endAll = dto.getEndTime().substring(0, 10) + " 23:59:59";
            long effProportionAll = DateUtils.calcDaysBetween(startAll, endAll) + 1;
            if (effProportionAll > 365) {
                throw new BusinessException("最大范围1年!");
            }
        }

        for (SearchTaskEvaluationDTO dto : list) {
            TaskEvaluationResponseDTO responseDTO = new TaskEvaluationResponseDTO();

            SearchEvaluationDTO searchEvaluationDTO = new SearchEvaluationDTO();
            SysTeamUserDTO sysTeamUserResponse = personalMapper.getRecordByLoginAccount(dto.getAccount());
            if (sysTeamUserResponse != null) {
                searchEvaluationDTO.setTeamId(sysTeamUserResponse.getTeamId());
                responseDTO.setUserName(sysTeamUserResponse.getUserName());
                responseDTO.setTalentNo(dto.getAccount());
                responseDTO.setUserRole(sysTeamUserResponse.getApplicationRole());
            }

            boolean flag = true;
            String account = dto.getAccount();
            if (sysTeamUserResponse == null) {
                log.info("该账户不存在：{}", account);
                flag = false;
            } else if (StringUtils.isEmpty(dto.getStartTime()) || StringUtils.isEmpty(dto.getEndTime())) {
                log.info("{} 账户时间范围不正确", account);
                flag = false;
            } else if (StringUtils.isNotEmpty(sysTeamUserResponse.getSubdividedRole()) && StringUtils.isNotEmpty(sysTeamUserResponse.getMinorSubdividedRole())) {
                String subdividedRole = sysTeamUserResponse.getSubdividedRole();
                String minorSubdividedRole = sysTeamUserResponse.getMinorSubdividedRole();
                boolean isSubdividedRoleValid = subdividedRole != null && CommonConstant.ROLE_ORDER_STATIC.contains(subdividedRole);
                boolean isMinorSubdividedRoleValid = minorSubdividedRole != null && CommonConstant.ROLE_ORDER_STATIC.contains(minorSubdividedRole);
                if (!isSubdividedRoleValid && !isMinorSubdividedRoleValid) {
                    // 该账户非IT工程师角色，请确认账户是否正确!
                    log.info("该账户{}子角色异常，子角色：{}, 辅子角色: {}", account, subdividedRole, minorSubdividedRole);
                    flag = false;
                }
            }

            if (flag) {
                List<String> userCodeList = new ArrayList<>();
                userCodeList.add(dto.getAccount());
                searchEvaluationDTO.setUserCodeList(userCodeList);
                searchEvaluationDTO.setStartDate(dto.getStartTime());
                searchEvaluationDTO.setEndDate(dto.getEndTime());

                // 账号查询的总天数
                String startAll = dto.getStartTime().substring(0, 10) + " 00:00:00";
                String endAll = dto.getEndTime().substring(0, 10) + " 23:59:59";
                long effProportionAll = DateUtils.calcDaysBetween(startAll, endAll) + 1;

                String entryTime = sysTeamUserResponse.getEntryTime();// 入项时间
                String outTime = sysTeamUserResponse.getOutTime();// 出项时间
                Integer isDelete = sysTeamUserResponse.getIsDelete();
                if (isDelete == 1) {
                    effProportionAll = DateUtils.calcDaysBetween(entryTime, outTime) + 1;
                }

                // 根据输入的日期划分月份，xxxx-xx 先生成本月的数据，按照人员维度从表中查询历史月份的数据
                List<String> dateList = DateUtils.getYearMonths(searchEvaluationDTO.getStartDate(), searchEvaluationDTO.getEndDate());

                List<TaskEvaluationSubResponseDTO> reponseSublist = new ArrayList<>();
                for (String dateTmp : dateList) {
                    TaskEvaluationSubResponseDTO subResponseDTO = new TaskEvaluationSubResponseDTO();
                    AITaskScoreEvaluationPO po = new AITaskScoreEvaluationPO();
                    String startTimeSub = null;
                    String endTimeSub = null;

                    String startYearMonth = searchEvaluationDTO.getStartDate().substring(0, 7); // 2024-07
                    String startMonth = searchEvaluationDTO.getStartDate().substring(5, 7); // 07
                    String endYearMonth = searchEvaluationDTO.getEndDate().substring(0, 7); // 2024-08
                    String endMonth = searchEvaluationDTO.getEndDate().substring(5, 7); // 08
                    String dateTmpMonth = dateTmp.substring(5, 7); // 08

                    String startTime1 = null;
                    String endTime1 = null;
                    String lastMonthStart1 = null;
                    String lastMonthEnd1 = null;
                    if (dateList.size() == 1) {
                        // 查询范围是一个月，按照请求参数直接查询
                        startTime1 = searchEvaluationDTO.getStartDate() + " 00:00:00";
                        endTime1 = searchEvaluationDTO.getEndDate() + " 23:59:59";
                        startTimeSub = searchEvaluationDTO.getStartDate().substring(0, 10) + " 00:00:00";
                        endTimeSub = searchEvaluationDTO.getEndDate() + " 23:59:59";
                    } else {
                        // 查询范围是多个月
                        if (startYearMonth.equals(dateTmp)) {
                            // 第一个月份
                            startTime1 = searchEvaluationDTO.getStartDate() + " 00:00:00";
                            endTime1 = DateUtils.getMonthEndTime(dateTmp);
                            startTimeSub = searchEvaluationDTO.getStartDate().substring(0, 10) + " 00:00:00";
                            endTimeSub = DateUtils.getMonthEndTime(dateTmp);
                        } else if (dateTmpMonth.equals(endMonth) && endYearMonth.equals(dateTmp)) {
                            // 最后一个月份
                            startTime1 = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTime1 = searchEvaluationDTO.getEndDate() + " 23:59:59";
                            startTimeSub = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTimeSub = searchEvaluationDTO.getEndDate() + " 23:59:59";
                        } else {
                            // 中间月份
                            startTime1 = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTime1 = DateUtils.getMonthEndTime(dateTmp);
                            startTimeSub = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTimeSub = DateUtils.getMonthEndTime(dateTmp);
                        }
                    }

                    lastMonthStart1 = DateUtils.getLastMonthStartTime(dateTmp);
                    lastMonthEnd1 = DateUtils.getLastMonthEndTime(dateTmp);
//                    List<AITaskScoreEvaluationPO> aiPOList = calcEvaluationByDateForITPM(dateTmp, searchEvaluationDTO, startTime1, endTime1, lastMonthStart1, lastMonthEnd1);
                    MetricsParam param = new MetricsParam();
                    BeanUtils.copyProperties(searchEvaluationDTO, param);
                    param.setStartDate(startTime1);
                    param.setEndDate(endTime1);
                    param.setLastStart(lastMonthStart1);
                    param.setLastEnd(lastMonthEnd1);
                    List<AITaskScoreEvaluationPO> aiPOList = metricsService.calcEvaluation(param);
                    if (CollectionUtil.isNotEmpty(aiPOList)) {
                        po = aiPOList.get(0);
                        // 组装数据
                        subResponseDTO.setEffMonth(dateTmp);
                        subResponseDTO.setEffMonthScore(po.getTaskTotalScore());

                        long sprintDay = 0L;
                        if (isDelete == 1) {
                            boolean isIn1 = true;
                            boolean isIn2 = true;
                            // 出项状态
                            // 本次循环时间 在 入项时间和出项时间 范围外
                            if ((DateUtils.parseDate(entryTime, FORMAT_DATE_TIME).after(DateUtils.parseDate(startTimeSub, FORMAT_DATE_TIME))
                                    || entryTime.equals(startTimeSub)) &&
                                    DateUtils.parseDate(entryTime, FORMAT_DATE_TIME).before(DateUtils.parseDate(endTimeSub, FORMAT_DATE_TIME))) {
                                startTimeSub = entryTime;
                            } else {
                                isIn1 = false;
                            }
                            if (DateUtils.parseDate(outTime, FORMAT_DATE_TIME).before(DateUtils.parseDate(endTimeSub, FORMAT_DATE_TIME)) &&
                                    DateUtils.parseDate(outTime, FORMAT_DATE_TIME).after(DateUtils.parseDate(startTimeSub, FORMAT_DATE_TIME))) {
                                endTimeSub = outTime;
                            } else {
                                isIn2 = false;
                            }
                            if (isIn1 && isIn2) {
                                sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                            } else if (isIn1 && !isIn2) {
                                sprintDay = DateUtils.calcDaysBetween(entryTime, endTimeSub) + 1;
                            } else if (!isIn1 && isIn2) {
                                sprintDay = DateUtils.calcDaysBetween(startTimeSub, outTime) + 1;
                            }
                            // 本次循环时间 在 入项时间和出项时间 范围内
                            if ((DateUtils.parseDate(entryTime, FORMAT_DATE_TIME).before(DateUtils.parseDate(startTimeSub, FORMAT_DATE_TIME)))
                                    && DateUtils.parseDate(outTime, FORMAT_DATE_TIME).after(DateUtils.parseDate(endTimeSub, FORMAT_DATE_TIME))) {
                                sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                            }

                        } else {
                            // 入项状态
                            sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                        }

                        subResponseDTO.setEffMonthDay((int) sprintDay);
                        subResponseDTO.setEffProportion(sprintDay + "/" + effProportionAll);
                        subResponseDTO.setUserOneNum(po.getTotalCount());
                        subResponseDTO.setUserOneScore(po.getSingleScore());
                        subResponseDTO.setUserTwoNum(po.getQualifiedCount());
                        subResponseDTO.setUserTwoScore(po.getQualifiedSingleScore());
                        subResponseDTO.setUserThreeEfficiency(po.getWorkHourSingleScoreRateFinal());
                        subResponseDTO.setUserThreeScore(po.getWorkHourSingleScore());
                    } else {
                        // 组装数据 (计算结果为空时，默认赋0)
                        subResponseDTO.setEffMonth(dateTmp);
                        subResponseDTO.setEffMonthScore(Double.valueOf(0));
                        long sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                        subResponseDTO.setEffMonthDay((int) sprintDay);
                        subResponseDTO.setEffProportion(sprintDay + "/" + effProportionAll);
                        subResponseDTO.setUserOneNum(0);
                        subResponseDTO.setUserOneScore(0);
                        subResponseDTO.setUserTwoNum(0);
                        subResponseDTO.setUserTwoScore(Double.valueOf(0));
                        subResponseDTO.setUserThreeEfficiency(Float.valueOf(0));
                        subResponseDTO.setUserThreeScore(Float.valueOf(0));
                    }
                    reponseSublist.add(subResponseDTO);
                }
                responseDTO.setList(reponseSublist);
                int totalDays = 0;

                // 计算总天数
                for (TaskEvaluationSubResponseDTO taskDto1 : reponseSublist) {
                    totalDays += taskDto1.getEffMonthDay();
                }

                double userEfficiency = 0.00;
                // 计算每个对象的加权得分
                // 如员工A 3月1日开始、5月10日结束，则一共作业3月31天、4月30天、5月10天，总计71天；
                // 那么该员工效能得分=31/71*3月效能得分+30/71*4月效能得分+10/71*5月效能得分
                for (TaskEvaluationSubResponseDTO taskDto2 : reponseSublist) {
                    userEfficiency += DecimalUtils.multiply(DecimalUtils.divideZeroByScale(new BigDecimal(taskDto2.getEffMonthDay()),
                            new BigDecimal(totalDays), 4), new BigDecimal(taskDto2.getEffMonthScore()), 4).doubleValue();
                }
                responseDTO.setUserEfficiency(userEfficiency);
            } else {
                responseDTO.setList(new ArrayList<>());
                responseDTO.setUserEfficiency(0.0);
            }
            result.add(responseDTO);
        }
        return result;
    }

    @Override
    public List<TaskEvaluationResponseDTO> getEvaluationInfoForRD(List<SearchTaskEvaluationDTO> list) {
        List<TaskEvaluationResponseDTO> result = new ArrayList<>();
        // 最大单次查询人数50人!
        if (CollectionUtils.isNotEmpty(list) && list.size() > CommonConstant.INTEGER_50) {
            throw new BusinessException("最大单次查询人数50人!");
        }
        for (SearchTaskEvaluationDTO dto : list) {
            String startAll = dto.getStartTime().substring(0, 10) + " 00:00:00";
            String endAll = dto.getEndTime().substring(0, 10) + " 23:59:59";
            long effProportionAll = DateUtils.calcDaysBetween(startAll, endAll) + 1;
            if (effProportionAll > 365) {
                throw new BusinessException("最大范围1年!");
            }
        }

        for (SearchTaskEvaluationDTO dto : list) {
            TaskEvaluationResponseDTO responseDTO = new TaskEvaluationResponseDTO();

            SearchEvaluationDTO searchEvaluationDTO = new SearchEvaluationDTO();
            SysTeamUserDTO sysTeamUserResponse = personalMapper.getRecordByLoginAccount(dto.getAccount());
            if (sysTeamUserResponse != null) {
                searchEvaluationDTO.setTeamId(sysTeamUserResponse.getTeamId());
                responseDTO.setUserName(sysTeamUserResponse.getUserName());
                responseDTO.setTalentNo(dto.getAccount());
                responseDTO.setUserRole(sysTeamUserResponse.getApplicationRole());
            }

            Boolean flag = true;
            if (sysTeamUserResponse == null) {
                log.info("未找到该账户：{}", dto.getAccount());
                flag = false;
            }
            if (StringUtils.isNotEmpty(sysTeamUserResponse.getTeam()) && !"R&D".equals(sysTeamUserResponse.getTeam())) {
                log.info("该账户：{}，非R&D战队人员", dto.getAccount());
                flag = false;
            }
            String subdividedRole = sysTeamUserResponse.getSubdividedRole();
            String minorSubdividedRole = sysTeamUserResponse.getMinorSubdividedRole();
            boolean isSubdividedRoleValid = subdividedRole != null && CommonConstant.ROLE_ORDER_STATIC.contains(subdividedRole);
            boolean isMinorSubdividedRoleValid = minorSubdividedRole != null && CommonConstant.ROLE_ORDER_STATIC.contains(minorSubdividedRole);
            if (!isSubdividedRoleValid && !isMinorSubdividedRoleValid) {
                // 该账户非IT工程师角色，请确认账户是否正确!
                log.info("该账户角色不正确subdividedRole：{}, minorSubdividedRole: {}", subdividedRole, minorSubdividedRole);
                flag = false;
            }

            if (flag) {
                List<String> userCodeList = new ArrayList<>();
                userCodeList.add(dto.getAccount());
                searchEvaluationDTO.setUserCodeList(userCodeList);
                searchEvaluationDTO.setStartDate(dto.getStartTime());
                searchEvaluationDTO.setEndDate(dto.getEndTime());

                // 账号查询的总天数
                String startAll = dto.getStartTime().substring(0, 10) + " 00:00:00";
                String endAll = dto.getEndTime().substring(0, 10) + " 23:59:59";
                long effProportionAll = DateUtils.calcDaysBetween(startAll, endAll) + 1;

                String entryTime = sysTeamUserResponse.getEntryTime();// 入项时间
                String outTime = sysTeamUserResponse.getOutTime();// 出项时间
                Integer isDelete = sysTeamUserResponse.getIsDelete();
                if (isDelete == 1) {
                    effProportionAll = DateUtils.calcDaysBetween(entryTime, outTime) + 1;
                }

                // 根据输入的日期划分月份，xxxx-xx 先生成本月的数据，按照人员维度从表中查询历史月份的数据
                List<String> dateList = DateUtils.getYearMonths(searchEvaluationDTO.getStartDate(), searchEvaluationDTO.getEndDate());

                List<TaskEvaluationSubResponseDTO> reponseSublist = new ArrayList<>();
                for (String dateTmp : dateList) {
                    TaskEvaluationSubResponseDTO subResponseDTO = new TaskEvaluationSubResponseDTO();
                    AITaskScoreEvaluationPO po = new AITaskScoreEvaluationPO();
                    String startTimeSub = null;
                    String endTimeSub = null;

                    String startYearMonth = searchEvaluationDTO.getStartDate().substring(0, 7); // 2024-07
                    String startMonth = searchEvaluationDTO.getStartDate().substring(5, 7); // 07
                    String endYearMonth = searchEvaluationDTO.getEndDate().substring(0, 7); // 2024-08
                    String endMonth = searchEvaluationDTO.getEndDate().substring(5, 7); // 08
                    String dateTmpMonth = dateTmp.substring(5, 7); // 08

                    String startTime1 = null;
                    String endTime1 = null;
                    String lastMonthStart1 = null;
                    String lastMonthEnd1 = null;
                    if (dateList.size() == 1) {
                        // 查询范围是一个月，按照请求参数直接查询
                        startTime1 = searchEvaluationDTO.getStartDate() + " 00:00:00";
                        endTime1 = searchEvaluationDTO.getEndDate() + " 23:59:59";
                        startTimeSub = searchEvaluationDTO.getStartDate().substring(0, 10) + " 00:00:00";
                        endTimeSub = searchEvaluationDTO.getEndDate() + " 23:59:59";
                    } else {
                        // 查询范围是多个月
                        if (startYearMonth.equals(dateTmp)) {
                            // 第一个月份
                            startTime1 = searchEvaluationDTO.getStartDate() + " 00:00:00";
                            endTime1 = DateUtils.getMonthEndTime(dateTmp);
                            startTimeSub = searchEvaluationDTO.getStartDate().substring(0, 10) + " 00:00:00";
                            endTimeSub = DateUtils.getMonthEndTime(dateTmp);
                        } else if (dateTmpMonth.equals(endMonth) && endYearMonth.equals(dateTmp)) {
                            // 最后一个月份
                            startTime1 = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTime1 = searchEvaluationDTO.getEndDate() + " 23:59:59";
                            startTimeSub = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTimeSub = searchEvaluationDTO.getEndDate() + " 23:59:59";
                        } else {
                            // 中间月份
                            startTime1 = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTime1 = DateUtils.getMonthEndTime(dateTmp);
                            startTimeSub = DateUtils.getMonthStartTimeNew(dateTmp);
                            endTimeSub = DateUtils.getMonthEndTime(dateTmp);
                        }
                    }

                    lastMonthStart1 = DateUtils.getLastMonthStartTime(dateTmp);
                    lastMonthEnd1 = DateUtils.getLastMonthEndTime(dateTmp);
//                    List<AITaskScoreEvaluationPO> aiPOList = calcEvaluationByDate(dateTmp, searchEvaluationDTO, startTime1, endTime1, lastMonthStart1, lastMonthEnd1);
                    MetricsParam param = new MetricsParam();
                    BeanUtils.copyProperties(searchEvaluationDTO, param);
                    param.setStartDate(startTime1);
                    param.setEndDate(endTime1);
                    param.setLastStart(lastMonthStart1);
                    param.setLastEnd(lastMonthEnd1);
                    List<AITaskScoreEvaluationPO> aiPOList = metricsService.calcEvaluation(param);
                    if (CollectionUtil.isNotEmpty(aiPOList)) {
                        po = aiPOList.get(0);
                        // 组装数据
                        subResponseDTO.setEffMonth(dateTmp);
                        subResponseDTO.setEffMonthScore(po.getTaskTotalScore());

                        long sprintDay = 0L;
                        if (isDelete == 1) {
                            boolean isIn1 = true;
                            boolean isIn2 = true;
                            // 出项状态
                            // 本次循环时间 在 入项时间和出项时间 范围外
                            if ((DateUtils.parseDate(entryTime, FORMAT_DATE_TIME).after(DateUtils.parseDate(startTimeSub, FORMAT_DATE_TIME))
                                    || entryTime.equals(startTimeSub)) &&
                                    DateUtils.parseDate(entryTime, FORMAT_DATE_TIME).before(DateUtils.parseDate(endTimeSub, FORMAT_DATE_TIME))) {
                                startTimeSub = entryTime;
                            } else {
                                isIn1 = false;
                            }
                            if (DateUtils.parseDate(outTime, FORMAT_DATE_TIME).before(DateUtils.parseDate(endTimeSub, FORMAT_DATE_TIME)) &&
                                    DateUtils.parseDate(outTime, FORMAT_DATE_TIME).after(DateUtils.parseDate(startTimeSub, FORMAT_DATE_TIME))) {
                                endTimeSub = outTime;
                            } else {
                                isIn2 = false;
                            }
                            if (isIn1 && isIn2) {
                                sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                            } else if (isIn1 && !isIn2) {
                                sprintDay = DateUtils.calcDaysBetween(entryTime, endTimeSub) + 1;
                            } else if (!isIn1 && isIn2) {
                                sprintDay = DateUtils.calcDaysBetween(startTimeSub, outTime) + 1;
                            }
                            // 本次循环时间 在 入项时间和出项时间 范围内
                            if ((DateUtils.parseDate(entryTime, FORMAT_DATE_TIME).before(DateUtils.parseDate(startTimeSub, FORMAT_DATE_TIME)))
                                    && DateUtils.parseDate(outTime, FORMAT_DATE_TIME).after(DateUtils.parseDate(endTimeSub, FORMAT_DATE_TIME))) {
                                sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                            }

                        } else {
                            // 入项状态
                            sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                        }

                        subResponseDTO.setEffMonthDay((int) sprintDay);
                        subResponseDTO.setEffProportion(sprintDay + "/" + effProportionAll);
                        subResponseDTO.setUserOneNum(po.getTotalCount());
                        subResponseDTO.setUserOneScore(po.getSingleScore());
                        subResponseDTO.setUserTwoNum(po.getQualifiedCount());
                        subResponseDTO.setUserTwoScore(po.getQualifiedSingleScore());
                        subResponseDTO.setUserThreeEfficiency(po.getWorkHourSingleScoreRateFinal());
                        subResponseDTO.setUserThreeScore(po.getWorkHourSingleScore());
                    } else {
                        // 组装数据 (计算结果为空时，默认赋0)
                        subResponseDTO.setEffMonth(dateTmp);
                        subResponseDTO.setEffMonthScore(Double.valueOf(0));
                        long sprintDay = DateUtils.calcDaysBetween(startTimeSub, endTimeSub) + 1;
                        subResponseDTO.setEffMonthDay((int) sprintDay);
                        subResponseDTO.setEffProportion(sprintDay + "/" + effProportionAll);
                        subResponseDTO.setUserOneNum(0);
                        subResponseDTO.setUserOneScore(0);
                        subResponseDTO.setUserTwoNum(0);
                        subResponseDTO.setUserTwoScore(Double.valueOf(0));
                        subResponseDTO.setUserThreeEfficiency(Float.valueOf(0));
                        subResponseDTO.setUserThreeScore(Float.valueOf(0));
                    }
                    reponseSublist.add(subResponseDTO);
                }
                responseDTO.setList(reponseSublist);
            } else {
                responseDTO.setList(new ArrayList<>());
                responseDTO.setUserEfficiency(0.0);
            }
            result.add(responseDTO);
        }
        return result;
    }

    /**
     * 计算员工效能 - 运营结果数据
     * 每日凌晨计算前一天的数据
     */
    @Override
    public String calcOperationalScore(CalcIndicatorDTO dto) {

        // 查询待计算人员范围
        List<AITaskScoreOperationPO> calcUserList = dealOperationalUserInfo(dto);

        // 计算 产品管理 运营结果数据
        calcProductManagerScore(dto.getYearMonth(), calcUserList);

        // 计算 敏捷教练 运营结果数据
        calcSprintCoachScore(dto.getYearMonth(), calcUserList);

        // 计算 技术架构师 运营结果数据
        calcArchitectScore(dto.getYearMonth(), calcUserList);

        // 计算 前后端 运营结果数据
        calcFrontAndBackScore(dto.getYearMonth(), calcUserList);

        // 计算 测试 运营结果数据
        calcTesterScore(dto.getYearMonth(), calcUserList);

        return "success";
    }

    /**
     * 计算架构师指标分
     *
     * @param yearMonth    年月
     * @param calcUserList 计算用户列表
     */
    private void calcArchitectScore(String yearMonth, List<AITaskScoreOperationPO> calcUserList) {
        // 筛选 架构师 人员
        List<AITaskScoreOperationPO> architects = calcUserList.stream()
                .filter(s -> Objects.equals(s.getSubRole(), SubRoleEnum.TECHNICAL_ARCHITECT.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(architects)) {

            // 指标1计算  架构问题整改率
            calcArchitectIssueScore(architects, yearMonth);

            // 指标2计算 安全问题整改率
            calcSateIssueScore(architects, yearMonth);

            // 指标3计算 设计类工时占比
            calcArchitectDesignScore(architects, yearMonth);

            // 指标4计算 系统稳定性评分
            calcSystemPerformanceScore(architects, yearMonth);

            // 保存数据
            saveOperationData(calcUserList);
        }
    }

    /**
     * 技术架构师指标4计算 -- 系统稳定性评分
     *
     * @param architects 架构师
     * @param yearMonth 年月
     */
    private void calcSystemPerformanceScore(List<AITaskScoreOperationPO> architects, String yearMonth) {
        List<String> userList = architects.stream().map(AITaskScoreOperationPO::getUserCode).toList();
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        // 处理日期计算，提取到方法或保持原结构
        YearMonth inputYearMonth = YearMonth.parse(yearMonth, DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_YEAR_MONTH));
        LocalDate endDate = inputYearMonth.equals(YearMonth.now()) ? LocalDate.now() : inputYearMonth.atEndOfMonth();
        LocalDate startDate = inputYearMonth.atDay(1);

        // 获取上月日期
        YearMonth lastMonth = inputYearMonth.minusMonths(1);
        LocalDate lastMonthStart = lastMonth.atDay(1);
        LocalDate lastMonthEnd = lastMonth.atEndOfMonth();

        // 查询本月和上月的系统指标数据
        List<AppPerformanceMetricPO> currentMonthMetrics = appPerformanceMetricMapper.selectByDateAndSubLeader(startDate, endDate, userList);
        List<AppPerformanceMetricPO> lastMonthMetrics = appPerformanceMetricMapper.selectByDateAndSubLeader(lastMonthStart, lastMonthEnd, userList);

        // 按用户分组计算
        Map<String, List<AppPerformanceMetricPO>> currentMonthByUser = currentMonthMetrics.stream()
                .filter(item -> item.getApdex() != 0)
                .collect(Collectors.groupingBy(AppPerformanceMetricPO::getSubLeaderAccount));
        Map<String, List<AppPerformanceMetricPO>> lastMonthByUser = lastMonthMetrics.stream()
                .filter(item -> item.getApdex() != 0)
                .collect(Collectors.groupingBy(AppPerformanceMetricPO::getSubLeaderAccount));

        // 计算每个用户的得分
        for (AITaskScoreOperationPO architect : architects) {
            String userCode = architect.getUserCode();
            List<AppPerformanceMetricPO> currentMetrics = currentMonthByUser.getOrDefault(userCode, Collections.emptyList());
            List<AppPerformanceMetricPO> lastMetrics = lastMonthByUser.getOrDefault(userCode, Collections.emptyList());

            double finalScore = calculateSystemPerformanceScore(currentMetrics, lastMetrics);

            // 设置得分
            architect.setRunIndex4(finalScore);
            architect.setRunIndex4Score(finalScore);
        }

    }

    /**
     * 技术架构师指标3计算 -- 设计类工时占比
     * 计算周期：每月
     * 设计类交付工时占比=sum(技术调研+架构设计+接口设计+时序图设计+数据库设计)/总工时
     *
     * @param architects 架构师列表
     * @param yearMonth  年月
     */
    private void calcArchitectDesignScore(List<AITaskScoreOperationPO> architects, String yearMonth) {
        List<String> userList = architects.stream().map(AITaskScoreOperationPO::getUserCode).toList();
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        // 处理日期计算，提取到方法或保持原结构
        YearMonth inputYearMonth = YearMonth.parse(yearMonth, DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_YEAR_MONTH));
        LocalDate endDate = inputYearMonth.equals(YearMonth.now()) ? LocalDate.now() : inputYearMonth.atEndOfMonth();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

        LocalDate startDate = inputYearMonth.atDay(1);
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime lastMonthDateTime = startDateTime.minusMonths(1);

        // 查询工时数据
        List<TaskBusHoursDTO> busHoursList = tbDyProjectMapper.selectBusinessAndConsumerHourByAccount(
                lastMonthDateTime, startDateTime, endDateTime, userList);

        // 使用流式处理统计工时
        Map<String, Double[]> userStats = busHoursList.stream()
                .filter(dto -> dto.getConsumerHours() != null)
                .collect(Collectors.groupingBy(
                        TaskBusHoursDTO::getLoginAccount,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    double total = list.stream().mapToDouble(TaskBusHoursDTO::getConsumerHours).sum();
                                    double docTotal = list.stream()
                                            .filter(dto -> CommonConstant.DOC_BUSINESS_UNIT.contains(dto.getBusinessUnit()))
                                            .mapToDouble(TaskBusHoursDTO::getConsumerHours)
                                            .sum();
                                    return new Double[]{total, docTotal};
                                }
                        )
                ));

        // 设置得分
        architects.forEach(architect -> {
            String userCode = architect.getUserCode();
            Double[] stats = userStats.get(userCode);
            if (stats != null && stats[0] != null && stats[0] > 0) {
                Double runIndex = DecimalUtils.getDoubleRate(stats[1], stats[0]);
                architect.setRunIndex3(runIndex);
                architect.setRunIndex3Score(dealDesignScore(runIndex));
            } else {
                architect.setRunIndex3(0D);
                architect.setRunIndex3Score(0D);
            }
        });

    }

    /**
     * 计算设计类交付工时占比得分
     */
    private double dealDesignScore(Double runIndex) {
        if (runIndex == null) {
            return 0.0;
        }
        if (runIndex > 10.0) {
            return 100.0;  // 优：(10%, +∞) → 100分
        } else if (runIndex > 8.0) {
            return 80.0;   // 良：(8%, 10%] → 80分
        } else if (runIndex > 5.0) {
            return 50.0;   // 中：(5%, 8%] → 50分
        } else if (runIndex > 0.0) {
            return 0.0;    // 差：(0%, 5%] → 0分
        } else {
            // 处理非正数情况，可以根据实际需求返回默认值
            return 0.0;
        }
    }

    /**
     * 技术架构师指标2计算 -- 安全问题整改率
     * 计算周期：滚动365天内
     * 评价人：负责人
     * 公式：安全问题整改率 = 1- (高风险潜在超期数 * 0.60 + 中风险潜在超期数 * 0.30 + 低风险潜在超期数 * 0.10) / (高风险总数 * 0.60 + 中风险总数 * 0.30 + 低风险总数 * 0.10)
     * 潜在超期：计划完成时间前1周开始计算
     *
     * @param architects 架构师
     * @param yearMonth  年 月
     */
    private void calcSateIssueScore(List<AITaskScoreOperationPO> architects, String yearMonth) {
        List<String> userList = architects.stream()
                .map(AITaskScoreOperationPO::getUserCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        // 时间计算
        YearMonth inputYm = YearMonth.parse(yearMonth, DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_YEAR_MONTH));
        LocalDateTime endDate = inputYm.equals(YearMonth.now())
                ? LocalDateTime.now()
                : inputYm.atEndOfMonth().atTime(LocalTime.MAX);
        LocalDateTime startDate = endDate.minusDays(365);

        // 查询安全问题数据
        List<SafeRiskPO> safeRiskList = safeRiskMapper.selectByLaunchDateAndImplement(startDate, endDate, userList);

        // 按用户分组
        Map<String, List<SafeRiskPO>> issuesByUser = safeRiskList.stream()
                .filter(issue -> issue.getResponsiblePersonAccount() != null)
                .collect(Collectors.groupingBy(SafeRiskPO::getResponsiblePersonAccount));

        // 批量设置得分
        architects.forEach(architect -> {
            List<SafeRiskPO> userIssues = issuesByUser.getOrDefault(architect.getUserCode(), Collections.emptyList());
            double score = calculateSafeIssueScore(userIssues);
            architect.setRunIndex2(score);
            architect.setRunIndex2Score(score);
        });
    }

    /**
     * 风险级别权重映射
     */
    private double getWeightByRiskLevel(SafeRiskLevelEnum riskLevel) {
        if (riskLevel == null) return 0.0;
        return switch (riskLevel) {
            case HIGH_RISK -> 0.6;
            case MEDIUM_RISK -> 0.3;
            case LOW_RISK -> 0.1;
        };
    }

    /**
     * 技术架构师指标1计算 -- 架构问题整改率
     * 计算周期：滚动180天内
     * 评价人：实施负责人
     * 公式：● 单元分 = 100/该研发负责所有整改数(未完成的)
     *      ● 研发架构整改率 = 100 - 单元分*(截止当月的该研发负责的逾期架构整改数)
     *
     * @param architects 架构师
     * @param yearMonth  年 月
     */
    private void calcArchitectIssueScore(List<AITaskScoreOperationPO> architects, String yearMonth) {
        List<String> userList = architects.stream().map(AITaskScoreOperationPO::getUserCode).toList();
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        // 处理日期计算
        YearMonth inputYm = YearMonth.parse(yearMonth, DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE_YEAR_MONTH));
        LocalDate endDate = inputYm.equals(YearMonth.now()) ?
                LocalDate.now() : inputYm.atEndOfMonth();
        LocalDate startDate = endDate.minusDays(180);

        // 查询平台问题数据
        List<PlatformIssuePO> platformIssueList = platformIssueMapper.selectByLaunchDateAndImplement(
                startDate.format(DateTimeFormatter.ISO_DATE),
                endDate.format(DateTimeFormatter.ISO_DATE),
                userList
        );

        // 统计每个用户的问题数据
        Map<String, List<PlatformIssuePO>> issuesByUser = platformIssueList.stream()
                .filter(issue -> issue.getSubLeaderAccount() != null)
                .collect(Collectors.groupingBy(PlatformIssuePO::getSubLeaderAccount));

        // 批量设置得分
        Date endDateTime = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        architects.forEach(architect -> {
            List<PlatformIssuePO> userIssues = issuesByUser.getOrDefault(architect.getUserCode(), Collections.emptyList());
            double score = calculateArchitectIssueScore(userIssues, endDateTime);
            architect.setRunIndex1(score);
            architect.setRunIndex1Score(score);
        });
    }

    /**
     * 处理待计算用户信息
     */
    List<AITaskScoreOperationPO> dealOperationalUserInfo(CalcIndicatorDTO dto) {
        List<AITaskScoreOperationPO> resultList = new ArrayList<>();
        List<SysTeamUser> userList = sysTeamUserMapper.getItPmUserListNew(dto.getUserCodeList());
        for (SysTeamUser user : userList) {
            AITaskScoreOperationPO operation = new AITaskScoreOperationPO();
            operation.setUserCode(user.getLoginAccount());
            operation.setUserName(user.getUserName());
            operation.setSubRole(user.getSubdividedRole());
            operation.setTeam(user.getTeam());
            operation.setSubTeam(user.getHomeDevTeam());
            operation.setYearMonthTime(dto.getYearMonth());
            resultList.add(operation);
        }
        return resultList;
    }

    /**
     * 计算 前后端 运营结果数据
     *
     * @param yearMonth 年月
     */
    void calcFrontAndBackScore(String yearMonth, List<AITaskScoreOperationPO> calcUserList) {
        log.info("=== 计算 {} 前后端 运营结果数据 ===", yearMonth);
        // 筛选 前后端 人员
        List<AITaskScoreOperationPO> resultList = calcUserList.stream()
                .filter(s -> Objects.equals(s.getSubRole(), SubRoleEnum.FRONT_END.getDesc())
                        || Objects.equals(s.getSubRole(), SubRoleEnum.BACK_END.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(resultList)) {

            // 计算 指标1 代码评价打分
            calcCodeReviewScore(resultList, yearMonth);

            // 计算 指标2 冒烟测试通过率
            calcSmokeTestPassRate(resultList, yearMonth);

            // 计算 指标3 开发工时bug率
            calcWorkHourBugScore(resultList, yearMonth);

            // 保存数据
            saveOperationData(resultList);
        }

        log.info("=== 计算 {} 前后端 运营结果数据 完成 ===", yearMonth);
    }

    /**
     * 计算 员工效能-运营结果-前后端-代码评价打分
     * a. 公式：代码交付类的业务单元平均得分
     * b. 详细逻辑：SUM（研发代码交付类评分）/SUM（研发代码交付类任务数量）
     *
     * @param resultList 计算结果数据
     */
    private void calcCodeReviewScore(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-前后端-代码评价打分 ===", yearMonth);
        // 代码类交付物Id
        List<Integer> deliverableIdList = List.of(2, 22, 23, 49, 55, 56);
        // 人员列表
        List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        // 计算时间
        String startTime = DateUtils.getMonthStartTime(yearMonth);   // 当月第一天
        String endTime = DateUtils.getMonthEndTime(yearMonth);   // 当月最后一天
        String lastMonthStart = DateUtils.getLastMonthStartTime(yearMonth);   // 上月第一天
        // 获取待计算任务数据
        List<TaskScoreResultDTO> taskListTemp = evaluationMapper.getTaskListNew(startTime, endTime, lastMonthStart, userList);

        // 计算代码评价打分
        for (AITaskScoreOperationPO userOperation : resultList) {

            // 当前人员 代码类任务 且 存在评分
            List<TaskScoreResultDTO> codeTaskList = taskListTemp.stream()
                    .filter(s -> Objects.equals(s.getUserCode(), userOperation.getUserCode()))
                    .filter(s -> deliverableIdList.contains(s.getDeliverableId()))
                    .filter(s -> s.getAiScore() != null)
                    .toList();

            if (CollectionUtil.isNotEmpty(codeTaskList)) {
                double aiScore = codeTaskList.stream().mapToDouble(TaskScoreResultDTO::getAiScore).sum();
                log.info("=== 代码评价打分 === userCode={}, aiScore={}, codeTaskList.size()={}", userOperation.getUserCode(), aiScore, codeTaskList.size());
                userOperation.setRunIndex1(DecimalUtils.getDoubleResult(aiScore / codeTaskList.size()));
                userOperation.setRunIndex1Score(DecimalUtils.getDoubleResult(aiScore / codeTaskList.size()));
            } else {
                // 如果没有代码类交付物，则设置为-1, 前端展示 "-"
                userOperation.setRunIndex1(0D);
                userOperation.setRunIndex1Score(0D);
            }
        }
        log.info("=== 计算 {} 员工效能-运营结果-前后端-代码评价打分 完成 ===", yearMonth);
    }

    /**
     * 计算 员工效能-运营结果-前后端-冒烟测试通过率
     * 公式：（冒烟p0bug个数）通过用例数/冒烟用例数
     *
     * @param resultList 计算结果数据
     */
    private void calcSmokeTestPassRate(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-前后端-冒烟测试通过率 ===", yearMonth);
        // 获取人员列表
        List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        // 查询冒烟测试数据
        SelectCaseParam param = new SelectCaseParam();
        param.setStartTime(DateUtils.getMonthStartTime(yearMonth));
        param.setEndTime(DateUtils.getMonthEndTime(yearMonth));
        param.setTesterList(userList);
        List<TbDySmokeTest> smokeList = smokeTestMapper.getSmokeBySprint(param);

        // 计算冒烟测试通过率
        for (AITaskScoreOperationPO userOperation : resultList) {
            List<TbDySmokeTest> userPlanList = smokeList.stream().filter(s -> Objects.equals(s.getTesterAccount(), userOperation.getUserCode())).toList();
            List<TbDySmokeTest> passPlanList = userPlanList.stream().filter(s -> Objects.equals("通过", s.getExecuteStatus())).toList();
            log.info("=== 冒烟测试通过率 === userCode={}, userPlanList.size()={}, passPlanList.size()={}",
                    userOperation.getUserCode(), userPlanList.size(), passPlanList.size());

            if (CollectionUtil.isNotEmpty(userPlanList)) {
                Double runIndex = DecimalUtils.getDoubleRate(passPlanList.size(), userPlanList.size());
                userOperation.setRunIndex2(runIndex);
                userOperation.setRunIndex2Score(runIndex);
            } else {
                userOperation.setRunIndex2(0D);
                userOperation.setRunIndex2Score(0D);
            }
        }
        log.info("=== 计算 {} 员工效能-运营结果-前后端-冒烟测试通过率 完成 ===", yearMonth);
    }

    /**
     * 计算 员工效能-运营结果-前后端-开发工时bug率
     * a. 公式：（p0个数*1+P1个数*0.5+p2个数*0.2）/(代码编写+联调测试 任务工时)
     *
     * @param resultList 结果集合
     * @param yearMonth  年月
     */
    private void calcWorkHourBugScore(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-前后端-开发工时bug率 ===", yearMonth);
        // 获取人员列表
        List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        SelectTaskParam param = new SelectTaskParam();
        param.setStartTime(DateUtils.getMonthStartTime(yearMonth));
        param.setEndTime(DateUtils.getMonthEndTime(yearMonth));
        param.setUserCodeList(userList);
        // 查询人员任务工时
        List<TaskScoreResultDTO> taskList = evaluationMapper.getTaskListBySprint(param);
        // 查询人员缺陷数据
        List<TaskScoreResultDTO> defectList = evaluationMapper.getDefectTaskList(param);

        // 计算 开发工时bug率
        for (AITaskScoreOperationPO userOperation : resultList) {

            Map<String, Long> levelCountMap = defectList.stream()
                    .filter(s -> Objects.equals(s.getUserCode(), userOperation.getUserCode()))
                    .collect(Collectors.groupingBy(
                            s -> s.getSerioutLevel() == null ? "UNKNOWN" : s.getSerioutLevel(),
                            Collectors.counting()
                    ));

            int blockerNum = levelCountMap.getOrDefault(ScrumDefectSeriousLevelEnum.Blocker.getValue(), 0L).intValue();
            int majorNum = levelCountMap.getOrDefault(ScrumDefectSeriousLevelEnum.Major.getValue(), 0L).intValue();
            int normalNum = levelCountMap.getOrDefault(ScrumDefectSeriousLevelEnum.Normal.getValue(), 0L).intValue()
                    + levelCountMap.getOrDefault("UNKNOWN", 0L).intValue();

            // 代码编写 + 联调测试 工时
            List<Integer> deliverableIdList = List.of(2, 22, 49, 55);
            double totalConsumeHours = taskList.stream()
                    .filter(s -> Objects.equals(s.getUserCode(), userOperation.getUserCode()))
                    .filter(s -> deliverableIdList.contains(s.getDeliverableId()))
                    .mapToDouble(TaskScoreResultDTO::getConsumeHours)
                    .sum();

            log.info("=== 开发工时bug率 === userCode={}, blockerNum={}, majorNum={}, normalNum={}, totalConsumeHours={}",
                    userOperation.getUserCode(), blockerNum, majorNum, normalNum, totalConsumeHours);

            if (totalConsumeHours > 0) {
                double value = DecimalUtils.getDoubleRate(blockerNum + majorNum * 0.5 + normalNum * 0.2, totalConsumeHours);
                userOperation.setRunIndex3(value);
                userOperation.setRunIndex3Score(calcWorkBugScore(value / 100));
            } else {
                userOperation.setRunIndex3(0D);
                userOperation.setRunIndex3Score(0D);
            }
        }
        log.info("=== 计算 {} 员工效能-运营结果-前后端-开发工时bug率 完成 ===", yearMonth);
    }

    /**
     * 计算分数
     */
    private double calcWorkBugScore(double value) {
        if (value >= 0 && value <= 0.25) {
            return 10 * ((0.25 - value) / 0.25) + 90;
        } else if (value > 0.25 && value <= 0.5) {
            return 10 * ((0.5 - value) / 0.25) + 80;
        } else if (value > 0.5 && value <= 0.75) {
            return 20 * ((0.75 - value) / 0.25) + 60;
        } else if (value > 0.75) {
            return 60 * ((1 - Math.min(1, value)) / 0.25);
        } else {
            return 0;
        }
    }

    /**
     * 计算 测试 运营结果数据
     *
     * @param yearMonth    年月
     * @param calcUserList 带计算用户列表
     */
    void calcTesterScore(String yearMonth, List<AITaskScoreOperationPO> calcUserList) {
        log.info("=== 计算 {} 测试 运营结果数据 ===", yearMonth);
        // 筛选 测试 人员
        List<AITaskScoreOperationPO> resultList = calcUserList.stream()
                .filter(s -> Objects.equals(s.getSubRole(), SubRoleEnum.TESTER.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(resultList)) {

            // 计算 指标1 自动化接口个数-迭代新增
            calcAutoInterfaceNum(resultList, yearMonth);

            // 计算 指标2 生产问题缺陷率
            calcDefectRate(resultList, yearMonth);

            // 计算 指标3 缺陷逃逸率
            calcDefectEscapeRate(resultList, yearMonth);

            // 保存数据
            saveOperationData(resultList);
        }

        log.info("=== 计算 {} 测试 运营结果数据 完成 ===", yearMonth);
    }

    /**
     * 计算 员工效能-运营结果-测试-自动化接口个数
     * 本月新增自动化用例个数
     *
     * @param resultList 结果集合
     * @param yearMonth  年月
     */
    private void calcAutoInterfaceNum(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-测试-自动化接口个数 ===", yearMonth);
        // 获取人员列表
        List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        SelectCaseParam param = new SelectCaseParam();
        param.setStartTime(DateUtils.getMonthStartTime(yearMonth));
        param.setEndTime(DateUtils.getMonthEndTime(yearMonth));
        param.setCreatorList(userList);
        List<CaseDTO> userCaseList = autoCaseMapper.getUserTestCaseNum(param);
        Map<String, Integer> userCaseMap = userCaseList.stream()
                .collect(Collectors.toMap(CaseDTO::getUserCode, CaseDTO::getCaseCount));
        log.info("=== 自动化接口个数 === userCaseMap={}", JsonUtil.toJsonString(userCaseMap));

        for (AITaskScoreOperationPO userOperation : resultList) {
            Integer caseCount = userCaseMap.getOrDefault(userOperation.getUserCode(), 0);
            userOperation.setRunIndex1(caseCount.doubleValue());
            userOperation.setRunIndex1Score(calcAutoScore(caseCount));
        }
        log.info("=== 计算 {} 员工效能-运营结果-测试-自动化接口个数 完成 ===", yearMonth);
    }

    /**
     * 计算自动化用例个数分数
     * @param caseCount 自动化用例个数
     * @return
     */
    private double calcAutoScore(Integer caseCount) {
        if (caseCount >= 8) {
            return 100;
        } else if (caseCount >= 4) {
            return 80;
        } else if (caseCount >= 1) {
            return 60;
        } else {
            return 0;
        }
    }

    /**
     * 计算 员工效能-运营结果-测试-生产问题缺陷率
     * ■ 计算周期：每个月、迭代
     * ■ 计算维度：按团队统计，团队内每名测试得分都是这个值
     * ■ 计算指标：(该团队的运维问题 /该团队开发的代码行数) * 1000
     */
    private void calcDefectRate(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-测试-生产问题缺陷率 ===", yearMonth);
        String startTime = DateUtils.getMonthStartTime(yearMonth);
        String endTime = DateUtils.getMonthEndTime(yearMonth);

        // 待计算的团队
        List<String> subTeamList = resultList.stream().map(AITaskScoreOperationPO::getSubTeam)
                .filter(Objects::nonNull).distinct().toList();

        // 查询全部用户
        List<SysTeamUser> allUserList = sysTeamUserMapper.getUniqueUser(QueryUserDTO.builder().subTeamList(subTeamList).build());

        Map<String, Double> teamScoreMap = new HashMap<>();
        Map<String, Long> teamCodeLineMap = new HashMap<>();
        for (String subTeam : subTeamList) {
            // 团队下人员
            List<String> teamUserList = allUserList.stream()
                    .filter(s -> Objects.equals(s.getHomeDevTeam(), subTeam))
                    .map(SysTeamUser::getLoginAccount).toList();
            List<UserSubmitCode> userSubmitCodes = evaluationReportService.getUserSubmitCode(UserCodeRequest.builder()
                    .accountList(teamUserList).startTime(startTime).endTime(endTime).build());
            long codeLine = userSubmitCodes.stream().mapToLong(UserSubmitCode::getCodeLines).sum();
            teamCodeLineMap.put(subTeam, codeLine);

            // 团队下运维问题
            OperationSupportInfoDTO dto = new OperationSupportInfoDTO();
            dto.setTeamName(subTeam);
            dto.setStartTime(startTime);
            dto.setEndTime(endTime);
            dto.setBugFlag(1);
            List<OperationSupportInfo> supportInfoList = operationSupportInfoMapper.getOperationSupportInfoList(dto);

            if (codeLine > 0) {
                double data = (double) (supportInfoList.size() * 1000L) / codeLine;
                teamScoreMap.put(subTeam, DecimalUtils.getDoubleResult(data));
            }
        }
        log.info("=== 生产问题缺陷率 === teamScoreMap={}, teamCodeLineMap={}", JsonUtil.toJsonString(teamScoreMap), JsonUtil.toJsonString(teamCodeLineMap));

        // 给测试人员赋值
        for (AITaskScoreOperationPO userOperation : resultList) {
            double value = teamScoreMap.getOrDefault(userOperation.getSubTeam(), 0D);
            userOperation.setRunIndex2(value);
            userOperation.setRunIndex2Score(DecimalUtils.getScore(100-value*10));
            Long codeLine = teamCodeLineMap.getOrDefault(userOperation.getSubTeam(), 0L);
            userOperation.setFactor2(Double.valueOf(codeLine));
        }
        log.info("=== 计算 {} 员工效能-运营结果-测试-生产问题缺陷率 完成 ===", yearMonth);
    }

    /**
     * 计算 员工效能-运营结果-测试-缺陷逃逸率
     * ■ 计算周期：每个月、迭代
     * ■ 计算维度：每名测试
     * ■ 计算指标：(该测试的运维问题 /(该测试的运维问题 +该测试为作者的缺陷总数 )) * 100%
     */
    private void calcDefectEscapeRate(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 工效能-运营结果-测试-缺陷逃逸率 ===", yearMonth);
        // 获取人员列表
        List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        // 查询测试负责的流程信息
        List<ProductFlow> flowList = productFlowMapper.getFLowByTester(userList);

        // 查询当前月份全部运维问题
        OperationSupportInfoDTO dto = new OperationSupportInfoDTO();
        dto.setStartTime(DateUtils.getMonthStartTime(yearMonth));
        dto.setEndTime(DateUtils.getMonthEndTime(yearMonth));
        dto.setBugFlag(1);
        List<OperationSupportInfo> opsList = operationSupportInfoMapper.getOperationSupportInfoList(dto);

        // 查询该测试为作者的缺陷数
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.getMonthStartTime(yearMonth));
        param.setEndDate(DateUtils.getMonthEndTime(yearMonth));
        param.setUserCodeList(userList);
        List<TbDyProject> defectProjectList = tbDyProjectMapper.getDefectProjectList(param);

        for (AITaskScoreOperationPO userOperation : resultList) {
            // 测试负责流程
            List<String> userFlow = flowList.stream()
                    .filter(s -> Objects.equals(s.getTesterCode(), userOperation.getUserCode()))
                    .map(ProductFlow::getFlowId).distinct().toList();
            // 测试运维问题
            long userOps = opsList.stream()
                    .filter(s -> userFlow.contains(s.getL3ProcessCode()) || userFlow.contains(s.getL4ProcessCode())).count();
            // 测试缺陷
            long userDefects = defectProjectList.stream().filter(s -> Objects.equals(s.getCreator(), userOperation.getUserCode())).count();

            // 计算指标
            double value = userOps + userDefects > 0 ? DecimalUtils.getDoubleRate(userOps, userOps + userDefects) : 0;
            userOperation.setRunIndex3(value);
            userOperation.setRunIndex3Score(calcDefectScore(value));

        }
        log.info("=== 计算 {} 员工效能-运营结果-测试-缺陷逃逸率 完成 ===", yearMonth);
    }

    /**
     * 计算缺陷逃逸率分数
     * @param value 缺陷逃逸率
     * @return 分数
     */
    private double calcDefectScore(double value) {
        if (value < 8) {
            return 100;
        } else {
            // 计算扣分，并确保不低于 0
            double score = 100 - Math.round(value - 8);
            return Math.max(score, 0); // 最低 0 分
        }
    }

    /**
     * 计算 员工效能-运营结果-测试-测试用例评审通过率
     * 公式：测试用例评审通过率=评审通过的用例数/用例总数*100%
     *
     * @param resultList 结果集合
     * @param yearMonth  年月
     */
    private void calcTestCasePassRate(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-测试-测试用例评审通过率 ===", yearMonth);
        // 获取人员列表
        List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        // 查询用例评审
        SelectCaseParam param = new SelectCaseParam();
        param.setStartTime(DateUtils.getMonthStartTime(yearMonth));
        param.setEndTime(DateUtils.getMonthEndTime(yearMonth));
        param.setTestType("reviewplan");
        param.setCreatorList(userList);
        List<TbDyTestCase> testCaseList = testCaseMapper.getCaseBySprint(param);

        // 计算 测试用例评审通过率
        for (AITaskScoreOperationPO userOperation : resultList) {
            List<TbDyTestCase> userPlanList = testCaseList.stream().filter(s -> Objects.equals(s.getCreator(), userOperation.getUserCode())).toList();
            List<TbDyTestCase> passPlanList = userPlanList.stream().filter(s -> Objects.equals("通过", s.getExecuteStatus())).toList();
            log.info("=== 测试用例评审通过率 === userCode={}, userPlanList.size()={}, passPlanList.size()={}",
                    userOperation.getUserCode(), userPlanList.size(), passPlanList.size());

            if (CollectionUtil.isNotEmpty(userPlanList)) {
                userOperation.setRunIndex2(DecimalUtils.getDoubleRate(passPlanList.size(), userPlanList.size()));
                userOperation.setRunIndex2Score(DecimalUtils.getDoubleRate(passPlanList.size(), userPlanList.size()));
            } else {
                userOperation.setRunIndex2(0D);
                userOperation.setRunIndex2Score(0D);
            }
        }
        log.info("=== 计算 {} 员工效能-运营结果-测试-测试用例评审通过率 完成 ===", yearMonth);
    }

    /**
     * 保存运营结果数据
     *
     * @param resultList 计算结果数据
     */
    private void saveOperationData(List<AITaskScoreOperationPO> resultList) {
        if (CollectionUtils.isNotEmpty(resultList)) {
            // 人员列表
            List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

            // 删除历史数据
            operationMapper.delOperationByUserCode(resultList.get(0).getYearMonthTime(), userList);

            // 保存数据
            operationMapper.insertOperationBatch(resultList);
        }

    }

    /**
     * 计算 产品管理 运营结果数据
     */
    private void calcProductManagerScore(String yearMonth, List<AITaskScoreOperationPO> calcUserList) {
        log.info("=== 计算 {} 产品 运营结果数据 ===", yearMonth);
        // 筛选 产品 人员
        List<AITaskScoreOperationPO> resultList = calcUserList.stream()
                .filter(s -> Objects.equals(s.getSubRole(), SubRoleEnum.PRODUCT_MANAGER.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(resultList)) {

            // 产品管理 指标1 产设一次通过率
            calcProductManageFirstIndex(resultList, yearMonth);

            // 产品管理 指标2 导入故事点数
            calcProductManageSecondIndex(resultList, yearMonth);

            // 产品管理 指标3 产设开口项评分
            calcProductManageThirdIndex(resultList, yearMonth);

            // 产品管理 指标4 产设超期率
            calcOverdueDesign(resultList, yearMonth);

            // 保存数据
            saveOperationData(calcUserList);
        }

        log.info("=== 计算 {} 产品 运营结果数据 完成 ===", yearMonth);
    }

    /**
     * 产品管理 指标1 产设一次通过率
     */
    private void calcProductManageFirstIndex(List<AITaskScoreOperationPO> calcUserList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-产品-指标1-产设一次通过率 ===", yearMonth);

        // 查询需求故事点数合计
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.getMonthStartTime(yearMonth));
        param.setEndDate(DateUtils.getMonthEndTime(yearMonth));
        param.setUserCodeList(calcUserList.stream().map(AITaskScoreOperationPO::getUserCode).toList());
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryPointBySprint(param);

        for (AITaskScoreOperationPO dto : calcUserList) {
            log.info(dto.getUserCode());
            List<PMIndicatorResponse> pmStoryList = storyList.stream().filter(s -> Objects.equals(s.getAssignedUserName(), dto.getUserCode())).toList();
            // 总故事点
            double totalStoryPoints = pmStoryList.stream().mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            // 通过的故事点
            double passStoryPoints = pmStoryList.stream()
                    .filter(s -> s != null && s.getReviewStatusFirst() != null)
                    .filter(s -> s.getReviewStatusFirst() == 1)
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            // 有条件通过的故事点
            double resPassStoryPoints = pmStoryList.stream()
                    .filter(s -> s != null && s.getReviewStatusFirst() != null)
                    .filter(s -> s.getReviewStatusFirst() == 2)
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            double passNum = passStoryPoints + resPassStoryPoints * 0.5;

            log.info("=== 计算产设一次通过率 === {} 总故事点：{}， 通过故事点：{}", dto.getUserCode(), totalStoryPoints, passNum);
            double indexValue = DecimalUtils.getDoubleRate(passNum, totalStoryPoints);

            // 写入数据
            dto.setRunIndex1(indexValue);
            dto.setRunIndex1Score(indexValue);
        }
        log.info("=== 计算 {} 员工效能-运营结果-产品-指标1-产设一次通过率 完成 ===", yearMonth);
    }

    /**
     * 产品管理 指标2 导入故事点数
     */
    private void calcProductManageSecondIndex(List<AITaskScoreOperationPO> calcUserList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-产品-指标2-导入故事点数 ===", yearMonth);

        // 查询需求故事点数合计
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.getMonthStartTime(yearMonth));
        param.setEndDate(DateUtils.getMonthEndTime(yearMonth));
        param.setUserCodeList(calcUserList.stream().map(AITaskScoreOperationPO::getUserCode).toList());
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryPointBySprint(param);

        for (AITaskScoreOperationPO dto : calcUserList) {
            double totalStoryPoints = storyList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), dto.getUserCode()))
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();

            // 写入数据
            dto.setRunIndex2(totalStoryPoints);
            dto.setRunIndex2Score(calProductIndex2(totalStoryPoints));
        }

        log.info("=== 计算 {} 交付质量-产品-指标2-导入故事点数 完成 ===", yearMonth);
    }

    private Double calProductIndex2(double indexValue) {
        if (indexValue >= 100) {
            return 100.0;
        } else if (indexValue >= 80) {
            return 80.0;
        } else if (indexValue >= 50) {
            return 60.0;
        } else if (indexValue >= 30) {
            return 30.0;
        } else if (indexValue >= 10) {
            return 20.0;
        } else if (indexValue > 0) {
            return 10.0;
        } else {
            return 0.0;
        }
    }

    /**
     * 产品管理 指标3 产设开口项评分
     * 产设开口项评分=100−((核心流程问题项数×1.5)+(非核心流程问题项数×1.0)/需求故事点数合计值×100)
     */
    private void calcProductManageThirdIndex(List<AITaskScoreOperationPO> calcUserList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-产品-指标3-产设开口项评分 ===", yearMonth);

        // 查询需求故事点数合计
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.getMonthStartTime(yearMonth));
        param.setEndDate(DateUtils.getMonthEndTime(yearMonth));
        param.setUserCodeList(calcUserList.stream().map(AITaskScoreOperationPO::getUserCode).toList());
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryPointBySprint(param);

        // 查询流程问题数
        List<PMIndicatorResponse> openItemList = tbDyProjectMapper.queryOpenItemBySprint(param);

        // 计算公式 产设开口项评分=100−((核心流程问题项数×1.5)+(非核心流程问题项数×1.0)/需求故事点数合计值×100)
        for (AITaskScoreOperationPO dto : calcUserList) {
            // 故事点
            double totalStoryPoints = storyList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), dto.getUserCode()))
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();

            // 核心开口项
            double coreCount = openItemList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), dto.getUserCode()))
                    .filter(s -> Objects.equals(s.getOpenItemType(), CalcProductManageConstant.CORE_FLOW))
                    .mapToDouble(PMIndicatorResponse::getOpenItemTypeCount).sum();

            // 非核心开口项
            double nonCoreCount = openItemList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), dto.getUserCode()))
                    .filter(s -> Objects.equals(s.getOpenItemType(), CalcProductManageConstant.NON_CORE_FLOW))
                    .mapToDouble(PMIndicatorResponse::getOpenItemTypeCount).sum();

            log.info("===产设开口项评分=== {} 故事点数：{}，核心开口项：{}，非核心开口项：{}", dto.getUserCode(), totalStoryPoints, coreCount, nonCoreCount);
            double indexValue = 100 - DecimalUtils.getDoubleRate(coreCount * 1.5 + nonCoreCount, totalStoryPoints);

            dto.setRunIndex3(indexValue);
            dto.setRunIndex3Score(indexValue);
        }

        log.info("=== 计算 {} 员工效能-运营结果-产品-指标3-产设开口项评分 完成 ===", yearMonth);

    }

    /**
     * 产品管理 指标4 产设超期率
     * 超期评审故事的故事点数 / 迭代时间内的故事的故事点数
     * 超期评审： 需求池首次评审通过时间 > 所属迭代开始时间
     * 取数范围： 计划完成时间在迭代时间内 + 计划完成时间为空 且 devops完成时间在迭代时间内
     */
    private void calcOverdueDesign(List<AITaskScoreOperationPO> calcUserList, String yearMonth){
        log.info("=== 计算 {} 员工效能-运营结果-产品-指标4-产设超期率 ===", yearMonth);

        // 查询当月迭代信息
        List<SprintInfoPO> sprintList = sprintInfoMapper.getSprintInfoByPeriod(DateUtils.getMonthStartTime(yearMonth), DateUtils.getMonthEndTime(yearMonth));

        List<PMIndicatorResponse> allList = new ArrayList<>();
        List<PMIndicatorResponse> overdueList = new ArrayList<>();
        for(SprintInfoPO sprint : sprintList){
            // 查询本迭代内产品创建故事
            PMIndicatorRequest param = new PMIndicatorRequest();
            param.setStartDate(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            param.setEndDate(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            param.setUserCodeList(calcUserList.stream().map(AITaskScoreOperationPO::getUserCode).toList());
            List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryInfoBySprint(param);
            for(PMIndicatorResponse storyInfo : storyList){
                if (storyInfo.getRequirementName().contains("临时") || storyInfo.getRequirementName().contains("紧急")) {
                    // 跳过当前需求下所有故事的统计
                    continue;
                }
                if(storyInfo.getReviewStatusFirstTime() != null){
                    if(storyInfo.getReviewStatusFirstTime().compareTo(sprint.getStartTime()) > 0){
                        overdueList.add(storyInfo);
                    }
                    storyInfo.setOverdueScore(dealOverdueScore(storyInfo.getReviewStatusFirstTime(), sprint.getStartTime()));
                }
                allList.add(storyInfo);
            }
        }

        for (AITaskScoreOperationPO dto : calcUserList) {
            List<PMIndicatorResponse> productStoryList = allList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), dto.getUserCode())).toList();

            double allStory = productStoryList.stream().mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            double overdueStory = overdueList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), dto.getUserCode()))
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            log.info("===产设超期率=== {} 故事点数：{}，超期故事点：{}", dto.getUserCode(), allStory, overdueStory);
            double indexValue = DecimalUtils.getDoubleRate(overdueStory, allStory);

            double overdueScore = productStoryList.stream().mapToDouble(PMIndicatorResponse::getOverdueScore).sum();
            log.info("===产设超期率得分=== {} 总超期分数：{}，任务个数：{}", dto.getUserCode(), overdueScore, productStoryList.size());
            double indexScore = CollectionUtil.isEmpty(productStoryList) ? 100
                    : DecimalUtils.getDoubleResult(overdueScore / productStoryList.size());

            dto.setRunIndex4(indexValue);
            dto.setRunIndex4Score(indexScore);
        }

        log.info("=== 计算 {} 员工效能-运营结果-产品-指标4-产设超期率 完成 ===", yearMonth);
    }

    /**
     * 计算产设超期率得分 - 单个任务
     */
    private double dealOverdueScore(Date reviewStatusFirstTime, Date sprintStartTime) {
        // 将 Date 转换为 LocalDate
        LocalDate reviewDate = reviewStatusFirstTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate sprintStartDate = sprintStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算日期差（reviewDate - sprintStartDate）
        long daysDiff = ChronoUnit.DAYS.between(sprintStartDate, reviewDate);

        // 根据规则计算得分
        if (daysDiff <= -3) {
            return 100;  // 提前 ≥ 3 天
        } else if (daysDiff == -2) {
            return 90;   // 提前 2 天
        } else if (daysDiff == -1) {
            return 80;   // 提前 1 天
        } else if (daysDiff == 0) {
            return 60;   // 当天完成
        } else if (daysDiff <= 3) {
            return 40;   // 延期 1~3 天
        } else {
            return 0;    // 延期 >3 天
        }
    }

    /**
     * 计算 敏捷管理 运营结果数据
     */
    private void calcSprintCoachScore(String yearMonth, List<AITaskScoreOperationPO> calcUserList) {
        // 筛选 敏捷教练 人员
        List<AITaskScoreOperationPO> resultList = calcUserList.stream()
                .filter(s -> Objects.equals(s.getSubRole(), SubRoleEnum.SPRINT_COACH.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(resultList)) {

            // 敏捷管理 指标1 生产问题复发率
            calcRecurrenceRate(resultList, yearMonth);

            // 敏捷管理 指标2 团队平均交付数量
            calcAvgDeliveryQuantity(resultList, yearMonth);

            // 敏捷管理 指标3 按期交付比率
            calcSprintOutputOnTime(resultList, yearMonth);

            // 保存数据
            saveOperationData(resultList);
        }

    }

    /**
     * 计算 员工效能-运营结果-敏捷教练-指标1-生产问题复发率
     * P0复发数量×0.60 + P1复发数量×0.30 + P2复发数量×0.10）/（P0问题总量×0.60 + P1问题总量×0.30 + P2问题总量×0.10
     */
    private void calcRecurrenceRate(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-敏捷教练-指标1-生产问题复发率 ===", yearMonth);
        // 获取人员列表
        List<String> userList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        // 查询敏捷负责的流程信息
        List<ProductFlowDto> flowList = productFlowMapper.getFLowByProjectManager(userList);

        // 查询当前月份全部运维问题
        OperationSupportInfoDTO dto = new OperationSupportInfoDTO();
        dto.setStartTime(DateUtils.getMonthStartTime(yearMonth));
        dto.setEndTime(DateUtils.getMonthEndTime(yearMonth));
        dto.setBugFlag(1);
        List<OperationSupportInfo> opsList = operationSupportInfoMapper.getOperationSupportInfoList(dto);

        for (AITaskScoreOperationPO userOperation : resultList) {
            // 敏捷负责流程
            List<String> userFlow = flowList.stream()
                    .filter(s -> Objects.equals(s.getPmCode(), userOperation.getUserCode()))
                    .map(ProductFlowDto::getFlowId).distinct().toList();
            // 敏捷运维问题
            List<OperationSupportInfo> pmOpsList = opsList.stream()
                    .filter(s -> userFlow.contains(s.getL3ProcessCode()) || userFlow.contains(s.getL4ProcessCode())).toList();
            Map<String, Long> opsMap = pmOpsList.stream().collect(Collectors.groupingBy(OperationSupportInfo::getUrgencyDegreeCode, Collectors.counting()));
            // 敏捷复发运维问题
            List<OperationSupportInfo> reopenOpsList = pmOpsList.stream().filter(s -> Objects.equals(s.getRecurrenceFlag(), 1)).toList();
            Map<String, Long> reOpsMap = reopenOpsList.stream().collect(Collectors.groupingBy(OperationSupportInfo::getUrgencyDegreeCode, Collectors.counting()));

            double opsValue = opsMap.getOrDefault("P0", 0L) * 0.6
                    + opsMap.getOrDefault("P1", 0L) * 0.3
                    + opsMap.getOrDefault("P2", 0L) * 0.1;

            double reOpsValue = reOpsMap.getOrDefault("P0", 0L) * 0.6
                    + reOpsMap.getOrDefault("P1", 0L) * 0.3
                    + reOpsMap.getOrDefault("P2", 0L) * 0.1;

            // 计算指标
            double runindex = opsValue > 0 ? DecimalUtils.getDoubleRate(reOpsValue, opsValue) : 0;
            userOperation.setRunIndex1(runindex);
            userOperation.setRunIndex1Score(dealRecurrenceScore(runindex));
        }
        log.info("=== 计算 {} 员工效能-运营结果-敏捷教练-指标1-生产问题复发率 完成 ===", yearMonth);
    }

    /**
     * 计算团队生产问题复发率得分
     * TeamDeliveryRate
     */
    private double dealRecurrenceScore(Double runIndex) {
        if (runIndex == null) {
            return 0.0;
        }

        if (runIndex <= 8) {
            return 100.0;  // (-∞, 8] → 100分
        } else if (runIndex < 15) {
            return 80.0;   // (8, 15) → 80分
        } else if (runIndex < 25) {
            return 60.0;   // [15, 25) → 60分
        } else {
            return 40.0;
        }
    }

    /**
     * 计算 运营结果-敏捷教练-指标2-团队平均交付数量
     * @param resultList
     * @param yearMonth
     */
    private void calcAvgDeliveryQuantity(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-敏捷教练-指标2-团队平均交付数量 ===", yearMonth);
        List<String> userCodeList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        // 查询敏捷教练所属团队
        List<GroupInfoPO> groupList = groupInfoMapper.selectByCoachList(userCodeList);
        List<Integer> groupIdList = groupList.stream().map(GroupInfoPO::getId).toList();
        List<String> subTeamList = groupList.stream().map(GroupInfoPO::getGroupName).toList();

        // 查询团队交付故事点
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.getMonthStartTime(yearMonth));
        param.setEndDate(DateUtils.getMonthEndTime(yearMonth));
        param.setGroupIdList(groupIdList);
        List<PMIndicatorResponse> teamStoryPoint = tbDyProjectMapper.getTeamStoryPoint(param);

        // 查询安全、运维交付故事点
        List<PMIndicatorResponse> teamStoryPoint1 = tbDyProjectMapper.getTeamStoryPoint1(param);

        // 查询手工录入的技术、安全需求故事点数
        List<PMIndicatorResponse> teamStoryPoint2 = tbDyProjectMapper.getTeamStoryPoint2(param);

        // 查询团队人数
        List<SysTeamUser> teamUsers = sysTeamUserMapper.getSubTeamUserList(subTeamList);

        for(AITaskScoreOperationPO userOperation : resultList){
            GroupInfoPO groupInfo = groupList.stream().filter(s -> Objects.equals(s.getPmCode(), userOperation.getUserCode())).findFirst().orElse(null);
            if(groupInfo != null){
                double storyPoint = teamStoryPoint.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                double storyPoint1 = teamStoryPoint1.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                double storyPoint2 = teamStoryPoint2.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                long teamUser = teamUsers.stream().filter(s -> Objects.equals(s.getHomeDevTeam(), userOperation.getSubTeam())).count();

                double value = DecimalUtils.getDoubleResult((storyPoint + storyPoint1 + storyPoint2) / teamUser);
                userOperation.setRunIndex2(value);
                userOperation.setRunIndex2Score(dealTDRScore(value));
            } else {
                userOperation.setRunIndex2(0D);
                userOperation.setRunIndex2Score(0D);
            }
        }

        log.info("=== 计算 {} 员工效能-运营结果-敏捷教练-指标2-团队平均交付数量 完成 ===", yearMonth);
    }

    /**
     * 计算团队平均交付质量得分
     * TeamDeliveryRate
     */
    private double dealTDRScore(Double runIndex) {
        if (runIndex == null) {
            // 处理null值情况，可以根据实际需求返回默认值或抛出异常
            return 0.0;
        }

        if (runIndex > 3) {
            return 100.0;  // 优：(3, +∞) → 100分
        } else if (runIndex > 2.0) {
            return 80.0;   // 良：(2, 3] → 80分
        } else if (runIndex > 1.0) {
            return 60.0;   // 中：(1, 2] → 60分
        } else if (runIndex > 0.0) {
            return 0.0;    // 差：(0.5, 1] → 0分
        } else {
            // 处理非正数情况，可以根据实际需求返回默认值
            return 0.0;
        }
    }

    /**
     * 计算 员工效能-运营结果-敏捷教练-指标3-按期交付比率
     * 按期交付需求故事点数量 / 应交付需求故事点数量
     * 按期交付： devops首次变更为完成得时间 < 需求池填写得故事计划完成时间
     * 取数范围： 计划完成时间在迭代时间内 + 计划完成时间为空 且 devops完成时间在迭代时间内
     */
    private void calcSprintOutputOnTime(List<AITaskScoreOperationPO> resultList, String yearMonth) {
        log.info("=== 计算 {} 员工效能-运营结果-敏捷教练-指标3-按期交付比率 完成 ===", yearMonth);
        List<String> userCodeList = resultList.stream().map(AITaskScoreOperationPO::getUserCode).toList();

        // 查询敏捷教练所属团队
        List<GroupInfoPO> groupList = groupInfoMapper.selectByCoachList(userCodeList);
        List<Integer> groupIdList = groupList.stream().map(GroupInfoPO::getId).toList();

        // 查询团队交付故事点
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.getMonthStartTime(yearMonth));
        param.setEndDate(DateUtils.getMonthEndTime(yearMonth));
        param.setGroupIdList(groupIdList);
        List<PMIndicatorResponse> teamStoryList = tbDyProjectMapper.queryStoryInfoBySprint(param);

        for (AITaskScoreOperationPO userOperation : resultList) {
            GroupInfoPO groupInfo = groupList.stream().filter(s -> Objects.equals(s.getPmCode(), userOperation.getUserCode())).findFirst().orElse(null);
            if(groupInfo != null){
                // 团队应交付故事点
                double planStoryPoint = teamStoryList.stream()
                        .filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                // 团队已交付故事点
                double actualStoryPoint = teamStoryList.stream()
                        .filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .filter(s -> s.getStoryOnlineDate() != null && s.getCompleteTime() != null)
                        .filter(s -> !s.getCompleteTime().after(s.getStoryOnlineDate()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();

                log.info("===按期交付比率=== {} 团队应交付故事点：{}，团队已交付故事点：{}", userOperation.getUserCode(), planStoryPoint, actualStoryPoint);
                double indexValue = DecimalUtils.getDoubleRate(actualStoryPoint, planStoryPoint);
                userOperation.setRunIndex3(indexValue);
                userOperation.setRunIndex3Score(dealOnTimeScore(indexValue));
            } else {
                userOperation.setRunIndex3(0D);
                userOperation.setRunIndex3Score(0D);
            }
        }
    }

    /**
     * 计算按期交付比率得分
     * TeamDeliveryRate
     */
    private double dealOnTimeScore(Double runIndex) {
        if (runIndex >= 97) {
            return 100.0;
        } else if (runIndex >= 90) {
            return 80.0;
        } else if (runIndex >= 80) {
            return 60.0;
        } else if (runIndex >= 60) {
            return 40.0;
        } else if (runIndex >= 40) {
            return 20.0;
        } else {
            return 0.0;
        }
    }

    /**
     * 计算交付质量数据
     */
    @Override
    public void calcDigitalIndicator(CalcIndicatorDTO dto, SprintInfoPO sprintInfo) {

        // 查询待计算人员范围
        List<SysTeamUser> userList = sysTeamUserMapper.getItPmUserListNew(dto.getUserCodeList());

        // 计算 产品管理 指标数据
        calcProductManagerBySprint(sprintInfo, userList);

        // 计算 敏捷教练 指标数据
        calcProjectManagerBySprint(sprintInfo, userList);

        // 计算 技术架构师 指标数据
        calcArchitectBySprint(sprintInfo, userList);

        // 计算 前后端 指标数据
        calcFrontAndBackBySprint(sprintInfo, userList);

        // 计算 测试 指标数据
        calcTesterBySprint(sprintInfo, userList);

    }

    @Override
    public Map<String, Double> getPersonalScoreByPeriod(List<SearchTaskEvaDTO> request) {

        Map<String, Double> scoreMap = new HashMap<>();

        Map<String, String> userBillMap = request.stream()
                .collect(Collectors.toMap(
                        SearchTaskEvaDTO::getAccount,
                        SearchTaskEvaDTO::getTaskBillCode,
                        (existing, replacement) -> existing
                ));

        List<String> accounts = request.stream().map(SearchTaskEvaDTO::getAccount).toList();
        List<AITaskScoreEvaluationItPmPO> aiTaskScoreEvaluationItPmList = aiTaskScoreEvaluationMapper.selectItPmEvaluationByUserCode(accounts);

        if (CollectionUtil.isEmpty(aiTaskScoreEvaluationItPmList)) {
            return scoreMap;
        }

        for (AITaskScoreEvaluationItPmPO evaluation : aiTaskScoreEvaluationItPmList) {

            String account = evaluation.getUserCode();
            String reqTaskBillCode = userBillMap.get(account);
            String taskBillCode = evaluation.getTaskBillCode();

            if (taskBillCode != null && taskBillCode.equals(reqTaskBillCode)) {
                scoreMap.put(account, evaluation.getTaskTotalScore());
            }
        }

        return scoreMap;
    }

    @Override
    public Boolean calculateUserEvaluation(List<CalculateUserRequest> list) {

        if (CollectionUtil.isNotEmpty(list)) {
            List<ThirdPartyUserRequest> requestList = new ArrayList<>();

            for (CalculateUserRequest calculateUserRequest : list) {
                ThirdPartyUserRequest request = new ThirdPartyUserRequest();
                BeanUtils.copyProperties(calculateUserRequest, request);
                requestList.add(request);
            }

            aiTaskScoreEvaluationService.updateThirdPartyUserScore(requestList);
        }

        return Boolean.TRUE;
    }

    /**
     * 计算 敏捷教练指标数据
     *
     * @param sprint   冲刺信息
     * @param userList 用户列表
     */
    private void calcProjectManagerBySprint(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 敏捷教练 迭代指标数据 ===", sprint.getSprintName());
        // 筛选 敏捷教练 人员
        List<SysTeamUser> projectManagers = userList.stream()
                .filter(s -> Objects.equals(s.getSubdividedRole(), SubRoleEnum.SPRINT_COACH.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(projectManagers)) {

            // 敏捷管理 指标1 生产问题复发率
            calcRecurrenceRate(sprint, projectManagers);

            // 敏捷管理 指标2 团队平均交付数量
            calcAvgDeliveryQuantity(sprint, projectManagers);

            // 敏捷管理 指标3 按期交付比率
            calcSprintOutputOnTime(sprint, projectManagers);
        }
        log.info("=== 计算 {} 敏捷教练 交付质量数据 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-敏捷教练-指标1-生产问题复发率
     * P0复发数量×0.60 + P1复发数量×0.30 + P2复发数量×0.10）/（P0问题总量×0.60 + P1问题总量×0.30 + P2问题总量×0.10
     */
    private void calcRecurrenceRate(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-敏捷教练-指标1-生产问题复发率 ===", sprint.getSprintName());
        List<String> userCodeList = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        // 查询敏捷负责的流程信息
        List<ProductFlowDto> flowList = productFlowMapper.getFLowByProjectManager(userCodeList);

        // 查询当前月份全部运维问题
        OperationSupportInfoDTO dto = new OperationSupportInfoDTO();
        dto.setStartTime(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
        dto.setEndTime(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        dto.setBugFlag(1);
        List<OperationSupportInfo> opsList = operationSupportInfoMapper.getOperationSupportInfoList(dto);

        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            // 敏捷负责流程
            List<String> userFlow = flowList.stream().filter(s -> Objects.equals(s.getPmCode(), user.getLoginAccount()))
                    .map(ProductFlowDto::getFlowId).distinct().toList();
            // 敏捷运维问题
            List<OperationSupportInfo> pmOpsList = opsList.stream().filter(s -> userFlow.contains(s.getL3ProcessCode()) || userFlow.contains(s.getL4ProcessCode())).toList();
            Map<String, Long> opsMap = pmOpsList.stream().collect(Collectors.groupingBy(OperationSupportInfo::getUrgencyDegreeCode, Collectors.counting()));
            // 敏捷复发运维问题
            List<OperationSupportInfo> reopenOpsList = pmOpsList.stream().filter(s -> Objects.equals(s.getRecurrenceFlag(), 1)).toList();
            Map<String, Long> reOpsMap = reopenOpsList.stream().collect(Collectors.groupingBy(OperationSupportInfo::getUrgencyDegreeCode, Collectors.counting()));

            double opsValue = opsMap.getOrDefault("P0", 0L) * 0.6
                    + opsMap.getOrDefault("P1", 0L) * 0.3
                    + opsMap.getOrDefault("P2", 0L) * 0.1;

            double reOpsValue = reOpsMap.getOrDefault("P0", 0L) * 0.6
                    + reOpsMap.getOrDefault("P1", 0L) * 0.3
                    + reOpsMap.getOrDefault("P2", 0L) * 0.1;

            // 计算指标
            double runindex3 = opsValue > 0 ? DecimalUtils.getDoubleRate(reOpsValue, opsValue) : 0;
            dataList.add(setDigitalIndicatorScore(user, sprint, "SCWTFFL", "生产问题复发率",
                    BigDecimal.valueOf(runindex3), BigDecimal.valueOf(dealRecurrenceScore(runindex3))));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "SCWTFFL");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 交付质量-敏捷教练-指标1-生产问题复发率 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-敏捷教练-指标2-团队平均交付数量
     */
    private void calcAvgDeliveryQuantity(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-敏捷教练-指标2-团队平均交付数量 ===", sprint.getSprintName());
        List<String> userCodeList = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        // 查询敏捷教练所属团队
        List<GroupInfoPO> groupList = groupInfoMapper.selectByCoachList(userCodeList);
        List<Integer> groupIdList = groupList.stream().map(GroupInfoPO::getId).toList();
        List<String> subTeamList = groupList.stream().map(GroupInfoPO::getGroupName).toList();

        // 查询团队交付故事点
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setEndDate(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setGroupIdList(groupIdList);
        List<PMIndicatorResponse> teamStoryPoint = tbDyProjectMapper.getTeamStoryPoint(param);

        // 查询安全、运维交付故事点
        List<PMIndicatorResponse> teamStoryPoint1 = tbDyProjectMapper.getTeamStoryPoint1(param);

        // 查询手工录入的技术、安全需求故事点数
        List<PMIndicatorResponse> teamStoryPoint2 = tbDyProjectMapper.getTeamStoryPoint2(param);

        // 查询团队人数
        List<SysTeamUser> teamUsers = sysTeamUserMapper.getSubTeamUserList(subTeamList);

        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for(SysTeamUser user : userList){
            GroupInfoPO groupInfo = groupList.stream().filter(s -> Objects.equals(s.getPmCode(), user.getLoginAccount())).findFirst().orElse(null);
            if(groupInfo != null){
                double storyPoint = teamStoryPoint.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                double storyPoint1 = teamStoryPoint1.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                double storyPoint2 = teamStoryPoint2.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                long teamUser = teamUsers.stream().filter(s -> Objects.equals(s.getHomeDevTeam(), user.getHomeDevTeam())).count();

                double value = DecimalUtils.getDoubleResult((storyPoint + storyPoint1 + storyPoint2) / teamUser);
                dataList.add(setDigitalIndicatorScore(user, sprint, "TDPJJFSL", "团队平均交付数量",
                        BigDecimal.valueOf(value), BigDecimal.valueOf(dealTDRScore(value))));
            } else {
                dataList.add(setDigitalIndicatorScore(user, sprint, "TDPJJFSL", "团队平均交付数量", BigDecimal.valueOf(0), BigDecimal.valueOf(0)));
            }
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "TDPJJFSL");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 交付质量-敏捷教练-指标2-团队平均交付数量 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-敏捷教练 指标3 按期交付比率
     */
    private void calcSprintOutputOnTime(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-敏捷教练-指标3-按期交付比率 ===", sprint.getSprintName());
        List<String> userCodeList = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        // 查询敏捷教练所属团队
        List<GroupInfoPO> groupList = groupInfoMapper.selectByCoachList(userCodeList);
        List<Integer> groupIdList = groupList.stream().map(GroupInfoPO::getId).toList();

        // 查询团队交付故事点
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setEndDate(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setGroupIdList(groupIdList);
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryInfoBySprint(param);

        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            GroupInfoPO groupInfo = groupList.stream().filter(s -> Objects.equals(s.getPmCode(), user.getLoginAccount())).findFirst().orElse(null);
            if(groupInfo != null){
                // 团队应交付故事点
                double planStoryPoint = storyList.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
                // 团队已交付故事点
                double actualStoryPoint = storyList.stream().filter(s -> Objects.equals(s.getGroupId(), groupInfo.getId()))
                        .filter(s -> s.getStoryOnlineDate() != null && s.getCompleteTime() != null)
                        .filter(s -> !s.getCompleteTime().after(s.getStoryOnlineDate()))
                        .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();

                log.info("===按期交付比率=== {} 团队应交付故事点：{}，团队已交付故事点：{}", user.getLoginAccount(), planStoryPoint, actualStoryPoint);
                double indexValue = DecimalUtils.getDoubleRate(actualStoryPoint, planStoryPoint);
                dataList.add(setDigitalIndicatorScore(user, sprint, "AQJFBL", "按期交付比率",
                        BigDecimal.valueOf(indexValue), BigDecimal.valueOf(dealOnTimeScore(indexValue))));
            } else {
                dataList.add(setDigitalIndicatorScore(user, sprint, "AQJFBL", "按期交付比率", BigDecimal.valueOf(0), BigDecimal.valueOf(0)));
            }
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "AQJFBL");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }
        log.info("=== 计算 {} 交付质量-敏捷教练-指标3-按期交付比率 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 产品管理 迭代指标数据
     */
    private void calcProductManagerBySprint(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 产品 交付质量数据 ===", sprint.getSprintName());

        // 筛选 产品 人员
        List<SysTeamUser> productList = userList.stream().filter(s -> Objects.equals(s.getSubdividedRole(), SubRoleEnum.PRODUCT_MANAGER.getDesc())).toList();

        if (CollectionUtil.isNotEmpty(productList)) {
            // 产品管理 指标1 产设一次通过率
            productManageFirstIndex(sprint, productList);

            // 产品管理 指标2 导入故事点数
            productManageSecondIndex(sprint, productList);

            // 产品管理 指标3 产设开口项评分
            productManageThirdIndex(sprint, productList);

            // 产品管理 指标4 产设超期率
            productManageFourIndex(sprint, productList);
        }

        log.info("=== 计算 {} 产品 交付质量数据 完成 ===", sprint.getSprintName());
    }

    /**
     * 交付质量 - 产品管理 指标1 产设一次通过率
     */
    private void productManageFirstIndex(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-产品-指标1-产设一次通过率 ===", sprint.getSprintName());

        // 查询需求故事点数合计
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setSprintName(sprint.getSprintName());
        param.setUserCodeList(userList.stream().map(SysTeamUser::getLoginAccount).toList());
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryPointBySprint(param);

        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            List<PMIndicatorResponse> pmStoryList = storyList.stream().filter(s -> Objects.equals(s.getAssignedUserName(), user.getLoginAccount())).toList();
            // 总故事点
            double totalStoryPoints = pmStoryList.stream().mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            // 通过的故事点
            double passStoryPoints = pmStoryList.stream()
                    .filter(s -> s != null && s.getReviewStatusFirst() != null)
                    .filter(s -> s.getReviewStatusFirst() == 1)
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            // 有条件通过的故事点
            double resPassStoryPoints = pmStoryList.stream()
                    .filter(s -> s != null && s.getReviewStatusFirst() != null)
                    .filter(s -> s.getReviewStatusFirst() == 2)
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            double passNum = passStoryPoints + resPassStoryPoints * 0.5;
            log.info("=== 计算产设一次通过率 === {} 总故事点：{}， 通过故事点：{}", user.getLoginAccount(), totalStoryPoints, passNum);
            double indexValue = DecimalUtils.getDoubleRate(passNum, totalStoryPoints);

            // 写入数据
            dataList.add(setDigitalIndicatorScore(user, sprint, "CSYCTGL", "产设一次通过率",
                    BigDecimal.valueOf(indexValue), BigDecimal.valueOf(indexValue)));

        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "CSYCTGL");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }
        log.info("=== 计算 {} 交付质量-产品-指标1-产设一次通过率 完成 ===", sprint.getSprintName());
    }

    /**
     * 产品管理 指标2 导入故事点数
     */
    private void productManageSecondIndex(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-产品-指标2-导入故事点数 === ", sprint.getSprintName());

        // 查询需求故事点数合计
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setSprintName(sprint.getSprintName());
        param.setUserCodeList(userList.stream().map(SysTeamUser::getLoginAccount).toList());
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryPointBySprint(param);

        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            double totalStoryPoints = storyList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), user.getLoginAccount()))
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();

            // 写入数据
            dataList.add(setDigitalIndicatorScore(user, sprint, "DRGSDS", "导入故事点数",
                    BigDecimal.valueOf(totalStoryPoints), BigDecimal.valueOf(calProductIndex2(totalStoryPoints))));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "DRGSDS");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }
        log.info("=== 计算 {} 交付质量-产品-指标2-导入故事点数 完成 ===", sprint.getSprintName());
    }

    /**
     * 产品管理 指标3 产设开口项评分
     */
    private void productManageThirdIndex(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-产品-指标3-产设开口项评分 === 迭代：", sprint.getSprintName());

        // 查询需求故事点数合计
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setSprintName(sprint.getSprintName());
        param.setUserCodeList(userList.stream().map(SysTeamUser::getLoginAccount).toList());
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryPointBySprint(param);

        // 查询流程问题数
        List<PMIndicatorResponse> openItemList = tbDyProjectMapper.queryOpenItemBySprint(param);

        // 计算公式 产设开口项评分=100−((核心流程问题项数×1.5)+(非核心流程问题项数×1.0)/需求故事点数合计值×100)
        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            // 故事点
            double totalStoryPoints = storyList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), user.getLoginAccount()))
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();

            // 核心开口项
            double coreCount = openItemList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), user.getLoginAccount()))
                    .filter(s -> Objects.equals(s.getOpenItemType(), CalcProductManageConstant.CORE_FLOW))
                    .mapToDouble(PMIndicatorResponse::getOpenItemTypeCount).sum();

            // 非核心开口项
            double nonCoreCount = openItemList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), user.getLoginAccount()))
                    .filter(s -> Objects.equals(s.getOpenItemType(), CalcProductManageConstant.NON_CORE_FLOW))
                    .mapToDouble(PMIndicatorResponse::getOpenItemTypeCount).sum();

            log.info("===产设开口项评分=== {} 故事点数：{}，核心开口项：{}，非核心开口项：{}", user.getLoginAccount(), totalStoryPoints, coreCount, nonCoreCount);
            double indexValue = 100 - DecimalUtils.getDoubleRate(coreCount * 1.5 + nonCoreCount, totalStoryPoints);

            dataList.add(setDigitalIndicatorScore(user, sprint, "CSKKXPF", "产设开口项评分",
                    BigDecimal.valueOf(indexValue), BigDecimal.valueOf(indexValue)));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "CSKKXPF");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 交付质量-产品-指标3-产设开口项评分 完成 ===", sprint.getSprintName());
    }

    /**
     * 交付质量 产品管理 指标4 产设超期率
     * 超期评审故事的故事点数 / 迭代时间内的故事的故事点数
     * 故事点： 计划完成时间在迭代时间内 + 计划完成时间为空 且 devops完成时间在迭代时间内
     * 超期评审： 需求池首次评审通过时间 大于 所属迭代开始时间
     */
    private void productManageFourIndex(SprintInfoPO sprint, List<SysTeamUser> userList){
        log.info("=== 计算 {} 交付质量-产品-指标4-产设超期率 ===", sprint.getSprintName());
        List<PMIndicatorResponse> allList = new ArrayList<>();
        List<PMIndicatorResponse> overdueList = new ArrayList<>();

        // 查询本迭代内产品创建故事
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setEndDate(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setUserCodeList(userList.stream().map(SysTeamUser::getLoginAccount).toList());
        List<PMIndicatorResponse> storyList = tbDyProjectMapper.queryStoryInfoBySprint(param);
        for(PMIndicatorResponse storyInfo : storyList){
            if (storyInfo.getRequirementName().contains("临时") || storyInfo.getRequirementName().contains("紧急")) {
                // 跳过当前需求下所有故事的统计
                continue;
            }
            if(storyInfo.getReviewStatusFirstTime() != null){
                if(storyInfo.getReviewStatusFirstTime().compareTo(sprint.getStartTime()) > 0){
                    overdueList.add(storyInfo);
                }
                storyInfo.setOverdueScore(dealOverdueScore(storyInfo.getReviewStatusFirstTime(), sprint.getStartTime()));
            }
            allList.add(storyInfo);
        }

        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            List<PMIndicatorResponse> productStoryList = allList.stream()
                    .filter(s -> Objects.equals(s.getAssignedUserName(), user.getLoginAccount())).toList();

            double allStory = productStoryList.stream().mapToDouble(PMIndicatorResponse::getStoryPoint).sum();
            double overdueStory = overdueList.stream().filter(s -> Objects.equals(s.getAssignedUserName(), user.getLoginAccount()))
                    .mapToDouble(PMIndicatorResponse::getStoryPoint).sum();

            log.info("===产设超期率=== {} 故事点数：{}，超期故事点：{}", user.getLoginAccount(), allStory, overdueStory);
            double indexValue = DecimalUtils.getDoubleRate(overdueStory, allStory);

            double overdueScore = productStoryList.stream().mapToDouble(PMIndicatorResponse::getOverdueScore).sum();
            log.info("===产设超期率得分=== {} 总超期分数：{}，任务个数：{}", user.getLoginAccount(), overdueScore, productStoryList.size());
            double indexScore = CollectionUtil.isEmpty(productStoryList) ? 100
                    : DecimalUtils.getDoubleResult(overdueScore / productStoryList.size());

            dataList.add(setDigitalIndicatorScore(user, sprint, "CSCQL", "产设超期率",
                    BigDecimal.valueOf(indexValue), BigDecimal.valueOf(indexScore)));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "CSCQL");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 交付质量-产品-指标4-产设超期率 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 架构师 迭代指标数据
     *
     * @param sprint   迭代
     * @param userList 用户列表
     */
    private void calcArchitectBySprint(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 架构师 迭代指标数据 ===", sprint.getSprintName());
        // 筛选人员
        List<SysTeamUser> architectList = userList.stream()
                .filter(s -> Objects.equals(s.getSubdividedRole(), SubRoleEnum.TECHNICAL_ARCHITECT.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(architectList)) {

            // 指标1 和 指标2 特殊处理需要1年的迭代
            LocalDate endDate = LocalDate.now();
            LocalDate startDate1 = endDate.minusDays(180);
            LocalDate startDate2 = endDate.minusDays(365);
            List<SprintInfoPO> sprints1 = sprintInfoMapper.getSprintInfoByPeriod(startDate1.format(DateTimeFormatter.ISO_DATE),
                    endDate.format(DateTimeFormatter.ISO_DATE));
            List<SprintInfoPO> sprints2 = sprintInfoMapper.getSprintInfoByPeriod(startDate2.format(DateTimeFormatter.ISO_DATE),
                    endDate.format(DateTimeFormatter.ISO_DATE));

            // 指标1计算
            calcArchitectIssue(sprints1, architectList);

            // 指标2计算
            calcSateIssue(sprints2, architectList);

            // 指标3计算
            calcArchitectDesign(sprint, architectList);

            // 指标4计算
            calcSystemPerformance(sprint, architectList);
        }
        log.info("=== 计算 {} 架构师 交付质量数据 完成 ===", sprint.getSprintName());
    }

    /**
     * 架构师 指标4 计算 - 系统稳定性评分
     *
     * @param sprint 冲刺
     * @param architectList 架构师
     */
    private void calcSystemPerformance(SprintInfoPO sprint, List<SysTeamUser> architectList) {
        List<String> userCodes = architectList.stream()
                .map(SysTeamUser::getLoginAccount)
                .toList();

        // 查询上一个迭代
        SprintInfoPO lastSprint = sprintInfoMapper.selectLastSprint(sprint.getSprintName());
        if (lastSprint == null) {
            return;
        }
        // 查询当前迭代和上一个迭代的系统指标数据
        List<AppPerformanceMetricPO> currentMetrics = appPerformanceMetricMapper.selectByDateAndSubLeader(
                sprint.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                sprint.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                userCodes
        );

        List<AppPerformanceMetricPO> lastMetrics = appPerformanceMetricMapper.selectByDateAndSubLeader(
                lastSprint.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                lastSprint.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                userCodes
        );

        // 按用户分组计算
        Map<String, List<AppPerformanceMetricPO>> currentMonthByUser = currentMetrics.stream()
                .filter(item -> item.getApdex() != 0)
                .collect(Collectors.groupingBy(AppPerformanceMetricPO::getSubLeaderAccount));
        Map<String, List<AppPerformanceMetricPO>> lastMonthByUser = lastMetrics.stream()
                .filter(item -> item.getApdex() != 0)
                .collect(Collectors.groupingBy(AppPerformanceMetricPO::getSubLeaderAccount));

        // 计算每个用户的得分并保存
        List<DigitalIndicatorScore> scores = new ArrayList<>();
        for (SysTeamUser architect : architectList) {
            String userCode = architect.getLoginAccount();
            List<AppPerformanceMetricPO> currentUserMetrics = currentMonthByUser.getOrDefault(userCode, Collections.emptyList());
            List<AppPerformanceMetricPO> lastUserMetrics = lastMonthByUser.getOrDefault(userCode, Collections.emptyList());

            double finalScore = calculateSystemPerformanceScore(currentUserMetrics, lastUserMetrics);

            // 创建并添加得分记录
            scores.add(setDigitalIndicatorScore(architect, sprint, "XTWDXPF", "系统稳定性评分",
                    BigDecimal.valueOf(finalScore), BigDecimal.valueOf(finalScore)));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!scores.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"),
                    sprint.getSprintName(), "XTWDXPF");
            digitalIndicatorScoreDao.batchInsertDataList(scores);
        }

    }

    /**
     * 计算系统稳定性评分
     * @param currentMetrics 当前周期的指标数据
     * @param lastMetrics 上一周期的指标数据
     * @return 最终得分
     */
    private double calculateSystemPerformanceScore(List<AppPerformanceMetricPO> currentMetrics, List<AppPerformanceMetricPO> lastMetrics) {
        // 当前没有指标数据，则返回100分
        if (CollectionUtil.isEmpty(currentMetrics)) {
            return 100.00;
        }
        // 计算本月和上月的平均稳定性评价分
        double currentMonthScore = currentMetrics.stream()
                .mapToDouble(AppPerformanceMetricPO::getApdex)
                .average()
                .orElse(0.0);
        double lastMonthScore = lastMetrics.stream()
                .mapToDouble(AppPerformanceMetricPO::getApdex)
                .average()
                .orElse(0.0);

        // 计算提升度
        double improvementRate = lastMonthScore == 0 ? 0 : ((currentMonthScore - lastMonthScore) / lastMonthScore) * 100;

        // 计算提升激励分
        double incentiveScore = 0;
        if (improvementRate > 10) {
            incentiveScore = 20;
        } else if (improvementRate > 5) {
            incentiveScore = 10;
        } else if (improvementRate < -10) {
            incentiveScore = -20;
        } else if (improvementRate < -5) {
            incentiveScore = -10;
        }

        // 计算最终得分
        double finalScore = currentMonthScore * 100 + incentiveScore;
        return Math.round(Math.min(100, Math.max(0, finalScore)) * 100.0) / 100.0;
    }

    /**
     * 架构师 指标3 计算 - 设计类交付工时占比
     *
     * @param sprint        迭代列表
     * @param architectList 架构师名单
     */
    private void calcArchitectDesign(SprintInfoPO sprint, List<SysTeamUser> architectList) {
        List<String> userCodes = architectList.stream()
                .map(SysTeamUser::getLoginAccount)
                .collect(Collectors.toList());

        // 根据迭代名称查询对应的迭代id
        List<Long> sprintIds = tbDySprintMapper.selectIdsBySprintName(sprint.getSprintName());
        if (CollectionUtil.isEmpty(sprintIds)) {
            return;
        }

        // 根据迭代id及用户域账号列表查询工时数据
        List<TaskBusHoursDTO> busHoursList = tbDyProjectMapper.selectBusinessAndConsumerHourBySprints(sprintIds, userCodes);

        // 使用流式处理统计工时
        Map<String, Double[]> userStats = busHoursList.stream()
                .filter(dto -> dto.getConsumerHours() != null)
                .collect(Collectors.groupingBy(
                        TaskBusHoursDTO::getLoginAccount,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    double total = list.stream().mapToDouble(TaskBusHoursDTO::getConsumerHours).sum();
                                    double docTotal = list.stream()
                                            .filter(dto -> CommonConstant.DOC_BUSINESS_UNIT.contains(dto.getBusinessUnit()))
                                            .mapToDouble(TaskBusHoursDTO::getConsumerHours)
                                            .sum();
                                    return new Double[]{total, docTotal};
                                }
                        )
                ));

        // 设置得分
        List<DigitalIndicatorScore> scores = new ArrayList<>();
        architectList.forEach(architect -> {
            String userCode = architect.getLoginAccount();
            Double[] stats = userStats.get(userCode);
            double ratio;
            if (stats != null && stats[0] != null && stats[0] > 0) {
                ratio = DecimalUtils.getDoubleRate(stats[1], stats[0]);
            } else {
                ratio = 0D;
            }
            scores.add(setDigitalIndicatorScore(architect, sprint, "SJLJFGSZB", "设计类交付工时占比",
                    BigDecimal.valueOf(ratio), BigDecimal.valueOf(dealDesignScore(ratio))));
        });

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!scores.isEmpty()) {

            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"),
                    sprint.getSprintName(), "SJLJFGSZB");

            // 批量插入新的计算结果
            digitalIndicatorScoreDao.batchInsertDataList(scores);

            log.info("=== 设计类交付工时占比数据 === 迭代: {}, 记录数: {}", sprint.getSprintName(), scores.size());
        }
    }

    /**
     * 架构师 指标2 计算 - 安全问题整改率
     *
     * @param sprints       迭代列表
     * @param architectList 架构师名单
     */
    private void calcSateIssue(List<SprintInfoPO> sprints, List<SysTeamUser> architectList) {
        if (CollectionUtils.isEmpty(sprints) || CollectionUtils.isEmpty(architectList)) {
            return;
        }

        // 获取架构师用户列表
        List<String> userCodes = architectList.stream()
                .map(SysTeamUser::getLoginAccount)
                .collect(Collectors.toList());

        // 先按开始时间排序sprints
        sprints.sort(Comparator.comparing(SprintInfoPO::getStartTime));

        // 查询所有问题数据
        List<SafeRiskPO> safeRiskList = safeRiskMapper.selectByLaunchDateAndImplement(
                LocalDateTime.ofInstant(sprints.get(0).getStartTime().toInstant(), ZoneId.systemDefault()),
                LocalDateTime.ofInstant(sprints.get(sprints.size() - 1).getEndTime().toInstant(), ZoneId.systemDefault()),
                userCodes);

        // 按迭代分组
        Map<String, List<SafeRiskPO>> issuesBySprint = safeRiskList.stream()
                .map(issue -> {
                    Optional<String> sprintName = sprints.stream()
                            .filter(sprint -> {
                                LocalDateTime start = sprint.getStartTime().toInstant()
                                        .atZone(ZoneId.systemDefault()).toLocalDateTime();
                                LocalDateTime end = sprint.getEndTime().toInstant()
                                        .atZone(ZoneId.systemDefault()).toLocalDateTime();
                                LocalDateTime created = issue.getDateCreated();
                                return created.isAfter(start) && created.isBefore(end);
                            })
                            .findFirst()
                            .map(SprintInfoPO::getSprintName);
                    return new AbstractMap.SimpleEntry<>(sprintName.orElse(null), issue);
                })
                .filter(entry -> entry.getKey() != null)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
        for (SprintInfoPO sprint : sprints) {
            String sprintName = sprint.getSprintName();
            issuesBySprint.computeIfAbsent(sprintName, k -> new ArrayList<>());
        }

        // 按架构师分组计算每个迭代的完成比值
        for (Map.Entry<String, List<SafeRiskPO>> entry : issuesBySprint.entrySet()) {
            String sprintName = entry.getKey();
            SprintInfoPO sprintInfo = sprints.stream()
                    .filter(s -> s.getSprintName().equals(sprintName))
                    .findFirst()
                    .orElse(null);
            List<SafeRiskPO> sprintIssues = entry.getValue();

            // 按架构师分组
            Map<String, List<SafeRiskPO>> issuesByUser = sprintIssues.stream()
                    .collect(Collectors.groupingBy(SafeRiskPO::getResponsiblePersonAccount));

            List<DigitalIndicatorScore> scores = new ArrayList<>();
            for (SysTeamUser architect : architectList) {
                List<SafeRiskPO> userIssues = issuesByUser.getOrDefault(architect.getLoginAccount(), Collections.emptyList());
                double score = calculateSafeIssueScore(userIssues);
                scores.add(setDigitalIndicatorScore(architect, sprintInfo, "AQWTZGL", "安全问题整改率",
                        BigDecimal.valueOf(score), BigDecimal.valueOf(score)));
            }

            // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
            if (!scores.isEmpty()) {
                digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprintInfo.getStartTime(), "yyyy"),
                        sprintName, "AQWTZGL");

                // 批量插入新的计算结果
                digitalIndicatorScoreDao.batchInsertDataList(scores);
            }
        }
    }

    /**
     * 架构师 指标1 计算 - 架构问题整改率
     *
     * @param sprints       迭代列表
     * @param architectList 架构师名单
     */
    private void calcArchitectIssue(List<SprintInfoPO> sprints, List<SysTeamUser> architectList) {
        if (CollectionUtils.isEmpty(sprints) || CollectionUtils.isEmpty(architectList)) {
            return;
        }

        // 获取架构师用户列表
        List<String> userCodes = architectList.stream()
                .map(SysTeamUser::getLoginAccount)
                .collect(Collectors.toList());

        // 先按开始时间排序sprints
        sprints.sort(Comparator.comparing(SprintInfoPO::getStartTime));

        // 查询所有问题数据
        List<PlatformIssuePO> allIssues = platformIssueMapper.selectByLaunchDateAndImplement(
                DateUtils.formatDate(sprints.get(0).getStartTime(), "yyyy-MM-dd"),
                DateUtils.formatDate(sprints.get(sprints.size() - 1).getEndTime(), "yyyy-MM-dd"),
                userCodes
        );

        // 按迭代分组
        Map<String, List<PlatformIssuePO>> issuesBySprint = allIssues.stream()
                .map(issue -> {
                    Optional<String> sprintName = sprints.stream()
                            .filter(sprint -> issue.getRectificationLaunchDate().after(sprint.getStartTime())
                                    && issue.getRectificationLaunchDate().before(sprint.getEndTime()))
                            .findFirst()
                            .map(SprintInfoPO::getSprintName);
                    return new AbstractMap.SimpleEntry<>(sprintName.orElse(null), issue);
                })
                .filter(entry -> entry.getKey() != null)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
        for (SprintInfoPO sprint : sprints) {
            String sprintName = sprint.getSprintName();
            issuesBySprint.computeIfAbsent(sprintName, k -> new ArrayList<>());
        }
        // 按架构师分组计算每个迭代的完成比值
        for (Map.Entry<String, List<PlatformIssuePO>> entry : issuesBySprint.entrySet()) {
            String sprintName = entry.getKey();
            SprintInfoPO sprintInfo = sprints.stream()
                    .filter(s -> s.getSprintName().equals(sprintName))
                    .findFirst()
                    .orElse(new SprintInfoPO());
            List<PlatformIssuePO> sprintIssues = entry.getValue();

            // 按架构师分组
            Map<String, List<PlatformIssuePO>> issuesByUser = sprintIssues.stream()
                    .filter(issue -> issue.getSubLeaderAccount() != null)
                    .collect(Collectors.groupingBy(PlatformIssuePO::getSubLeaderAccount));

            // 批量设置得分
            Date endDateTime = sprintInfo.getEndTime();
            List<DigitalIndicatorScore> scores = new ArrayList<>();
            architectList.forEach(architect -> {
                List<PlatformIssuePO> userIssues = issuesByUser.getOrDefault(architect.getLoginAccount(), Collections.emptyList());
                double score = calculateArchitectIssueScore(userIssues, endDateTime);
                scores.add(setDigitalIndicatorScore(architect, sprintInfo, "JGWTZGL", "架构问题整改率",
                        BigDecimal.valueOf(score), BigDecimal.valueOf(score)));
            });

            // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
            if (!scores.isEmpty()) {

                digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprintInfo.getStartTime(), "yyyy"),
                        sprintName, "JGWTZGL");

                // 批量插入新的计算结果
                digitalIndicatorScoreDao.batchInsertDataList(scores);

                log.info("=== 保存架构问题整改率数据 === 迭代: {}, 记录数: {}", sprintName, scores.size());
            }
        }
    }

    /**
     * 架构师 指标1计算公式 - 研发架构整改率
     * 公式： 研发架构整改率 = 100 - 单元分*(截止当月的该研发负责的逾期架构整改数)
     * 单元分 = 100/该研发负责所有整改数(未完成的)
     *
     * @param issues 问题
     * @param endDate 结束日期
     * @return double
     */
    private double calculateArchitectIssueScore(List<PlatformIssuePO> issues, Date endDate) {
        if (CollectionUtils.isEmpty(issues)) {
            return 100.0;
        }
        if (endDate == null) {
            return 100D;
        }
        // 统计未完成和逾期的问题数
        int pendingIssues = 0;
        int overdueIssues = 0;

        for (PlatformIssuePO issue : issues) {
            if (issue.getIssueStatus() != RectificationStatusEnum.COMPLETE.getCode()) {
                pendingIssues++;
                // 检查是否逾期
                if (issue.getRectificationPlanDate() != null &&
                        issue.getRectificationPlanDate().before(endDate)) {
                    overdueIssues++;
                }
            }
        }

        if (pendingIssues > 0) {
            // 计算单元分
            double unitScore = 100.0 / pendingIssues;
            // 计算最终得分
            double finalScore = 100.0 - (unitScore * overdueIssues);
            // 确保分数在0-100之间
            finalScore = Math.max(0, Math.min(100, finalScore));
            // 保留2位小数
            return Double.parseDouble(new DecimalFormat("0.00").format(finalScore));
        }

        return 100D;
    }

    /**
     * 架构师 指标2计算公式 - 计算安全问题整改率
     * 公式：安全问题整改率 = 1- (高风险潜在超期数 * 0.60 + 中风险潜在超期数 * 0.30 + 低风险潜在超期数 * 0.10) / (高风险总数 * 0.60 + 中风险总数 * 0.30 + 低风险总数 * 0.10)
     *
     * @param issues 安全问题列表
     * @return 整改率得分
     */
    private double calculateSafeIssueScore(List<SafeRiskPO> issues) {
        if (CollectionUtils.isEmpty(issues)) {
            return 100D;
        }

        double totalWeight = 0;
        double overdueWeight = 0;

        for (SafeRiskPO issue : issues) {
            // 获取风险级别
            SafeRiskLevelEnum riskLevel = SafeRiskLevelEnum.getByCode(issue.getRiskValue());
            double weight = getWeightByRiskLevel(riskLevel);
            // 忽略无效权重
            if (weight <= 0) {
                continue;
            }
            totalWeight += weight;

            // 判断是否潜在超期
            boolean isPotentiallyOverdue = false;
            if (issue.getAdjustPlanExpectDate() != null) {
                LocalDateTime oneWeekBefore = issue.getAdjustPlanExpectDate().minusWeeks(1);
                isPotentiallyOverdue = LocalDateTime.now().isAfter(oneWeekBefore) &&
                        !SafeRiskStatusEnum.CLOSE.getCode().equals(issue.getStatus());
            }

            if (isPotentiallyOverdue) {
                overdueWeight += weight;
            }
        }

        if (totalWeight > 0) {
            // 计算整改率 = 1 - (潜在超期权重/总权重)
            return 100 - DecimalUtils.getDoubleRate(overdueWeight, totalWeight);
        }
        return 100D;
    }

    /**
     * 计算 前后端 指标数据
     *
     * @param sprint   迭代
     * @param userList 用户数据
     */
    void calcFrontAndBackBySprint(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 前后端 交付质量数据 ===", sprint.getSprintName());
        // 筛选 前后端 人员
        List<SysTeamUser> frontAndBackList = userList.stream()
                .filter(s -> Objects.equals(s.getSubdividedRole(), SubRoleEnum.FRONT_END.getDesc())
                        || Objects.equals(s.getSubdividedRole(), SubRoleEnum.BACK_END.getDesc()))
                .toList();

        if (CollectionUtil.isNotEmpty(frontAndBackList)) {

            // 计算 指标1 代码评价打分
            codeReviewScore(sprint, frontAndBackList);

            // 计算 指标2 冒烟测试通过率
            smokeTestPassRate(sprint, frontAndBackList);

            // 计算 指标3 开发工时bug率
            workHourBugScore(sprint, frontAndBackList);
        }

        log.info("=== 计算 {} 前后端 交付质量数据 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-前后端-代码评价打分
     * a. 公式：代码交付类的业务单元平均得分
     * b. 详细逻辑：SUM（研发代码交付类评分）/SUM（研发代码交付类任务数量）
     */
    private void codeReviewScore(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-前后端-代码评价打分 ===", sprint.getSprintName());
        // 代码类交付物Id
        List<Integer> deliverableIdList = List.of(2, 22, 23, 49, 55, 56);
        // 人员列表
        List<String> userCodes = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        SelectTaskParam param = new SelectTaskParam();
        param.setSprintName(sprint.getSprintName());
        param.setUserCodeList(userCodes);
        // 获取待计算任务数据
        List<TaskScoreResultDTO> taskListTemp = evaluationMapper.queryTaskListBySprint(param);

        // 计算代码评价打分
        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {

            // 当前人员 代码类任务 且 存在评分
            List<TaskScoreResultDTO> codeTaskList = taskListTemp.stream()
                    .filter(s -> Objects.equals(s.getUserCode(), user.getLoginAccount()))
                    .filter(s -> deliverableIdList.contains(s.getDeliverableId()))
                    .filter(s -> s.getAiScore() != null)
                    .toList();

            double value = 0;
            if (CollectionUtil.isNotEmpty(codeTaskList)) {
                double aiScore = codeTaskList.stream().mapToDouble(TaskScoreResultDTO::getAiScore).sum();
                log.info("=== 【前后端】代码评价打分 === userCode={}, aiScore={}, codeTaskList.size()={}", user.getLoginAccount(), aiScore, codeTaskList.size());
                value = DecimalUtils.getDoubleResult(aiScore / codeTaskList.size());
            }

            // 写入数据
            dataList.add(setDigitalIndicatorScore(user, sprint, "DMPJDF", "代码评价打分",
                    BigDecimal.valueOf(value), BigDecimal.valueOf(value)));

        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {

            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"),
                    sprint.getSprintName(), "DMPJDF");

            // 批量插入新的计算结果
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }
        log.info("=== 计算 {} 交付质量-前后端-代码评价打分 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-前后端-冒烟测试通过率
     * 公式：（冒烟p0bug个数）通过用例数/冒烟用例数
     */
    private void smokeTestPassRate(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-前后端-冒烟测试通过率 ===", sprint.getSprintName());
        // 获取人员列表
        List<String> userCodes = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        // 查询冒烟测试数据
        SelectCaseParam param = new SelectCaseParam();
        param.setSprintName(sprint.getSprintName());
        param.setTesterList(userCodes);
        List<TbDySmokeTest> smokeList = smokeTestMapper.getSmokeBySprint(param);

        // 计算冒烟测试通过率
        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            List<TbDySmokeTest> userPlanList = smokeList.stream().filter(s -> Objects.equals(s.getTesterAccount(), user.getLoginAccount())).toList();
            List<TbDySmokeTest> passPlanList = userPlanList.stream().filter(s -> Objects.equals("通过", s.getExecuteStatus())).toList();
            log.info("=== 【前后端】交付质量-前后端-冒烟测试通过率 === userCode={}, userPlanList.size()={}, passPlanList.size()={}",
                    user.getLoginAccount(), userPlanList.size(), passPlanList.size());

            double value = CollectionUtil.isNotEmpty(userPlanList) ? DecimalUtils.getDoubleRate(passPlanList.size(), userPlanList.size()) : 0;

            // 写入数据
            dataList.add(setDigitalIndicatorScore(user, sprint, "PMYCSTGL", "P0冒烟测试通过率",
                    BigDecimal.valueOf(value), BigDecimal.valueOf(value)));

        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {

            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"),
                    sprint.getSprintName(), "PMYCSTGL");

            // 批量插入新的计算结果
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }
        log.info("=== 计算 {} 交付质量-前后端-冒烟测试通过率 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-前后端-开发工时bug率
     * 本迭代新增自动化用例个数
     */
    private void workHourBugScore(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-前后端-开发工时bug率 ===", sprint.getSprintName());
        // 获取人员列表
        List<String> userCode = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        SelectTaskParam param = new SelectTaskParam();
        param.setSprintName(sprint.getSprintName());
        param.setUserCodeList(userCode);
        // 查询人员任务工时
        List<TaskScoreResultDTO> taskList = evaluationMapper.queryTaskListBySprint(param);
        // 查询人员缺陷数据
        param.setStartTime(DateUtils.formatDate(sprint.getStartTime(), FORMAT_DATE_TIME));
        param.setEndTime(DateUtils.formatDate(sprint.getEndTime(), FORMAT_DATE_TIME));
        List<TaskScoreResultDTO> defectList = evaluationMapper.getDefectTaskList(param);

        // 计算 开发工时bug率
        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {

            Map<String, Long> levelCountMap = defectList.stream()
                    .filter(s -> Objects.equals(s.getUserCode(), user.getLoginAccount()))
                    .collect(Collectors.groupingBy(
                            s -> s.getSerioutLevel() == null ? "UNKNOWN" : s.getSerioutLevel(),
                            Collectors.counting()
                    ));

            int blockerNum = levelCountMap.getOrDefault(ScrumDefectSeriousLevelEnum.Blocker.getValue(), 0L).intValue();
            int majorNum = levelCountMap.getOrDefault(ScrumDefectSeriousLevelEnum.Major.getValue(), 0L).intValue();
            int normalNum = levelCountMap.getOrDefault(ScrumDefectSeriousLevelEnum.Normal.getValue(), 0L).intValue()
                    + levelCountMap.getOrDefault("UNKNOWN", 0L).intValue();

            // 代码编写 + 联调测试 工时
            List<Integer> deliverableIdList = List.of(2, 22, 49, 55);
            double totalConsumeHours = taskList.stream()
                    .filter(s -> Objects.equals(s.getUserCode(), user.getLoginAccount()))
                    .filter(s -> deliverableIdList.contains(s.getDeliverableId()))
                    .mapToDouble(TaskScoreResultDTO::getConsumeHours)
                    .sum();

            log.info("=== 【前后端】交付质量-前后端-开发工时bug率 === userCode={}, blockerNum={}, majorNum={}, normalNum={}, totalConsumeHours={}",
                    user.getLoginAccount(), blockerNum, majorNum, normalNum, totalConsumeHours);

            double value = totalConsumeHours > 0 ? DecimalUtils.getDoubleRate(blockerNum + majorNum * 0.5 + normalNum * 0.2, totalConsumeHours) : 0;

            // 写入数据
            dataList.add(setDigitalIndicatorScore(user, sprint, "KFGSBL", "开发工时bug率",
                    BigDecimal.valueOf(value), BigDecimal.valueOf(calcWorkBugScore(value / 100))));

        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {

            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"),
                    sprint.getSprintName(), "KFGSBL");

            // 批量插入新的计算结果
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 交付质量-前后端-开发工时bug率 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 测试 指标数据
     *
     * @param sprint   迭代
     * @param userList 用户数据
     */
    void calcTesterBySprint(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 测试 交付质量数据 ===", sprint.getSprintName());
        // 筛选 测试 人员
        List<SysTeamUser> testerList = userList.stream().filter(s -> Objects.equals(s.getSubdividedRole(), SubRoleEnum.TESTER.getDesc())).toList();

        if (CollectionUtil.isNotEmpty(testerList)) {

            // 计算 指标1 自动化接口个数-迭代新增
            autoInterfaceNum(sprint, testerList);

            // 计算 指标2 生产问题缺陷率
            defectRate(sprint, testerList);

            // 计算 指标3 缺陷逃逸率
            defectEscapeRate(sprint, testerList);

        }

        log.info("=== 计算 {} 测试 交付质量数据 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-测试-自动化接口个数
     * 本迭代新增自动化用例个数
     */
    private void autoInterfaceNum(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-测试-自动化接口个数 === 迭代：", sprint.getSprintName());
        // 获取人员列表
        List<String> userCodeList = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        SelectCaseParam param = new SelectCaseParam();
        param.setStartTime(DateUtils.formatDate(sprint.getStartTime(), FORMAT_DATE_TIME));
        param.setEndTime(DateUtils.formatDate(sprint.getEndTime(), FORMAT_DATE_TIME));
        param.setCreatorList(userCodeList);
        List<CaseDTO> userCaseList = autoCaseMapper.getUserTestCaseNum(param);
        Map<String, Integer> userCaseMap = userCaseList.stream()
                .collect(Collectors.toMap(CaseDTO::getUserCode, CaseDTO::getCaseCount));

        // 保存数据
        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            Integer caseCount = userCaseMap.getOrDefault(user.getLoginAccount(), 0);
            log.info("=== 【测试】交付质量-测试-自动化接口个数 === userCode={}, caseCount={}", user.getLoginAccount(), caseCount);

            dataList.add(setDigitalIndicatorScore(user, sprint, "ZDHJKGS", "自动化接口个数-迭代新增",
                    BigDecimal.valueOf(caseCount), BigDecimal.valueOf(calcAutoScore(caseCount))));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {

            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"),
                    sprint.getSprintName(), "ZDHJKGS");

            // 批量插入新的计算结果
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 交付质量-测试-自动化接口个数 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-测试-生产问题缺陷率
     * ■ 计算周期：每个月、迭代
     * ■ 计算维度：按团队统计，团队内每名测试得分都是这个值
     * ■ 计算指标：(该团队的运维问题 /该团队开发的代码行数) * 1000
     */
    private void defectRate(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-测试-自动化接口个数 === 迭代：", sprint.getSprintName());
        String startTime = DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss");

        // 查询全部用户
        List<SysTeamUser> allUserList = sysTeamUserMapper.getUniqueUser(QueryUserDTO.builder().build());

        // 待计算的团队
        List<String> subTeamList = userList.stream().map(SysTeamUser::getHomeDevTeam).distinct().toList();

        Map<String, Double> teamScoreMap = new HashMap<>();
        for (String subTeam : subTeamList) {
            // 团队下人员
            List<String> teamUserList = allUserList.stream()
                    .filter(s -> Objects.equals(s.getHomeDevTeam(), subTeam))
                    .map(SysTeamUser::getLoginAccount).toList();
            List<UserSubmitCode> userSubmitCodes = evaluationReportService.getUserSubmitCode(UserCodeRequest.builder()
                    .accountList(teamUserList).startTime(startTime).endTime(endTime).build());
            long codeLine = userSubmitCodes.stream().mapToLong(UserSubmitCode::getCodeLines).sum();

            // 团队下运维问题
            OperationSupportInfoDTO dto = new OperationSupportInfoDTO();
            dto.setTeamName(subTeam);
            dto.setStartTime(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            dto.setEndTime(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            dto.setBugFlag(1);
            List<OperationSupportInfo> supportInfoList = operationSupportInfoMapper.getOperationSupportInfoList(dto);

            if (codeLine > 0) {
                double data = (double) (supportInfoList.size() * 1000L) / codeLine;
                teamScoreMap.put(subTeam, DecimalUtils.getDoubleResult(data));
            }
        }

        // 给测试人员赋值
        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            double value = teamScoreMap.getOrDefault(user.getHomeDevTeam(), 0D);
            dataList.add(setDigitalIndicatorScore(user, sprint, "SCWTQXL", "生产问题缺陷率",
                    BigDecimal.valueOf(value), BigDecimal.valueOf(DecimalUtils.getScore(100-value*10))));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "SCWTQXL");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 交付质量-测试-生产问题缺陷率 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-测试-缺陷逃逸率
     * ■ 计算周期：每个月、迭代
     * ■ 计算维度：每名测试
     * ■ 计算指标：(该测试的运维问题 /(该测试的运维问题 +该测试为作者的缺陷总数 )) * 100%
     */
    private void defectEscapeRate(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-测试-缺陷逃逸率 ===", sprint.getSprintName());
        List<String> userCodeList = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        // 查询测试负责的流程信息
        List<ProductFlow> flowList = productFlowMapper.getFLowByTester(userCodeList);

        // 查询当前月份全部运维问题
        OperationSupportInfoDTO dto = new OperationSupportInfoDTO();
        dto.setStartTime(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
        dto.setEndTime(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        dto.setBugFlag(1);
        List<OperationSupportInfo> opsList = operationSupportInfoMapper.getOperationSupportInfoList(dto);

        // 查询该测试为作者的缺陷数
        PMIndicatorRequest param = new PMIndicatorRequest();
        param.setStartDate(DateUtils.formatDate(sprint.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setEndDate(DateUtils.formatDate(sprint.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
        param.setUserCodeList(userCodeList);
        List<TbDyProject> defectProjectList = tbDyProjectMapper.getDefectProjectList(param);

        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            // 测试负责流程
            List<String> userFlow = flowList.stream()
                    .filter(s -> Objects.equals(s.getTesterCode(), user.getLoginAccount()))
                    .map(ProductFlow::getFlowId).distinct().toList();
            // 测试运维问题
            long userOps = opsList.stream()
                    .filter(s -> userFlow.contains(s.getL3ProcessCode()) || userFlow.contains(s.getL4ProcessCode())).count();
            // 测试缺陷
            long userDefects = defectProjectList.stream().filter(s -> Objects.equals(s.getCreator(), user.getLoginAccount())).count();

            double value = userOps + userDefects > 0 ? DecimalUtils.getDoubleRate(userOps, userOps + userDefects) : 0D;
            dataList.add(setDigitalIndicatorScore(user, sprint, "QXTYL", "缺陷逃逸率",
                    BigDecimal.valueOf(value), BigDecimal.valueOf(calcDefectScore(value))));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {
            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"), sprint.getSprintName(), "QXTYL");
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }
        log.info("=== 计算 {} 交付质量-测试-缺陷逃逸率 完成 ===", sprint.getSprintName());
    }

    /**
     * 计算 交付质量-测试-测试用例评审通过率
     */
    private void testCasePassRate(SprintInfoPO sprint, List<SysTeamUser> userList) {
        log.info("=== 计算 {} 交付质量-测试-测试用例评审通过率 ===", sprint.getSprintName());
        List<String> userCodeList = userList.stream().map(SysTeamUser::getLoginAccount).toList();

        // 查询用例评审
        SelectCaseParam param = new SelectCaseParam();
        param.setSprintName(sprint.getSprintName());
        param.setTestType("reviewplan");
        param.setCreatorList(userCodeList);
        List<TbDyTestCase> testCaseList = testCaseMapper.selectCaseBySprint(param);

        // 计算 测试用例评审通过率
        List<DigitalIndicatorScore> dataList = new ArrayList<>();
        for (SysTeamUser user : userList) {
            List<TbDyTestCase> userPlanList = testCaseList.stream().filter(s -> Objects.equals(s.getCreator(), user.getLoginAccount())).toList();
            List<TbDyTestCase> passPlanList = userPlanList.stream().filter(s -> Objects.equals("通过", s.getExecuteStatus())).toList();
            log.info("=== 【测试】测试用例评审通过率 === userCode={}, userPlanList.size()={}, passPlanList.size()={}", user.getLoginAccount(), userPlanList.size(), passPlanList.size());

            double value = CollectionUtil.isNotEmpty(userPlanList) ? DecimalUtils.getDoubleRate(passPlanList.size(), userPlanList.size()) : 0;

            dataList.add(setDigitalIndicatorScore(user, sprint, "CSYLPSTGL", "测试用例评审通过率",
                    BigDecimal.valueOf(value), BigDecimal.valueOf(value)));
        }

        // 批量保存指标数据 - 先删除已有数据再插入新的计算结果
        if (!dataList.isEmpty()) {

            digitalIndicatorScoreDao.deleteByIteration(DateUtils.formatDate(sprint.getStartTime(), "yyyy"),
                    sprint.getSprintName(), "CSYLPSTGL");

            // 批量插入新的计算结果
            digitalIndicatorScoreDao.batchInsertDataList(dataList);
        }

        log.info("=== 计算 {} 员工效能-运营结果-测试-测试用例评审通过率 完成 ===", sprint.getSprintName());
    }

    DigitalIndicatorScore setDigitalIndicatorScore(SysTeamUser user, SprintInfoPO sprint,
                                                   String indicatorCode, String indicatorName, BigDecimal actualValue, BigDecimal score) {
        DigitalIndicatorScore scoreData = new DigitalIndicatorScore();
        scoreData.setYear(DateUtils.formatDate(sprint.getStartTime(), "yyyy"));
        scoreData.setIterationCode(sprint.getSprintName());
        scoreData.setUserCode(user.getLoginAccount());
        scoreData.setUserName(user.getUserName());
        scoreData.setRoleCode(String.valueOf(user.getRoleId()));
        scoreData.setRoleName(user.getRoleName());
        scoreData.setSubRoleName(user.getSubdividedRole());
        scoreData.setIndicatorCode(indicatorCode);
        scoreData.setIndicatorName(indicatorName);
        scoreData.setActualValue(actualValue);
        scoreData.setScore(score);
        return scoreData;
    }
}
