package com.faw.sa0214.tak.model.dto.baseCenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "基础处用户中心返回结果类")
public class BCResult<T> {

    @Schema(description = "返回结果类型")
    private String success;
    @Schema(description = "状态码")
    private String code;
    @Schema(description = "提示信息")
    private String message;
    @Schema(description = "返回值")
    private BCPage<T> data;
}
