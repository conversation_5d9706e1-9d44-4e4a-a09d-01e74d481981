<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--namespace根据自己需要创建的mapper的路径和名称填写-->
<mapper namespace="com.faw.sa0214.tak.mapper.PersonalMapper">
    <resultMap id="BaseMap" type="com.faw.sa0214.tak.po.SysTeamUser">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="loginAccount" column="login_account" jdbcType="VARCHAR"/>
        <result property="team" column="team" jdbcType="VARCHAR"/>
        <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
        <result property="roleId" column="role_id" jdbcType="INTEGER"/>
        <result property="teamId" column="team_id" jdbcType="INTEGER"/>
        <result property="position" column="position" jdbcType="VARCHAR"/>
        <result property="subdividedRole" column="subdivided_role" jdbcType="VARCHAR"/>
        <result property="minorRoleName" column="minor_role_name" jdbcType="VARCHAR"/>
        <result property="minorSubdividedRole" column="minor_subdivided_role" jdbcType="VARCHAR"/>
        <result property="minorRoleId" column="minor_role_id" jdbcType="VARCHAR"/>
        <result property="homeDevTeam" column="home_dev_team" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="VARCHAR"/>
        <result property="company" column="company" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="recordableDays" column="recordable_days" jdbcType="INTEGER"/>
        <result property="level" column="level" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="userType" column="user_type" jdbcType="INTEGER"/>
        <result property="entryTime" column="entry_time" jdbcType="VARCHAR"/>
        <result property="outTime" column="out_time" jdbcType="VARCHAR"/>
        <result property="settlementStartTime" column="settlement_start_time" jdbcType="VARCHAR"/>
        <result property="settlementEndTime" column="settlement_end_time" jdbcType="VARCHAR"/>
        <result property="productChain" column="product_chain" jdbcType="VARCHAR"/>
        <result property="productLine" column="product_line" jdbcType="VARCHAR"/>
        <result property="taskBillCode" column="task_bill_code" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="PersonalInfoMap" type="com.faw.sa0214.tak.model.dto.SysTeamUserDTO">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="loginAccount" column="login_account" jdbcType="VARCHAR"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="post" column="post" jdbcType="VARCHAR"/>
        <result property="rank" column="rank" jdbcType="VARCHAR"/>
        <result property="team" column="team" jdbcType="VARCHAR"/>
        <result property="teamId" column="team_id" jdbcType="VARCHAR"/>
        <result property="teamAdmin" column="team_admin" jdbcType="VARCHAR"/>
        <result property="applicationRole" column="role_name" jdbcType="VARCHAR"/>
        <result property="applicationRoleId" column="application_role_Id" jdbcType="VARCHAR"/>
        <result property="subdividedRole" column="subdivided_role" jdbcType="VARCHAR"/>
        <result property="homeDevTeam" column="home_dev_team" jdbcType="VARCHAR"/>
        <result property="lastUpdateTime" column="last_update_time" jdbcType="VARCHAR"/>
        <result property="orgRelation" column="org_relation" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="PersonalInfoMapTEMP" type="com.faw.sa0214.tak.model.dto.SysTeamUserDTO">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="loginAccount" column="login_account" jdbcType="VARCHAR"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="post" column="post" jdbcType="VARCHAR"/>
        <result property="rank" column="rank1" jdbcType="VARCHAR"/>
        <result property="team" column="team" jdbcType="VARCHAR"/>
        <result property="teamId" column="team_id" jdbcType="VARCHAR"/>
        <result property="teamAdmin" column="team_admin" jdbcType="VARCHAR"/>
        <result property="applicationRole" column="role_name" jdbcType="VARCHAR"/>
        <result property="applicationRoleId" column="application_role_Id" jdbcType="VARCHAR"/>
        <result property="subdividedRole" column="subdivided_role" jdbcType="VARCHAR"/>
        <result property="homeDevTeam" column="home_dev_team" jdbcType="VARCHAR"/>
        <result property="lastUpdateTime" column="last_update_time" jdbcType="VARCHAR"/>
        <result property="orgRelation" column="org_relation" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="VARCHAR"/>
        <result property="minorRoleName" column="minor_role_name" jdbcType="VARCHAR"/>
        <result property="minorRoleId" column="minor_role_id" jdbcType="VARCHAR"/>
        <result property="minorSubdividedRole" column="minor_subdivided_role" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="base">
        id, user_name, login_account, user_id, team, role_name, subdivided_role, `position`, home_dev_team, company,
        recordable_days, create_time, `level`, is_delete, team_id, role_id, user_type, entry_time, out_time,
        minor_role_name, minor_role_id, minor_subdivided_role, product_chain, product_line, task_bill_code,
        settlement_start_time, settlement_end_time
    </sql>

    <insert id="insertSysTeamUser">
        INSERT INTO msa_tak.sys_team_user(
        user_name,
        login_account,
        user_id,
        recordable_days,
        create_time,
        is_delete,
        `level`
        ) values
        <foreach collection="RequestList" item="Request" separator=",">
            (#{Request.userName}, #{Request.loginAccount}, #{Request.userId},1,#{Request.createTime},0,#{Request.rank})
        </foreach>
    </insert>

    <insert id="insertChangeHistory">
        INSERT INTO msa_tak.t_user_change_histroy(user_id,
                                                  created_by,
                                                  created_Time,
                                                  is_delete,
                                                  before_Approver,
                                                  before_Approver_Name)
        values (#{Request.userId}, #{loginId}, #{Request.createTime}, 0, #{approveUserCode}, #{approveUserName})
    </insert>

    <update id="updateTeamRole">
        update msa_tak.sys_team_user
        set team = #{Request.team},
            team_id=#{Request.teamId},
            role_Id=#{Request.applicationRoleId},
            role_name = #{Request.applicationRole},
            subdivided_role = #{Request.subdividedRole},
            home_dev_team = #{Request.homeDevTeam},
            minor_role_id=#{Request.minorRoleId},
            minor_role_name = #{Request.minorRoleName},
            minor_subdivided_role = #{Request.minorSubdividedRole}
        where login_account = #{Request.loginAccount}
    </update>

    <select id="findPersonalInfo" resultMap="PersonalInfoMap" resultType="com.faw.sa0214.tak.model.dto.SysTeamUserDTO">
        SELECT
        usera.id AS id,
        usera.user_id AS user_id,
        usera.user_name AS user_name,
        usera.login_account AS login_account,
        usera.team AS team,
        usera.team_id AS team_Id,
        usera.role_name AS role_name,
        usera.role_Id AS application_role_Id,
        usera.subdivided_role AS subdivided_role,
        usera.home_dev_team AS home_dev_team,
        usera.product_chain AS productChain,
        usera.is_delete AS is_delete
        FROM
        msa_tak.sys_team_user usera
        <where>
            <if test="Request.team!=null and Request.team!=''">
                AND usera.team = #{Request.team}
            </if>
            <if test="Request.teamId!=null and Request.teamId!=''">
                AND usera.team_id = #{Request.teamId}
            </if>
            <if test="Request.applicationRole!=null and Request.applicationRole!=''">
                AND usera.role_name = #{Request.applicationRole}
            </if>
            <if test="Request.applicationRoleId!=null and Request.applicationRoleId!=''">
                AND usera.role_Id = #{Request.applicationRoleId}
            </if>
            <if test="Request.subdividedRole!=null and Request.subdividedRole!=''">
                AND usera.subdivided_role = #{Request.subdividedRole}
            </if>
            <if test="Request.isDelete!=null and Request.isDelete!=''">
                AND usera.is_delete = #{Request.isDelete}
            </if>
            <if test="Request.userId!=null and Request.userId!=''">
                AND usera.user_id = #{Request.userId}
            </if>
            <if test="Request.loginAccount!=null and Request.loginAccount!=''">
                AND usera.login_account = #{Request.loginAccount}
            </if>
        </where>
        order by id desc
    </select>

    <select id="findTeamList" resultType="java.lang.String">
        SELECT team_Name
        FROM t_team_info
        WHERE team_Name IS NOT NULL and is_delete = 0
        GROUP BY team_Name
    </select>

    <select id="findTeamIdList" resultType="java.lang.String">
        SELECT team_id
        FROM msa_tak.t_team_info
        WHERE team_id IS NOT NULL and is_delete = 0
        GROUP BY team_id
    </select>

    <select id="findRoleList" resultType="java.lang.String">
        SELECT role_name
        FROM t_role_info
        WHERE role_name IS NOT NULL and is_delete = 0
        GROUP BY role_name
    </select>

    <select id="findChildRoleNameByRole" resultType="java.lang.String">
        SELECT child_role_name
        FROM msa_tak.t_role_info
        WHERE role_name = #{role} and is_delete = 0
    </select>

    <select id="findHomeDevTeamByTeam" resultType="java.lang.String">
        SELECT team_second_info
        FROM msa_tak.t_team_info
        WHERE team_Name = #{team}
    </select>

    <select id="findTeamManagerByTeam" resultType="java.lang.String">
        SELECT distinct team_magager
        FROM msa_tak.t_team_info
        WHERE team_id = #{teamId}
    </select>

    <select id="findTeamManagerNameByTeam" resultType="java.lang.String">
        SELECT distinct team_magager_name
        FROM msa_tak.t_team_info
        WHERE team_id = #{teamId}
    </select>

    <select id="findHistoryIdByUserId" resultType="java.lang.String">
        SELECT id
        FROM msa_tak.t_user_change_histroy
        WHERE user_id = #{userId}
        AND is_delete is not null
        AND (before_is_Agree IS NULL OR before_is_Agree &lt;&gt; 0)
    </select>

    <select id="findCurrentTime" resultType="java.lang.String">
        SELECT updated_Time
        FROM msa_tak.t_user_change_histroy
        WHERE user_id = #{userId}
          and is_completed = 'true'
        order by updated_Time Desc limit 1
    </select>

    <select id="findPersonalByLoginAccount" resultMap="PersonalInfoMapTEMP" resultType="com.faw.sa0214.tak.model.dto.SysTeamUserDTO">
        SELECT usera.id AS id, usera.user_id AS user_id, usera.user_name AS user_name, usera.login_account AS login_account,
            usera.user_type as userType, usera.team AS team, usera.team_Id AS team_Id, usera.role_name AS role_name,
            usera.role_Id AS application_role_Id, usera.subdivided_role AS subdivided_role, usera.home_dev_team AS home_dev_team,
            usera.is_delete AS is_delete, usera.company AS department, usera.position AS post, usera.`level` AS rank1,
            usera.minor_role_name AS minor_role_name, usera.minor_role_id AS minor_role_id,
            usera.minor_subdivided_role AS minor_subdivided_role, usera.product_chain AS productChain,
            usera.product_line AS productLine, team.team_magager AS team_admin
        FROM msa_tak.sys_team_user usera
        LEFT JOIN msa_tak.t_team_info team ON usera.team_id = team.team_id
        WHERE usera.login_account = #{Request.loginAccount} and usera.is_delete = 0 limit 1
    </select>

    <select id="selectUserInfoByUserId" parameterType="string" resultMap="PersonalInfoMap">
        select * from msa_tak.sys_team_user
        <where>
            <if test="userId !=null and userId !=null">
                user_id=#{userId}
            </if>
        </where>
    </select>

    <update id="deletePersonUserId">
        update msa_tak.sys_team_user set is_delete = 1 where user_id = #{userId}
    </update>

    <update id="deletePersonHistoryByUserId">
        update msa_tak.t_user_change_histroy
        set is_delete = 1,
        <if test="currentTime!=null and currentTime!=''">
            updated_Time = #{currentTime}
        </if>
        <where>
            <if test="userId!=null and userId!=''">
                and user_id = #{userId}
            </if>
        </where>
    </update>
    <select id="checkPersonRole" parameterType="integer" resultType="int">
        select count(1) from msa_tak.sys_team_user
        <where>
            <if test="roleId !=null and roleId !=null">
                role_Id=#{roleId}
            </if>
        </where>
    </select>
    <select id="checkPersonTeam" parameterType="integer" resultType="int">
        select count(1) from msa_tak.sys_team_user
        <where>
            <if test="teamId !=null and teamId !=null">
                team_id=#{teamId}
            </if>
        </where>
    </select>

    <select id="findTeamIdByLoginAccount" resultType="com.faw.sa0214.tak.model.dto.SysTeamUserDTO">
        select team_id,team from msa_tak.sys_team_user
        where is_delete = 0
        <if test="loginAccount!=null and loginAccount!=''">
            and login_account = #{loginAccount}
        </if>
    </select>

    <select id="findBusMainLineIdByTeam" resultType="java.lang.String">
        select main_line_code from msa_tak.t_team_info
        where is_delete = 0
        <if test="teamId!=null and teamId!=''">
            and team_id = #{teamId}
        </if>
    </select>

    <select id="findTeamAll" resultType="com.faw.sa0214.tak.model.request.EaMapRequest">
        select team_Name as invoker,main_line_code as busMainlineId from msa_tak.t_team_info where is_delete = 0
    </select>

    <select id="findProcessCodeListByInvoker" resultType="java.lang.String">
        select flow_id from msa_tak.ea_map_flow where invoker = #{invoker}
    </select>

    <select id="findPersonalByTeamId" resultMap="PersonalInfoMap">
        select * from msa_tak.sys_team_user where team_id = #{teamId} and is_delete = 0
    </select>

    <select id="getUserList" resultMap="BaseMap">
        select
            <include refid="base"/>
        from sys_team_user
        <if test="loginAccountList != null and loginAccountList.size()>0">
            WHERE login_account in
            <foreach collection="loginAccountList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="insertBatch">
        <foreach collection="sysTeamUserList" item="item" separator=";">
            INSERT INTO sys_team_user
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.loginAccount != null and item.loginAccount != ''">
                    login_account,
                </if>
                <if test="item.userId != null and item.userId !=''">
                    user_id,
                </if>
                <if test="item.recordableDays != null">
                    recordable_days,
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time,
                </if>
                <if test="item.isDelete != null">
                    is_delete,
                </if>
                <if test="item.level != null and item.level != ''">
                    `level`,
                </if>
                <if test="item.userType != null">
                    user_type,
                </if>
                <if test="item.team != null and item.team !=''">
                    team,
                </if>
                <if test="item.roleName != null and item.roleName != ''">
                    role_name,
                </if>
                <if test="item.subdividedRole != null and item.subdividedRole != ''">
                    subdivided_role,
                </if>
                <if test="item.position != null and item.position != ''">
                    `position`,
                </if>
                <if test="item.homeDevTeam != null and item.homeDevTeam != '' ">
                    home_dev_team,
                </if>
                <if test="item.company != null and item.company != ''">
                    company,
                </if>
                <if test="item.teamId != null">
                    team_id,
                </if>
                <if test="item.roleId != null">
                    role_Id,
                </if>
                <if test="item.entryTime != null and item.entryTime != ''">
                    entry_time,
                </if>
                <if test="item.outTime != null and item.outTime != ''">
                    out_time,
                </if>
                <if test="item.userName != null and item.userName != ''">
                    user_name,
                </if>
                <if test="item.minorRoleName != null and item.minorRoleName != ''">
                    minor_role_name,
                </if>
                <if test="item.minorSubdividedRole != null and item.minorSubdividedRole != ''">
                    minor_subdivided_role,
                </if>
                <if test="item.minorRoleId != null">
                    minor_role_id,
                </if>
                <if test="item.userCreateTime != null and item.userCreateTime != ''">
                    user_create_time,
                </if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="item.loginAccount != null and item.loginAccount != ''">
                #{item.loginAccount},
            </if>
            <if test="item.userId != null and item.userId !=''">
                #{item.userId},
            </if>
            <if test="item.recordableDays != null">
                #{item.recordableDays},
            </if>
            <if test="item.createTime != null and item.createTime != ''">
                #{item.createTime},
            </if>
            <if test="item.isDelete != null">
                #{item.isDelete},
            </if>
            <if test="item.level != null and item.level != ''">
                #{item.level},
            </if>
            <if test="item.userType != null">
                #{item.userType},
            </if>
            <if test="item.team != null and item.team !=''">
                #{item.team},
            </if>
            <if test="item.roleName != null and item.roleName != ''">
                #{item.roleName},
            </if>
            <if test="item.subdividedRole != null and item.subdividedRole != ''">
                #{item.subdividedRole},
            </if>
            <if test="item.position != null and item.position != ''">
                #{item.position},
            </if>
            <if test="item.homeDevTeam != null and item.homeDevTeam != ''">
                #{item.homeDevTeam},
            </if>
            <if test="item.company != null and item.company != ''">
                #{item.company},
            </if>
            <if test="item.teamId != null">
                #{item.teamId},
            </if>
            <if test="item.roleId != null">
                #{item.roleId},
            </if>
            <if test="item.entryTime != null and item.entryTime != ''">
                #{item.entryTime},
            </if>
            <if test="item.outTime != null and item.outTime != ''">
                #{item.outTime},
            </if>
            <if test="item.userName != null and item.userName != ''">
                #{item.userName},
            </if>
                <if test="item.minorRoleName != null and item.minorRoleName != ''">
                    #{item.minorRoleName},
                </if>
                <if test="item.minorSubdividedRole != null and item.minorSubdividedRole != ''">
                    #{item.minorSubdividedRole},
                </if>
                <if test="item.minorRoleId != null">
                    #{item.minorRoleId},
                </if>
                <if test="item.userCreateTime != null and item.userCreateTime != ''">
                    #{item.userCreateTime},
                </if>
            </trim>
        </foreach>
    </insert>
    <insert id="batchInsertThirdPartyUser">
        INSERT INTO sys_team_user (
            user_name,
            login_account,
            user_id,
            team,
            role_name,
            subdivided_role,
            position,
            home_dev_team,
            company,
            recordable_days,
            create_time,
            level,
            team_id,
            role_id,
            user_type,
            entry_time,
            out_time,
            minor_role_name,
            minor_subdivided_role,
            minor_role_id,
            user_create_time,
            product_chain,
            product_line,
            task_bill_code,
            settlement_start_time,
            settlement_end_time,
            is_delete
        )
        VALUES
        <foreach collection="insertList" item="item" separator=",">
            (
            #{item.userName},
            #{item.loginAccount},
            #{item.userId},
            #{item.team},
            #{item.roleName},
            #{item.subdividedRole},
            #{item.position},
            #{item.homeDevTeam},
            #{item.company},
            #{item.recordableDays},
            #{item.createTime},
            #{item.level},
            #{item.teamId},
            #{item.roleId},
            #{item.userType},
            #{item.entryTime},
            #{item.outTime},
            #{item.minorRoleName},
            #{item.minorSubdividedRole},
            #{item.minorRoleId},
            #{item.userCreateTime},
            #{item.productChain},
            #{item.productLine},
            #{item.taskBillCode},
            #{item.settlementStartTime},
            #{item.settlementEndTime},
            #{item.isDelete}
            )
        </foreach>
    </insert>

    <select id="selectByCondition" resultMap="BaseMap">
        select <include refid="base"/>
        from sys_team_user
        where 1 = 1
        and user_id is not null
        and user_id != ''
        <if test="loginAccount != null and loginAccount != ''">
           and login_account = #{loginAccount}
        </if>
        <if test="userId != null and userId !=''">
            and user_id = #{userId}
        </if>
        <if test="recordableDays != null">
            and recordable_days = #{recordableDays}
        </if>
        <if test="createTime != null and createTime != ''">
           and create_time = #{createTime}
        </if>
        <if test="isDelete != null">
           and is_delete = #{isDelete}
        </if>
        <if test="level != null and level != ''">
           and level = #{level}
        </if>
        <if test="userType != null">
           and user_type = #{userType}
        </if>
        <if test="team != null and team !=''">
           and team = #{team}
        </if>
        <if test="roleName != null and roleName != ''">
            and role_name = #{roleName}
        </if>
        <if test="subdividedRole != null and subdividedRole != ''">
            and subdivided_role = #{subdividedRole}
        </if>
        <if test="position != null and position != ''">
            and position = #{position}
        </if>
        <if test="homeDevTeam != null and homeDevTeam != ''">
            and home_dev_team = #{homeDevTeam}
        </if>
        <if test="company != null and company != ''">
            and company = #{company}
        </if>
        <if test="teamId != null">
            and team_id = #{teamId}
        </if>
        <if test="roleId != null">
            and role_Id = #{roleId}
        </if>
        <if test="userName != null and userName != ''">
            and (user_name like concat('%',#{userName},'%') or login_account like concat('%',#{userName},'%'))
        </if>
        <if test="minorRoleName != null and minorRoleName != ''">
            and minor_role_name = #{minorRoleName}
        </if>
        <if test="minorSubdividedRole != null and minorSubdividedRole != ''">
            and minor_subdivided_role = #{minorSubdividedRole}
        </if>
        <if test="minorRoleId != null">
            and minor_role_id = #{minorRoleId}
        </if>
        <if test="productChain != null and productChain != ''">
            and product_chain = #{productChain}
        </if>
        <if test="productLine != null and productLine != ''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="selectByConditionAll" resultMap="BaseMap">
        select <include refid="base"/>
        from sys_team_user
        where 1 = 1
        <if test="loginAccount != null and loginAccount != ''">
            and login_account = #{loginAccount}
        </if>
        <if test="userId != null and userId !=''">
            and user_id = #{userId}
        </if>
        <if test="recordableDays != null">
            and recordable_days = #{recordableDays}
        </if>
        <if test="createTime != null and createTime != ''">
            and create_time = #{createTime}
        </if>
        <if test="isDelete != null">
            and is_delete = #{isDelete}
        </if>
        <if test="level != null and level != ''">
            and level = #{level}
        </if>
        <if test="userType != null">
            and user_type = #{userType}
        </if>
        <if test="team != null and team !=''">
            and team = #{team}
        </if>
        <if test="roleName != null and roleName != ''">
            and role_name = #{roleName}
        </if>
        <if test="subdividedRole != null and subdividedRole != ''">
            and subdivided_role = #{subdividedRole}
        </if>
        <if test="position != null and position != ''">
            and position = #{position}
        </if>
        <if test="homeDevTeam != null and homeDevTeam != ''">
            and home_dev_team = #{homeDevTeam}
        </if>
        <if test="company != null and company != ''">
            and company = #{company}
        </if>
        <if test="teamId != null">
            and team_id = #{teamId}
        </if>
        <if test="roleId != null">
            and role_Id = #{roleId}
        </if>
        <if test="userName != null and userName != ''">
            and (user_name like concat('%',#{userName},'%') or login_account like concat('%',#{userName},'%'))
        </if>
        <if test="minorRoleName != null and minorRoleName != ''">
            and minor_role_name = #{minorRoleName}
        </if>
        <if test="minorSubdividedRole != null and minorSubdividedRole != ''">
            and minor_subdivided_role = #{minorSubdividedRole}
        </if>
        <if test="minorRoleId != null">
            and minor_role_id = #{minorRoleId}
        </if>
        <if test="productChain != null and productChain != ''">
            and product_chain = #{productChain}
        </if>
        <if test="productLine != null and productLine != ''">
            and product_line = #{productLine}
        </if>
    </select>

    <select id="selectByConditionForUserIdIsNull" resultMap="BaseMap">
        select <include refid="base"/>
        from sys_team_user
        where 1 = 1
            and (user_id is null or user_id = '')
        <if test="loginAccount != null and loginAccount != ''">
            and login_account = #{loginAccount}
        </if>
        <if test="recordableDays != null">
            and recordable_days = #{recordableDays}
        </if>
        <if test="createTime != null and createTime != ''">
            and create_time = #{createTime}
        </if>
        <if test="isDelete != null">
            and is_delete = #{isDelete}
        </if>
        <if test="level != null and level != ''">
            and level = #{level}
        </if>
        <if test="userType != null">
            and user_type = #{userType}
        </if>
        <if test="team != null and team !=''">
            and team = #{team}
        </if>
        <if test="roleName != null and roleName != ''">
            and role_name = #{roleName}
        </if>
        <if test="subdividedRole != null and subdividedRole != ''">
            and subdivided_role = #{subdividedRole}
        </if>
        <if test="position != null and position != ''">
            and position = #{position}
        </if>
        <if test="homeDevTeam != null and homeDevTeam != ''">
            and home_dev_team = #{homeDevTeam}
        </if>
        <if test="company != null and company != ''">
            and company = #{company}
        </if>
        <if test="teamId != null">
            and team_id = #{teamId}
        </if>
        <if test="roleId != null">
            and role_Id = #{roleId}
        </if>
        <if test="userName != null and userName != ''">
            and (user_name like concat('%',#{userName},'%') or login_account like concat('%',#{userName},'%'))
        </if>
        <if test="minorRoleName != null and minorRoleName != ''">
            and minor_role_name = #{minorRoleName}
        </if>
        <if test="minorSubdividedRole != null and minorSubdividedRole != ''">
            and minor_subdivided_role = #{minorSubdividedRole}
        </if>
        <if test="minorRoleId != null">
            and minor_role_id = #{minorRoleId}
        </if>
        <if test="productChain != null and productChain != ''">
            and product_chain = #{productChain}
        </if>
        <if test="productLine != null and productLine != ''">
            and product_line = #{productLine}
        </if>
    </select>
    <select id="findCompanyList" resultType="java.lang.String">
        SELECT DISTINCT
            company
        FROM
            sys_team_user
        WHERE
            is_delete = 0
          AND company IS NOT NULL
    </select>

    <update id="updateItPmStatus">
        update
            sys_team_user
        set is_delete = 1
        where CONCAT(task_bill_code, ':', login_account) in
            <foreach collection="users" separator="," item="item" index="index" open="(" close=")">
                CONCAT(#{item.taskBillCode}, ':', #{item.loginAccount})
            </foreach>
    </update>
    <update id="batchUpdateOutTime">
        <foreach collection="updateList" item="item" separator=";">
            UPDATE
                sys_team_user
            SET
                out_time = #{item.outTime},
                is_delete = #{item.isDelete},
                settlement_start_time = #{item.settlementStartTime},
                settlement_end_time = #{item.settlementEndTime}
            WHERE task_bill_code = #{item.taskBillCode} AND login_account = #{item.loginAccount}
        </foreach>
    </update>

    <select id="selectUserDistinct" resultType="java.lang.String">
        SELECT DISTINCT login_account
        FROM sys_team_user
    </select>

    <select id="getTeamNameByMainLineCode" resultType="java.lang.String">
        select team_Name from t_team_info where main_line_code = #{mainLineCode}
    </select>

    <select id="getPersonDetail" resultMap="PersonalInfoMap">
        select * from sys_team_user where is_delete = 0
        and login_account in
        <foreach collection="loginAccountList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="queryRole != null and queryRole != ''">
            and role_name = #{queryRole}
        </if>
    </select>
    <select id="findProcessFlowCodeList" resultType="java.lang.String">
        select flow_id from msa_tak.ea_map_flow
    </select>
    <select id="getRecordByLoginAccount" resultMap="PersonalInfoMapTEMP">
        select <include refid="base"/>
        from sys_team_user
        where login_account = #{loginAccount}
        order by id desc
        limit 1
    </select>
    <select id="selectEntryUsers" resultMap="BaseMap">
        select <include refid="base"/>
        from sys_team_user
        where user_type = 1
    </select>
    <select id="getUserByAccountAndRole" resultType="com.faw.sa0214.tak.po.SysTeamUser">
        select distinct
            login_account as loginAccount,
            user_name as userName
        from sys_team_user
        where login_account in
        <foreach collection="userCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="role != null and role != ''">
            and role_name = #{role}
        </if>
    </select>
    <select id="getUserListByTeam" resultType="com.faw.sa0214.tak.model.dto.SysTeamUserDTO">
        select
            <include refid="base"/>
        from
            sys_team_user
        where
            team = #{team}
    </select>

    <select id="getTeamUsersByTeamIdAndRole" resultType="com.faw.sa0214.tak.model.response.TeamUserResponse">
        SELECT 
            t1.user_name,
            t1.login_account 
        FROM 
            sys_team_user t1 
        INNER JOIN 
            t_team_info t2 
        ON 
            t1.team_id = t2.team_id 
        WHERE 
            t2.team_id = #{teamId}
            <if test="subdividedRole != null and subdividedRole != ''">
                AND t1.subdivided_role = #{subdividedRole}
            </if>
            AND t1.is_delete = 0
    </select>

    <select id="queryUserList" parameterType="com.faw.sa0214.tak.model.dto.UserDTO" resultType="com.faw.sa0214.tak.model.dto.UserDTO">
        SELECT distinct login_account as user_code, user_name as userName
        FROM sys_team_user
        <where>
            <if test="userName != null and userName != ''">
                and (user_name like concat('%',#{userName},'%') or login_account like concat('%',#{userName},'%'))
            </if>
        </where>
    </select>
    <select id="selectBySearchCondition" resultType="com.faw.sa0214.tak.po.SysTeamUser">
        select <include refid="base"/>
        from sys_team_user
        where 1 = 1
        <if test="userType != null">
            and user_type = #{userType}
        </if>
        <if test="team != null and team !=''">
            and team = #{team}
        </if>
        <if test="applicationRole != null and applicationRole.size() != 0">
            and role_name in
            <foreach collection="applicationRole" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="subdividedRole != null and subdividedRole.size() != 0">
            and subdivided_role in
            <foreach collection="subdividedRole" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="userName != null and userName != ''">
            and (user_name like concat('%',#{userName},'%') or login_account like concat('%',#{userName},'%'))
        </if>
        <if test="minorRoleName != null and minorRoleName.size() != 0">
            and minor_role_name in
            <foreach collection="minorRoleName" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="minorSubdividedRole != null and minorSubdividedRole.size() != 0">
            and minor_subdivided_role in
            <foreach collection="minorSubdividedRole" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="productChain != null and productChain != ''">
            and product_chain = #{productChain}
        </if>
        <if test="productLine != null and productLine != ''">
            and product_line = #{productLine}
        </if>
    </select>
    <select id="selectByTaskBill" resultType="com.faw.sa0214.tak.po.SysTeamUser">
        select
        <include refid="base"/>
        from sys_team_user
        <if test="taskBillCodeList != null and taskBillCodeList.size()>0">
            WHERE task_bill_code in
            <foreach collection="taskBillCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectByTaskBillUserCodes" resultType="com.faw.sa0214.tak.po.SysTeamUser">
        SELECT
        <include refid="base"/>
        FROM sys_team_user
        <if test="taskBillUserCodeList != null and taskBillUserCodeList.size() > 0">
            WHERE CONCAT(task_bill_code, ':', login_account) IN
            <foreach collection="taskBillUserCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
