package com.faw.sa0214.tak.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.user.UserCheck;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.QueryCondDTO;
import com.faw.sa0214.tak.model.dto.TaskWorkHoursDTO;
import com.faw.sa0214.tak.model.dto.TaskWorkHoursDetailInfo;
import com.faw.sa0214.tak.model.request.SearchRequest;
import com.faw.sa0214.tak.model.response.*;
import com.faw.sa0214.tak.po.TbHours;
import com.faw.sa0214.tak.service.TbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 图表数据接口
 *
 * <AUTHOR>
 * @version $ Id: bMidAttendanceController, v 0.1 2023/09/15 15:28 Lsj Exp $
 * @date 2023/09/15
 */
@Tag(name = "图表数据接口", description = "图表数据接口")
@RequestMapping("/tb")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TbController {

    private final TbService tbService;

    private static final Logger logger = LogManager.getLogger(TbController.class);

    /**
     * 获取团队角色列表
     *
     * @param searchRequest 请求条件
     * @return {@link Result}<{@link List}<{@link String}>>
     */
    @Operation(summary = "获取团队角色列表", description = "[author:********]")
    @PostMapping("getTeamRoleList")
    public Result<List<String>> getTeamRoleList(@Validated @RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.getTeamRoleList(searchRequest));
    }

    /**
     * TB任务小图数据
     *
     * @param searchRequest 请求条件
     * @return {@link Result}<{@link List}<{@link TbTaskResponse}>>
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "任务小图数据", description = "[author:********]")
    @PostMapping("getTbTaskChartData")
    public Result<TbTaskResponseMain> getTbTaskChartData(@Validated @RequestBody SearchRequest searchRequest) {

        return Result.success(tbService.getTaskChartData(searchRequest));
    }

    /**
     * 员工小图工时数据
     *
     * @param searchRequest 请求条件
     * @return {@link Result}<{@link List}<{@link TbTaskResponse}>>
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "员工小图工时数据", description = "[author:********]")
    @PostMapping("getUserChartData")
    public Result<UserChartResponse> getUserChartData(@Validated @RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.getUserChartData(searchRequest));
    }

    /**
     * 角色小图工时数据
     *
     * @param searchRequest 请求条件
     * @return {@link Result}<{@link List}<{@link TbTaskResponse}>>
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "角色小图工时数据", description = "[author:********]")
    @PostMapping("getRoleAverageDailyHoursChartData")
    public Result<RoleChartResponse> getRoleAverageDailyHoursData(@Validated @RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.getRoleAverageDailyHoursData(searchRequest));
    }

    /**
     * 角色工时大图数据
     *
     * @param searchRequest 请求条件
     * @return {@link Result}<{@link List}<{@link RoleHoursResponse}>>
     */
    @Operation(summary = "角色工时大图数据", description = "[author:********]")
    @PostMapping("/getRoleHoursChartData")
    public Result<List<RoleHoursResponse>> getRoleHoursChartData(
            @Validated @RequestBody SearchRequest searchRequest) {

        return Result.success(tbService.getRoleHoursChartData(searchRequest));
    }

    /**
     * 业务单元工时大图数据
     *
     * @param searchRequest 请求条件
     * @return {@link Result}<{@link Map}<{@link Integer}, {@link Map}<{@link String}, {@link List}<{@link TbHours}>>>>
     */
    @Operation(summary = "业务单元工时大图数据", description = "[author:********]")
    @PostMapping("/getBusinessUnitHours")
    public Result<Map<Integer, Map<String, List<TbHours>>>> getBusinessUnitHoursChartData(
            @Validated @RequestBody SearchRequest searchRequest) {

        return Result.success(tbService.getBusinessUnitHoursChartData(searchRequest));
    }

    /**
     * 详表数据
     *
     * @param searchRequest 请求条件
     * @return {@link Result}<{@link PageResult}<{@link DetailHoursResponse}>>
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "详表数据", description = "[author:********]")
    @PostMapping("/getDetailHours")
    public Result<List<DetailHoursResponse>> getDetailsTableData(
            @Validated @RequestBody SearchRequest searchRequest) {

        return Result.success(tbService.getDetailsTableData(searchRequest));
    }

    /**
     * 业务单元小图
     *
     * @param searchRequest 请求条件
     * @return {@code Result<BusinessUtilResponse> }
     * <AUTHOR>
     */
    @Operation(summary = "业务单元小图", description = "[author:********]")
    @PostMapping("getBusinessUtilList")
    public Result<BusinessUtilResponse> getBusinessUtilList(@Validated @RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.getBusinessUtilList(searchRequest));
    }

    /**
     * 业务单元小图-任务数最少
     *
     * @param searchRequest 请求条件
     * @return {@code Result<List<BusinessUtilResponse>> }
     * <AUTHOR>
     */
    @Operation(summary = "业务单元小图-任务数最少", description = "[author:********]")
    @PostMapping("getBusinessUtilDesc")
    public Result<List<BusinessUtilResponse>> getBusinessUtilDesc(@Validated @RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.getBusinessUtilListDesc(searchRequest));
    }


    /**
     * 获取员工列表
     *
     * @param dto 查询条件对象
     * @return {@code Result<List<SysTeamUserResponse>> }
     * <AUTHOR>
     */
    @Operation(summary = "获取员工列表", description = "[author:********]")
    @PostMapping("getUserList")
    public Result<List<SysTeamUserResponse>> getUserList(@RequestBody QueryCondDTO dto) {
        return Result.success(tbService.getUserList(dto));
    }


    /**
     * 获取角色列表
     *
     * @param dto 查询条件对象
     * @return {@code Result<List<SysTeamUserResponse>> }
     * <AUTHOR>
     */
    @Operation(summary = "获取角色列表", description = "[author:********]")
    @PostMapping("getRoleList")
    public Result<List<String>> getRoleList(@RequestBody QueryCondDTO dto) {
        return Result.success(tbService.getRoleList(dto));
    }

    /**
     * 获取大图员工工时列表
     *
     * @param searchRequest 查询条件对象
     * @return {@code Result<List<SysTeamUserResponse>> }
     * <AUTHOR>
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "获取大图员工工时列表", description = "[author:********]")
    @PostMapping("getUserHoursChartData")
    public Result<UserHoursChartResponse> getUserHoursChartData(@RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.getUserHoursChartData(searchRequest));
    }

    /**
     * 获取任务工时
     *
     * @param searchRequest 搜索请求
     * @return {@link Result}<{@link List}<{@link TaskWorkHoursDTO}>>
     */
    @UserCheck(checkField = "team", interfaceRole = "takAdmin")
    @Operation(summary = "获取任务工时", description = "[author:********]")
    @PostMapping("/findTaskWorkHours")
    public Result<List<TaskWorkHoursDTO>> findTaskWorkHours(@RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.findTaskWorkHours(searchRequest));
    }

    /**
     * 按生态域帐户查找可记录天数
     *
     * @param loginAccount 生态域帐户
     * @return {@link Result}<{@link Integer}>
     */
    @Operation(summary = "按生态域帐户查找可记录天数", description = "[author:********]")
    @GetMapping("findRecordableDaysByLoginAccount")
    public Result<Integer> findRecordableDaysByLoginAccount(@RequestParam("loginAccount") String loginAccount) {
        return Result.success(tbService.findRecordableDaysByLoginAccount(loginAccount));
    }

    /**
     * 导出excel文件
     *
     * @param searchRequest 搜索请求
     * @param response      响应
     * @return {@link Result}
     * @throws Exception 例外
     */
    @Operation(summary = "任务工时详细信息下载", description = "[author:********]")
    @PostMapping("/download")
    public void writeExcel(@RequestBody SearchRequest searchRequest, HttpServletResponse response) throws Exception {
        logger.info("download  入参[]" + searchRequest.toString());
        if (StringUtils.isEmpty(searchRequest.getYearMonths()) || StringUtils.isEmpty(searchRequest.getTeam())) {
            logger.info("参数不能为空");
        } else {
            // 方法开始处记录时间
            long startTime = System.currentTimeMillis();
            // 或者 Instant startTime = Instant.now();
            logger.info("download started at {}", new Date(startTime));

            response.setCharacterEncoding("UTF-8");
            //处理 HTTP 协议中，头部信息中的值必须是 ASCII 码字符  中文 转成 ASCII码
            String fileName = "task.xlsx";
            //content-disposition 指示如何处理响应内容,一般有两种方式：
            // inline ：直接在页面显示,   attchment ：以附件形式下载
            response.setHeader("Content-disposition", "attachment; filename=\"" + fileName + "\"");
            //获取所有数据
            List<TaskWorkHoursDetailInfo> list = tbService.findTaskWorkHoursDetailInfo(searchRequest);
            long endTimeSQL = System.currentTimeMillis();
            long sqlTime = endTimeSQL - startTime;
            logger.info("download sqlTime in {} ms", sqlTime);
            logger.info("赋值结束");
            try {
                EasyExcel.write(response.getOutputStream(), TaskWorkHoursDetailInfo.class)
                        .sheet("任务工时详细信息").doWrite(list);
            } catch (IOException e) {
                e.printStackTrace();
            }

            // 方法结束处记录时间并计算耗时
            long endTime = System.currentTimeMillis();
            logger.info("download end at {}", new Date(endTime));
            // 或者 Instant endTime = Instant.now();
            long elapsedTime = endTime - startTime;
            logger.info("download completed in {} ms", elapsedTime);
            logger.info("数据导出成功");
        }
    }

    @Operation(summary = "获取任务工时详细信息", description = "[author:********]")
    @PostMapping("/findTaskWorkHoursDetailInfo")
    public Result<List<TaskWorkHoursDetailInfo>> findTaskWorkHoursDetailInfo(@RequestBody SearchRequest searchRequest) {
        return Result.success(tbService.findTaskWorkHoursDetailInfo(searchRequest));
    }


    @Operation(summary = "任务工时详细信息导出", description = "[author:********]")
    @PostMapping("/export")
    public void export(@RequestBody SearchRequest request, HttpServletResponse res) {

        tbService.export(request, res);

    }
}
