package com.faw.sa0214.tak.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.sa0214.tak.client.dto.bpm.StartProcessDTO;
import com.faw.sa0214.tak.common.constant.BizException;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.common.constant.ProcessConstant;
import com.faw.sa0214.tak.common.constant.enums.TeamApproveTypeEnum;
import com.faw.sa0214.tak.common.util.ApproveProcessService;
import com.faw.sa0214.tak.mapper.PersonalMapper;
import com.faw.sa0214.tak.mapper.UserChanageHistoryMapper;
import com.faw.sa0214.tak.model.dto.BpmEndCallBackDTO;
import com.faw.sa0214.tak.model.dto.SysTeamUserDTO;
import com.faw.sa0214.tak.model.request.SysTeamUserRequest;
import com.faw.sa0214.tak.po.TeamInfo;
import com.faw.sa0214.tak.po.TeamRole;
import com.faw.sa0214.tak.po.UserChangeHistroy;
import com.faw.sa0214.tak.service.ProcessService;
import com.faw.sa0214.tak.service.TeamInfoService;
import com.faw.sa0214.tak.service.TeamRoleService;
import com.faw.sa0214.tak.service.UserChangeHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.faw.sa0214.tak.common.constant.enums.TeamApproveTypeEnum.APPROVE_ERROR;

/**
 * 业务名称: XXX
 *
 * @version v1.0.1
 * To change this template use File | Settings | Editor | Color Scheme | File and Code Templates.
 * @Copyright: Copyright (c) 2024$
 * @Author: thought1231
 * @Date: 2024/1/23
 * @Time: 15:46
 * @Project sa-0214_msa_tak_be
 */
@Service
@Slf4j
public class UserChangeHistoryServiceImpl extends ServiceImpl<UserChanageHistoryMapper, UserChangeHistroy> implements UserChangeHistoryService {
    @Autowired
    private UserChanageHistoryMapper userChanageHistoryMapper;
    @Autowired
    private PersonalMapper personalMapper;
    @Autowired
    private ApproveProcessService approveProcessService;
    @Autowired
    private TeamInfoService teamInfoService;
    @Autowired
    private TeamRoleService teamRoleService;
    @Autowired
    private ProcessService processService;

    public String listenerBPMCallBack(Object entity) {
        log.info("listenerBPMCallBack() 监听工作流回调 参数 entity={}", JSON.toJSONString(entity));
        String deleteReason = null;
        String processInstanceId = null;
        String eventName = null;
        String businessKey = null;
        try {
            JSONObject paramJson = JSONObject.parseObject(JSON.toJSONString(entity));
            log.error(paramJson.toJSONString());
            JSONObject interfaceParam = (JSONObject) paramJson.get("interfaceParam");
            JSONObject execution = (JSONObject) interfaceParam.get("execution");
            processInstanceId = (execution.get("processInstanceId") == null || execution.get("processInstanceId").equals("")) ? "" : execution.getString("processInstanceId");
            eventName = execution.getString("eventName");
            businessKey = execution.getString("businessKey");//业务主键号
            deleteReason = (execution.get("deleteReason") == null || execution.get("deleteReason").equals("")) ? "" : execution.getString("deleteReason");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return APPROVE_ERROR.getDesc();
        }
        if (!"end".equals(eventName)) {
            return APPROVE_ERROR.getDesc();
        }

        // 0.检查OA审批是否已处理
        if (!(businessKey != null && !businessKey.equals(""))) {
            return TeamApproveTypeEnum.APPROVE_ERROR.getDesc();
        }
        UserChangeHistroy userChangeHistroy = new UserChangeHistroy();
        userChangeHistroy.setId(Integer.parseInt(businessKey));
        List<UserChangeHistroy> userChangeHistroys = getUserChangeHistory(userChangeHistroy);
        if (userChangeHistroys != null && userChangeHistroys.size() > 0) {
            userChangeHistroy = userChangeHistroys.get(0);
        }
        // 审批驳回或撤回流程执行
        if (deleteReason.equals("REJECTTOSTART") || deleteReason.equals("withdraw")) {
            userChangeHistroy.setAfterApproverDesc(deleteReason.equals("REJECTTOSTART") ? "申请被驳回!!" : "申请被撤回!!");
            userChangeHistroy.setProcessInstanceId(processInstanceId);
            userChangeHistroy.setIsCompleted("true");
            if (StringUtils.isNotBlank(userChangeHistroy.getBeforeIsAgree())) {
                userChangeHistroy.setAfterIsAgree("0");
            } else {
                userChangeHistroy.setBeforeIsAgree("0");
            }
            try {
                this.updateById(userChangeHistroy);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        // 审核通过流程执行
        if (deleteReason.equals("") || "null".equalsIgnoreCase(deleteReason)) {
            if (!(userChangeHistroy.getIsDelete() != null && !userChangeHistroy.getIsDelete().equals("") && (userChangeHistroy.getIsDelete().equals("1") || userChangeHistroy.getIsDelete().equals("0")))) {
                String approveNodes = userChangeHistroy.getApproveNodes();
                if (approveNodes != null && !approveNodes.equals("")) {
                    JSONObject jsonObject = JSONObject.parseObject(approveNodes);
                    //判断第一步审批是否完成,如果第一步完成,则进入第二步
                    //   log.error("jsonObject"+jsonObject.toJSONString());
                    if (jsonObject != null && jsonObject.size() == 2) {
                        try {
                            if (jsonObject.get("first") != null && jsonObject.getString("first").equals("complete")) {
                                LocalDateTime currentDateTime = LocalDateTime.now();
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                String currentTime = currentDateTime.format(formatter);
                                jsonObject.put("second", "complete");
                                userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
                                userChangeHistroy.setAfterIsAgree("1");
                                userChangeHistroy.setIsCompleted("true");
                                userChangeHistroy.setProcessInstanceId(processInstanceId);
                                userChangeHistroy.setUpdatedTime(currentTime);
                                SysTeamUserRequest sysTeamUserRequest = buildSysTeamUserRequest(userChangeHistroy);
                                try {
                                    personalMapper.updateTeamRole(sysTeamUserRequest);
                                    //   log.error("processInstanceId"+processInstanceId+"getId"+userChangeHistroy.getId());
                                    this.updateById(userChangeHistroy);
                                } catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                }
                            } else {
                                userChangeHistroy.setBeforeIsAgree("1");
                                jsonObject.put("first", "complete");
                                userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
//                                approveProcessService.startWorkflowChangeUser(userChangeHistroy.getCreatedBy(), userChangeHistroy.getUserId(), userChangeHistroy);
                                submitAdjustFlow(userChangeHistroy);
                                updateById(userChangeHistroy);
                            }
                        } catch (Throwable e) {
                            log.error(e.getMessage(), e);
                        }
                    } else {
                        SysTeamUserRequest sysTeamUserRequest = buildSysTeamUserRequest(userChangeHistroy);
                        assert jsonObject != null;
                        jsonObject.put("first", "complete");
                        userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
                        try {
                            personalMapper.updateTeamRole(sysTeamUserRequest);
                            userChangeHistroy.setBeforeIsAgree("1");
                            userChangeHistroy.setIsCompleted("true");
                            //   log.error("保存用户结束"+JSONObject.toJSONString(sysTeamUserRequest));
                            this.updateById(userChangeHistroy);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }

            } else {
                personalMapper.deletePersonUserId(userChangeHistroy.getUserId());
                LocalDateTime currentDateTime = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String currentTime = currentDateTime.format(formatter);
                personalMapper.deletePersonHistoryByUserId(userChangeHistroy.getUserId(), currentTime);
                userChangeHistroy.setBeforeIsAgree("1");
                userChangeHistroy.setIsDelete("1");
                userChangeHistroy.setIsCompleted("true");
                this.updateById(userChangeHistroy);
            }
        }

        return TeamApproveTypeEnum.APPROVE_SUCCESS.getDesc();
    }

    @Override
    public List<UserChangeHistroy> getUserChangeHistoryUserId(String userId) {
        if (userId != null && !userId.equals("")) {
            List list = null;
            try {
                UserChangeHistroy userChangeHistroy = new UserChangeHistroy();
                userChangeHistroy.setUserId(userId);
                list = userChanageHistoryMapper.getUserChangeHistory(userChangeHistroy);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (list != null && list.size() > 0) {
                return list;
            }
        }
        return null;
    }

    public List<UserChangeHistroy> getUserChangeHistory(UserChangeHistroy userChangeHistroy) {
        List list = null;
        try {
            list = userChanageHistoryMapper.getUserChangeHistory(userChangeHistroy);
        } catch (Exception e) {
            log.error("报错了");
            log.error(e.getMessage(), e);
        }
        if (list != null && list.size() > 0) {
            return list;
        }
        return null;
    }

    /**
     * 审批流待办回调逻辑
     *
     * @param taskId        任务 ID
     * @param busId         公交车 ID
     * @param procInstIdUrl proc inst id url
     * @return {@link Map}
     */
    public Map getBusinessDetail(String taskId, String busId, String procInstIdUrl) {
        log.info("请求参数:taskId" + taskId + "业务busId:" + busId);
        Map<String, Object> mapJson = new HashMap<>();
        Map<String, Object> mapObject = new HashMap<>();
        UserChangeHistroy userChangeHistroyTp = new UserChangeHistroy();
        Integer busldInt = null;
        if (StringUtils.isNotBlank(busId)) {
            try {
                busldInt = Integer.parseInt(busId);
            } catch (NumberFormatException e) {
                log.error(e.getMessage(), e);
                log.error("请求参数:taskId" + taskId + "业务busId:" + busId);
                return null;
            }
        }
        if ((StringUtils.isBlank(busId)) && (StringUtils.isNotBlank(taskId))) {
            userChangeHistroyTp.setProcessInstanceId(taskId);
        } else {
            userChangeHistroyTp.setId(busldInt);
        }
        List<UserChangeHistroy> userChangeHistroys = this.getUserChangeHistory(userChangeHistroyTp);
        if (userChangeHistroys != null && userChangeHistroys.size() > 0) {
            UserChangeHistroy userChangeHistroy = userChangeHistroys.get(0);
            String loginAccount = userChangeHistroy.getLoginAccount();
            if (loginAccount != null && !loginAccount.equals("")) {
                SysTeamUserRequest sysTeamUserRequest = new SysTeamUserRequest();
                sysTeamUserRequest.setLoginAccount(userChangeHistroy.getLoginAccount());
                List<SysTeamUserDTO> userDTOS = personalMapper.findPersonalInfo(sysTeamUserRequest);
                if (userDTOS != null && userDTOS.size() > 0 && userDTOS.get(0) != null) {
                    SysTeamUserDTO sysTeamUserDTO = userDTOS.get(0);
                    String userName = sysTeamUserDTO.getUserName();
                    mapJson.put("titleName", userName + "的岗位调整");
                    mapJson.put("userName", userName);
                } else {
                    mapJson.put("titleName", userChangeHistroy.getLoginAccount() + "的岗位调整");
                    mapJson.put("userName", userChangeHistroy.getLoginAccount());
                }
            }
            mapJson.put("adjustBeforeTeam", userChangeHistroy.getAdjustBeforeTeamName());
            mapJson.put("adjustAfterTeam", userChangeHistroy.getAdjustAfterTeamName());
            mapJson.put("adjustBeforeRole", userChangeHistroy.getAdjustBeforeRoleName());
            mapJson.put("adjustAfterRole", userChangeHistroy.getAdjustAfterRoleName());
            mapJson.put("adjustBeforeChildRole", userChangeHistroy.getAdjustBeforeChildRole());
            mapJson.put("adjustAfterChildRole", userChangeHistroy.getAdjustAfterChildRole());
            mapJson.put("adjustBeforeChildTeam", userChangeHistroy.getAdjustBeforeChildTeam());
            mapJson.put("adjustAfterChildTeam", userChangeHistroy.getAdjustAfterChildTeam());
            mapJson.put("procInstId", taskId);
            mapJson.put("procInstIdUrl", procInstIdUrl);
            mapObject.put("appUrl", "https://iwork.faw.cn/home?taskInstanceCode=" + busId);
            mapObject.put("pcUrl", "https://iwork.faw.cn/home?taskInstanceCode=" + busId);
            mapObject.put("files", new JSONArray());
            mapObject.put("detail", mapJson);
            return mapObject;
        }

        return null;
    }

    /**
     * 结束点回调的拒绝或撤回处理逻辑
     *
     * @param execution 执行
     */
    @Override
    public void handleRejectOrWithdraw(BpmEndCallBackDTO.Execution execution) {
        String businessKey = execution.getBusinessKey();
        String processInstanceId = execution.getProcessInstanceId();
        String deleteReason = execution.getDeleteReason();

        UserChangeHistroy userChangeHistroy = new UserChangeHistroy();
        userChangeHistroy.setId(Integer.parseInt(businessKey));
        List<UserChangeHistroy> userChangeHistoryList = getUserChangeHistory(userChangeHistroy);
        if (CollectionUtil.isNotEmpty(userChangeHistoryList)) {
            userChangeHistroy = userChangeHistoryList.get(CommonConstant.INTEGER_0);
        }

        // 审批驳回或撤回流程执行
        userChangeHistroy.setAfterApproverDesc(ProcessConstant.REJECT_TO_START.equals(deleteReason) ? "申请被驳回!!" : "申请被撤回!!");
        userChangeHistroy.setProcessInstanceId(processInstanceId);
        userChangeHistroy.setIsCompleted("true");
        if (StringUtils.isNotBlank(userChangeHistroy.getBeforeIsAgree())) {
            userChangeHistroy.setAfterIsAgree("0");
        } else {
            userChangeHistroy.setBeforeIsAgree("0");
        }
        try {
            this.updateById(userChangeHistroy);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 结束点回调的通过处理逻辑
     *
     * @param execution 执行
     */
    @Override
    public void handleApprovePass(BpmEndCallBackDTO.Execution execution) {
        String businessKey = execution.getBusinessKey();
        String processInstanceId = execution.getProcessInstanceId();

        UserChangeHistroy userChangeHistroy = new UserChangeHistroy();
        userChangeHistroy.setId(Integer.parseInt(businessKey));
        List<UserChangeHistroy> userChangeHistoryList = getUserChangeHistory(userChangeHistroy);
        if (CollectionUtil.isNotEmpty(userChangeHistoryList)) {
            userChangeHistroy = userChangeHistoryList.get(CommonConstant.INTEGER_0);
        }

        if (!(org.apache.commons.lang3.StringUtils.isNotBlank(userChangeHistroy.getIsDelete())
                && (userChangeHistroy.getIsDelete().equals("1") || userChangeHistroy.getIsDelete().equals("0")))) {
            String approveNodes = userChangeHistroy.getApproveNodes();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(approveNodes)) {
                JSONObject jsonObject = JSONObject.parseObject(approveNodes);
                //先判断是几步审批，然后判断第一步审批是否完成,如果第一步完成,则进入第二步
                if (jsonObject != null && jsonObject.size() == 2) {
                    try {
                        if (jsonObject.get("first") != null && jsonObject.getString("first").equals("complete")) {
                            log.info("两步审批，历史记录中第一个已经执行完毕，目前是第二个审批");
                            LocalDateTime currentDateTime = LocalDateTime.now();
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            String currentTime = currentDateTime.format(formatter);
                            jsonObject.put("second", "complete");
                            userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
                            userChangeHistroy.setAfterIsAgree("1");
                            userChangeHistroy.setIsCompleted("true");
                            userChangeHistroy.setProcessInstanceId(processInstanceId);
                            userChangeHistroy.setUpdatedTime(currentTime);
                            SysTeamUserRequest sysTeamUserRequest = buildSysTeamUserRequest(userChangeHistroy);
                            try {
                                personalMapper.updateTeamRole(sysTeamUserRequest);
                                this.updateById(userChangeHistroy);
                            } catch (Exception e) {
                                log.error(e.getMessage(), e);
                            }
                        } else {
                            log.info("两步审批，历史记录没有执行记录，目前是第一个审批");
                            userChangeHistroy.setBeforeIsAgree("1");
                            jsonObject.put("first", "complete");
                            userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
//                            approveProcessService.startWorkflowChangeUser(userChangeHistroy.getCreatedBy(), userChangeHistroy.getUserId(), userChangeHistroy);
                            submitAdjustFlow(userChangeHistroy);
                            updateById(userChangeHistroy);
                        }
                    } catch (Throwable e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    log.info("一步审批");
                    SysTeamUserRequest sysTeamUserRequest = buildSysTeamUserRequest(userChangeHistroy);
                    assert jsonObject != null;
                    jsonObject.put("first", "complete");
                    userChangeHistroy.setApproveNodes(jsonObject.toJSONString());
                    try {
                        personalMapper.updateTeamRole(sysTeamUserRequest);
                        userChangeHistroy.setBeforeIsAgree("1");
                        userChangeHistroy.setIsCompleted("true");
                        this.updateById(userChangeHistroy);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }

        } else {
            // 处理人员删除的变更（一步）
            personalMapper.deletePersonUserId(userChangeHistroy.getUserId());
            LocalDateTime currentDateTime = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String currentTime = currentDateTime.format(formatter);
            personalMapper.deletePersonHistoryByUserId(userChangeHistroy.getUserId(), currentTime);
            userChangeHistroy.setBeforeIsAgree("1");
            userChangeHistroy.setIsDelete("1");
            userChangeHistroy.setIsCompleted("true");
            this.updateById(userChangeHistroy);
        }
    }

    @Override
    public String submitAdjustFlow(UserChangeHistroy userChangeHistory) {
        //1.获得申请人相关信息
        SysTeamUserDTO sysTeamUserDTO = getApplicantInfo(userChangeHistory.getLoginAccount());

        if (sysTeamUserDTO != null) {
            userChangeHistory.setLoginAccount(sysTeamUserDTO.getLoginAccount());
            userChangeHistory.setAdjustBeforeProductChain(sysTeamUserDTO.getProductChain());
        } else {
            log.info("申请人相关信息根据域账号{}查不到", userChangeHistory.getLoginAccount());
            return null;
        }

        //判断是否是多个流程,并是否进入第二个流程,如果没有则说明首次进入
        String approvePerson;
        if (StringUtils.isNotBlank(userChangeHistory.getApproveNodes())) {
            JSONObject jsonObject = JSONObject.parseObject(userChangeHistory.getApproveNodes());
            approvePerson = jsonObject.getString("second");
        } else {
            List<TeamInfo> teamBefore = teamInfoService.getTeamByTeamId(userChangeHistory.getAdjustBeforeTeam());
            List<TeamInfo> teamAfter = teamInfoService.getTeamByTeamId(userChangeHistory.getAdjustAfterTeam());

            //审批人处理----开始
            // 前战队长、前等同战队长、前战队长姓名
            String beforeTeamManager;
            String beforeTeamSecondManager;
            String beforeTeamManagerName = "";
            boolean adjustBeforeTeamNameFlag = false;
            // 针对首次没有战队长的人，给赵艳辉审批, 审批前战队名称默认为空，不再是"保密"
            if (StringUtils.isBlank(userChangeHistory.getAdjustBeforeTeam())) {
                beforeTeamManager = "zhaoyanhui3";
                beforeTeamSecondManager = "";
                adjustBeforeTeamNameFlag = true;
            } else if (CollectionUtils.isNotEmpty(teamBefore)) {
                beforeTeamManager = teamBefore.get(0).getTeamMagager();
                beforeTeamSecondManager = teamBefore.get(0).getTeamSecondManager();
                SysTeamUserRequest beforeManager = new SysTeamUserRequest();
                beforeManager.setLoginAccount(beforeTeamManager);
                SysTeamUserDTO sysTeamUserTeam = personalMapper.findPersonalByLoginAccount(beforeManager);
                if (sysTeamUserTeam != null) {
                    beforeTeamManagerName = sysTeamUserTeam.getUserName();
                }
            } else {
                log.info("审批前战队有输入，但是查不到！");
                return null;
            }

            // 后战队长、后等同战队长、后战队长姓名
            String afterTeamManager;
            String afterTeamSecondManager;
            String afterTeamManagerName = "";
            if (CollectionUtils.isNotEmpty(teamAfter)) {
                afterTeamManager = teamAfter.get(0).getTeamMagager();
                afterTeamSecondManager = teamAfter.get(0).getTeamSecondManager();
                SysTeamUserRequest afterManager = new SysTeamUserRequest();
                afterManager.setLoginAccount(afterTeamManager);
                SysTeamUserDTO sysTeamUserTeam = personalMapper.findPersonalByLoginAccount(afterManager);
                if (sysTeamUserTeam != null) {
                    afterTeamManagerName = sysTeamUserTeam.getUserName();
                }
            } else {
                log.info("审批后战队有输入，但是查不到！");
                return null;
            }
            log.info("审批前后战队长及等同战队长角色分别是：前：{}，{}；后：{}，{}", beforeTeamManager, beforeTeamSecondManager,
                    afterTeamManager, afterTeamSecondManager);

            JSONObject jsonObject = new JSONObject();
            if (StringUtils.isNotBlank(beforeTeamManager) && StringUtils.isNotBlank(afterTeamManager) &&
                    !beforeTeamManager.equals(afterTeamManager)) {
                //将申请放入记录中
                if (StringUtils.isNotEmpty(beforeTeamSecondManager)) {
                    beforeTeamManager = beforeTeamManager + "," + beforeTeamSecondManager;
                }

                if (StringUtils.isNotEmpty(afterTeamSecondManager)) {
                    afterTeamManager = afterTeamManager + "," + afterTeamSecondManager;
                }
                jsonObject.put("first", beforeTeamManager);
                jsonObject.put("second", afterTeamManager);
                userChangeHistory.setApproveNodes(jsonObject.toJSONString());

                approvePerson = beforeTeamManager;
            } else {
                if (StringUtils.isNotEmpty(beforeTeamManager)) {
                    approvePerson = beforeTeamManager;
                    if (StringUtils.isNotEmpty(beforeTeamSecondManager)) {
                        approvePerson = beforeTeamManager + "," + beforeTeamSecondManager;
                    }
                } else {
                    approvePerson = afterTeamManager;
                    if (StringUtils.isNotEmpty(afterTeamSecondManager)) {
                        approvePerson = afterTeamManager + "," + afterTeamSecondManager;
                    }
                }

                log.info("审批人是{}", approvePerson);
                jsonObject.put("first", beforeTeamManager.isEmpty() ? afterTeamManager : beforeTeamManager);
                userChangeHistory.setApproveNodes(jsonObject.toJSONString());
            }

            //审批人处理----接收
            //角色处理---开始
            String beforeRoleId = "";
            String afterRoleId = "";
            String beforeRoleName = "";
            String afterRoleName = "";
            String beforeRoleIdMinor = "";
            String beforeRoleNameMinor = "";
            String afterRoleIdMinor = "";
            String afterRoleNameMinor = "";
            if (StringUtils.isNotBlank(userChangeHistory.getAdjustBeforeRole())) {
                TeamRole teamRole = teamRoleService.getById(userChangeHistory.getAdjustBeforeRole());
                beforeRoleName = teamRole.getRoleName();
                beforeRoleId = userChangeHistory.getAdjustBeforeRole();
            }
            if (StringUtils.isNotBlank(userChangeHistory.getAdjustAfterRole())) {
                TeamRole teamRole = teamRoleService.getById(userChangeHistory.getAdjustAfterRole());
                afterRoleName = teamRole.getRoleName();
                afterRoleId = userChangeHistory.getAdjustAfterRole();
            }
            if (StringUtils.isNotBlank(userChangeHistory.getAdjustBeforeRoleMinor())) {
                TeamRole teamRole = teamRoleService.getById(userChangeHistory.getAdjustBeforeRoleMinor());
                beforeRoleNameMinor = teamRole.getRoleName();
                beforeRoleIdMinor = userChangeHistory.getAdjustBeforeRoleMinor();
            }
            if (StringUtils.isNotBlank(userChangeHistory.getAdjustAfterRoleMinor())) {
                TeamRole teamRole = teamRoleService.getById(userChangeHistory.getAdjustAfterRoleMinor());
                afterRoleNameMinor = teamRole.getRoleName();
                afterRoleIdMinor = userChangeHistory.getAdjustAfterRoleMinor();
            }
            //角色处理---结束
            if (StringUtils.isBlank(beforeTeamManagerName)) {
                beforeTeamManagerName = sysTeamUserDTO.getHomeDevTeam();
            }
            if (StringUtils.isBlank(beforeRoleName)) {
                beforeRoleName = sysTeamUserDTO.getApplicationRole();
            }
            userChangeHistory.setAdjustBeforeTeam(teamBefore != null && !teamBefore.isEmpty() && teamBefore.get(0) != null && teamBefore.get(0).getTeamId() != null ? teamBefore.get(0).getTeamId().toString() : "");
            userChangeHistory.setAdjustAfterTeam(!teamAfter.isEmpty() && teamAfter.get(0) != null && teamAfter.get(0).getTeamId() != null ? teamAfter.get(0).getTeamId().toString() : "");
            if (adjustBeforeTeamNameFlag) {
                // 审批前战队名称默认为空，不再是"保密"
                userChangeHistory.setAdjustBeforeTeamName("");
            } else {
                userChangeHistory.setAdjustBeforeTeamName(!teamBefore.isEmpty() && teamBefore.get(0) != null && teamBefore.get(0).getTeamName() != null && !teamBefore.get(0).getTeamName().isEmpty() ? teamBefore.get(0).getTeamName() : "");
            }
            userChangeHistory.setAdjustAfterTeamName(!teamAfter.isEmpty() && teamAfter.get(0) != null && teamAfter.get(0).getTeamName() != null && !teamAfter.get(0).getTeamName().equals("") ? teamAfter.get(0).getTeamName() : "");
            userChangeHistory.setBeforeApprover((teamBefore != null && !teamBefore.isEmpty() && teamBefore.get(0) != null && teamBefore.get(0).getTeamMagager() != null && !teamBefore.get(0).getTeamMagager().equals("")) ? teamBefore.get(0).getTeamMagager() : "");
            userChangeHistory.setAfterApprover(!teamAfter.isEmpty() && teamAfter.get(0) != null && teamAfter.get(0).getTeamMagager() != null && !teamAfter.get(0).getTeamMagager().equals("") ? teamAfter.get(0).getTeamMagager() : "");
            userChangeHistory.setBeforeApproverName(beforeTeamManagerName);
            userChangeHistory.setAfterApproverName(afterTeamManagerName);
            userChangeHistory.setAdjustBeforeRole(beforeRoleId);
            userChangeHistory.setAdjustBeforeRoleName(beforeRoleName);
            userChangeHistory.setAdjustAfterRole(afterRoleId);
            userChangeHistory.setAdjustAfterRoleName(afterRoleName);
            //辅角色
            userChangeHistory.setAdjustBeforeRoleMinor(beforeRoleIdMinor);
            userChangeHistory.setAdjustBeforeRoleNameMinor(beforeRoleNameMinor);
            userChangeHistory.setAdjustAfterRoleMinor(afterRoleIdMinor);
            userChangeHistory.setAdjustAfterRoleNameMinor(afterRoleNameMinor);
            userChangeHistory.setIsCompleted("false");
            this.save(userChangeHistory);
        }

        //获得记录中的业务id装备传入审批流程
        String processInstanceId = "";
        if (userChangeHistory.getId() != null) {
            String taskInstanceCode = String.valueOf(userChangeHistory.getId());
            Map<String, String> map = getApproveMap(userChangeHistory, sysTeamUserDTO, taskInstanceCode);
            try {
                String createdBy = userChangeHistory.getCreatedBy();
                log.info("creatorCode {}", createdBy);
                StartProcessDTO.BizModel bizModel = new StartProcessDTO.BizModel();
                bizModel.setApplyHeader(map);

                Map<String, String> assignees = new HashMap<>();
                assignees.put("审批", approvePerson);

                if (StringUtils.isBlank(createdBy)) {
                    createdBy = userChangeHistory.getCurrentLoginAccount();
                }
                processInstanceId = processService.startSingleNodeWorkflow(createdBy, assignees,
                        bizModel, taskInstanceCode);
                updateUserChangeHistory(taskInstanceCode, processInstanceId);
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
                throw new BizException(e.getMessage());
            }
            log.info("线程启动成功，返回流程id：{}", processInstanceId);
            return processInstanceId;
        }

        return processInstanceId;
    }

    @NotNull
    private static Map<String, String> getApproveMap(UserChangeHistroy userChangeHistory, SysTeamUserDTO sysTeamUserDTO, String taskInstanceCode) {
        Map<String, String> map = new HashMap<>();
        if (sysTeamUserDTO.getUserName() != null && !sysTeamUserDTO.getUserName().equals("")) {
            map.put("titleName", sysTeamUserDTO.getUserName() + "的岗位调整");
        } else {
            map.put("titleName", "岗位调整");
        }
        map.put("userName", sysTeamUserDTO.getUserName());
        map.put("adjustBeforeTeam", userChangeHistory.getAdjustBeforeTeamName());
        map.put("adjustAfterTeam", userChangeHistory.getAdjustAfterTeamName());
        map.put("adjustBeforeRole", userChangeHistory.getAdjustBeforeRoleName());
        map.put("adjustAfterRole", userChangeHistory.getAdjustAfterRoleName());
        map.put("adjustBeforeChildRole", userChangeHistory.getAdjustBeforeChildRole());
        map.put("adjustAfterChildRole", userChangeHistory.getAdjustAfterChildRole());
        map.put("adjustBeforeChildTeam", userChangeHistory.getAdjustBeforeChildTeam());
        map.put("adjustAfterChildTeam", userChangeHistory.getAdjustAfterChildTeam());

        map.put("adjustBeforeRoleMinor", userChangeHistory.getAdjustBeforeRoleNameMinor());
        map.put("adjustAfterRoleMinor", userChangeHistory.getAdjustAfterRoleNameMinor());
        map.put("adjustBeforeChildRoleMinor", userChangeHistory.getAdjustBeforeChildRoleMinor());
        map.put("adjustAfterChildRoleMinor", userChangeHistory.getAdjustAfterChildRoleMinor());

        map.put("adjustBeforeProductChain", userChangeHistory.getAdjustBeforeProductChain());
        map.put("adjustAfterProductChain", userChangeHistory.getAdjustAfterProductChain());

        map.put("code", taskInstanceCode);
        return map;
    }

    /**
     * 获取申请人信息
     *
     * @param loginAccount 登录帐户
     * @return {@link SysTeamUserDTO }
     */
    private SysTeamUserDTO getApplicantInfo(String loginAccount) {
        SysTeamUserRequest sysTeamUserRequest = new SysTeamUserRequest();
        sysTeamUserRequest.setLoginAccount(loginAccount);
        List<SysTeamUserDTO> userDTOS = personalMapper.findPersonalInfo(sysTeamUserRequest);
        return userDTOS != null && !userDTOS.isEmpty() && userDTOS.get(0) != null ? userDTOS.get(0) : null;
    }

    private SysTeamUserRequest buildSysTeamUserRequest(UserChangeHistroy userChangeHistroy) {
        SysTeamUserRequest sysTeamUserRequest = new SysTeamUserRequest();
        sysTeamUserRequest.setLoginAccount(userChangeHistroy.getLoginAccount());
        sysTeamUserRequest.setTeam(userChangeHistroy.getAdjustAfterTeamName());
        sysTeamUserRequest.setTeamId(userChangeHistroy.getAdjustAfterTeam());
        sysTeamUserRequest.setApplicationRole(userChangeHistroy.getAdjustAfterRoleName());
        sysTeamUserRequest.setApplicationRoleId(userChangeHistroy.getAdjustAfterRole());
        sysTeamUserRequest.setSubdividedRole(userChangeHistroy.getAdjustAfterChildRole());
        sysTeamUserRequest.setHomeDevTeam(userChangeHistroy.getAdjustAfterChildTeam());
        sysTeamUserRequest.setMinorRoleName(userChangeHistroy.getAdjustAfterRoleNameMinor());
        sysTeamUserRequest.setMinorRoleId(userChangeHistroy.getAdjustAfterRoleMinor());
        sysTeamUserRequest.setMinorSubdividedRole(userChangeHistroy.getAdjustAfterChildRoleMinor());
        sysTeamUserRequest.setProductChain(userChangeHistroy.getAdjustAfterProductChain());
        return sysTeamUserRequest;
    }

    private void updateUserChangeHistory(String taskInstanceCode, String processInstanceId) {
        if (taskInstanceCode != null && !taskInstanceCode.isEmpty() && processInstanceId != null && !processInstanceId.isEmpty()) {
            UserChangeHistroy userChangeHistroy = new UserChangeHistroy();
            userChangeHistroy.setId(Integer.parseInt(taskInstanceCode));
            userChangeHistroy.setProcessInstanceId(processInstanceId);
            userChangeHistroy.setIsCompleted("false");
            this.updateById(userChangeHistroy);
        }
    }
}
