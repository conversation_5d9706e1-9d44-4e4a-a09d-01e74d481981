package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.model.dto.operation.OperationStatisticsCardRequest;
import com.faw.sa0214.tak.model.dto.operation.OperationStatisticsCardResponse;
import com.faw.sa0214.tak.model.response.OperationStatisticsResponse;
import com.faw.sa0214.tak.service.operation.OperationSupportStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运维支持统计
 *
 * <AUTHOR>
 * @date 2025/04/21
 */
@Tag(name = "运维支持统计")
@RestController
@RequestMapping("/operationStatistics")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OperationSupportStatisticsController {

    private final OperationSupportStatisticsService operationSupportStatisticsService;

    /**
     * 运维统计-小看板卡片
     *
     * @param request 请求
     * @return {@link Result }<{@link OperationStatisticsCardResponse }>
     */
    @Operation(summary = "运维统计-小看板卡片", description = "[author:10027705]")
    @PostMapping("/getOperationCard")
    public Result<OperationStatisticsCardResponse> getOperationCard(@RequestBody OperationStatisticsCardRequest request) {
        return operationSupportStatisticsService.getOperationCard(request);
    }

    /**
     * 查询运维统计列表
     */
    @Operation(summary = "运维统计-看板列表", description = "[author:10027705]")
    @PostMapping("/getOperationStatisticsList")
    public Result<OperationStatisticsResponse> getOperationStatisticsList(@RequestBody OperationStatisticsCardRequest request) {
        return operationSupportStatisticsService.getOperationStatisticsList(request);
    }

}

