package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.model.dto.AITaskScoreEvaluationDTO;
import com.faw.sa0214.tak.model.dto.MetricsParam;
import com.faw.sa0214.tak.model.request.ProjectSprintRequest;
import com.faw.sa0214.tak.model.request.ScrumHoursRequest;
import com.faw.sa0214.tak.model.request.ScrumItemRequest;
import com.faw.sa0214.tak.po.AITaskScoreEvaluationPO;
import com.faw.sa0214.tak.service.AiTaskScoreEvaluationService;
import com.faw.sa0214.tak.service.CodeInspectService;
import com.faw.sa0214.tak.service.DevopsSyncService;
import com.faw.sa0214.tak.service.MetricsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运维接口
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
@Tag(name = "运维用接口", description = "运维用接口")
@Slf4j
@RestController
@RequestMapping("/maintenance")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MaintenanceController {

    private final MetricsService metricsService;
    private final DevopsSyncService devopsSyncService;
    private final CodeInspectService codeInspectService;
    private final AiTaskScoreEvaluationService aiTaskScoreEvaluationService;

    @Operation(summary = "拉取特定项目下特定人的敏捷事项", description = "拉取特定项目下特定人的敏捷事项[author:10027705]")
    @PostMapping("/scrumItem")
    public Result<String> getScrumItem(@Validated @RequestBody ScrumItemRequest request) {

        List<Long> busIds = request.getBusIds();
        String startTime = request.getStartTime();
        String endTime = request.getEndTime();
        List<String> userIds = request.getUserIds();

        devopsSyncService.insertTbProject(busIds, startTime, endTime, userIds);

        return Result.success("OK");
    }

    @Operation(summary = "拉取特定项目下特定人的工时", description = "拉取特定项目下特定人的工时[author:10027705]")
    @PostMapping("/hoursList")
    public Result<String> getHours(@Validated @RequestBody ScrumHoursRequest request) {

        devopsSyncService.insertTbDyHours(request.getBusIds(), request.getStartTime(), request.getEndTime(), request.getUserMap());

        return Result.success("OK");
    }

    @Operation(summary = "拉取特定项目下的迭代", description = "拉取特定项目下的迭代[author:10027705]")
    @PostMapping("/sprintList")
    public Result<String> getSprint(@Validated @RequestBody ProjectSprintRequest request) {

        devopsSyncService.insertTbDySprint(request.getBusIds(), request.getStartTime(), request.getEndTime(), request.getDateFlag());

        return Result.success("OK");
    }

    @Operation(summary = "对称加密拉取代码的token", description = "对称加密拉取代码的token[author:10027705]")
    @PostMapping("/encryption")
    public Result<String> encryption(@RequestBody List<Integer> ids) {

        codeInspectService.encryption(ids);

        return Result.success("OK");
    }

    @Operation(summary = "解密token", description = "解密token[author:10027705]")
    @PostMapping("/decryption")
    public Result<String> decryption(@RequestBody String token) {

        String decryption = codeInspectService.decryption(token);

        return Result.success(decryption);
    }

    @Operation(summary = "每日计算apdex指标分", description = "每日计算apdex指标分[author:10027705]")
    @PostMapping("/apdex")
    public Result<String> recordApdex(@RequestParam String date) {

        if (DateUtils.validateDate(date, DateUtils.FORMAT_DATE)) {
            devopsSyncService.recordApdex(date);
            return Result.success("OK");
        } else {
            return Result.failed("格式验证错误");
        }
    }

    @Operation(summary = "人员月度效能分数数据", description = "[author:50012536]")
    @PostMapping("getEvaluationByMonth")
    public Result<List<AITaskScoreEvaluationPO>> getEvaluationByMonth(@RequestBody AITaskScoreEvaluationDTO aiTaskScoreEvaluationDTO) {

        if (aiTaskScoreEvaluationDTO.getYearMonth() == null || StringUtils.isEmpty(aiTaskScoreEvaluationDTO.getYearMonth())) {
            aiTaskScoreEvaluationDTO.setYearMonth(DateUtils.getLastMonth());
        }

        MetricsParam param = new MetricsParam();
        param.setYearMonth(aiTaskScoreEvaluationDTO.getYearMonth());
        param.setUserCodeList(aiTaskScoreEvaluationDTO.getUserCodeList());
        param.setTeamId(aiTaskScoreEvaluationDTO.getTeamId());
        param.setSubTeam(aiTaskScoreEvaluationDTO.getSubTeam());
        param.setSubRole(aiTaskScoreEvaluationDTO.getSubRole());

        List<AITaskScoreEvaluationPO> aiPOList = metricsService.calcEvaluation(param);

        // 将人员的分存到数据库中, 有则更新，无则新增
        aiTaskScoreEvaluationService.saveEvaluationList(aiTaskScoreEvaluationDTO.getYearMonth(), aiPOList);

        return Result.success(aiPOList);
    }

}
