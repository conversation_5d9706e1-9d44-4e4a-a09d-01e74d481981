package com.faw.sa0214.tak.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.faw.sa0214.tak.client.MessagePushFeignClient;
import com.faw.sa0214.tak.client.UserCenterFeignClient;
import com.faw.sa0214.tak.common.constant.BizException;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.JsonUtil;
import com.faw.sa0214.tak.config.UcgConfig;
import com.faw.sa0214.tak.config.UserIdListConfig;
import com.faw.sa0214.tak.config.UserIdListMinorConfig;
import com.faw.sa0214.tak.mapper.*;
import com.faw.sa0214.tak.model.bo.DingDingMsgBo;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.dto.baseCenter.BCResult;
import com.faw.sa0214.tak.model.dto.baseCenter.BCPage;
import com.faw.sa0214.tak.model.dto.baseCenter.UserCenterInfo;
import com.faw.sa0214.tak.model.vo.PushDingUserIdVO;
import com.faw.sa0214.tak.po.GroupPushLogPO;
import com.faw.sa0214.tak.po.SysCalendar;
import com.faw.sa0214.tak.po.SysTeamUser;
import com.faw.sa0214.tak.po.TbDdWork;
import com.faw.sa0214.tak.service.DevopsSyncService;
import com.faw.sa0214.tak.service.DingDingMsgService;
import com.faw.sa0214.tak.service.TbSendMessageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【user_hours】的数据库操作Service实现
 * @createDate 2023-09-20 10:10:29
 */
@Service
@Slf4j
public class TbSendMessageServiceImpl implements TbSendMessageService {
    @Resource
    private TbSendMessageMapper tbSendMessageMapper;
    @Resource
    private TbUserSendMessageMapper tbUserSendMessageMapper;
    @Resource
    private DingDingMsgService sendDingMessage;
    @Resource
    private SysTeamUserMapper sysTeamUserMapper;
    @Resource
    DevopsSyncService devopsSyncService;
    @Resource
    private SysCalendarMapper sysCalendarMapper;
    @Autowired
    private GroupPushLogMapper groupPushLogMapper;
    @Autowired
    private UcgConfig ucgConfig;
    @Autowired
    private UserIdListConfig userIdListConfig;
    @Autowired
    private UserIdListMinorConfig userIdListMinorConfig;
    @Autowired
    private MessagePushFeignClient messagePushFeignClient;
    @Autowired
    private UserCenterFeignClient userCenterFeignClient;

    @Value(value = "${push.exclude}")
    private String excludeList;

    @Override
    public void sendMessage() {
        String time = DateUtils.formatDate(new Date(), DateUtils.FORMAT_TIME);
        if (time.compareTo("17:00:00") < 0) {
            log.info("hp_17点前不能调用");
            return;
        }
        String timeY = DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE);
        String year = timeY.split("-")[0];
        String month = timeY.split("-")[1];
        String day = timeY.split("-")[2];
        int monthi = Integer.parseInt(month);
        int dayi = Integer.parseInt(day);
        LambdaQueryWrapper<SysCalendar> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysCalendar::getYear, year);
        lambdaQueryWrapper.eq(SysCalendar::getMonth, monthi);
        lambdaQueryWrapper.eq(SysCalendar::getDay, dayi);
        List<SysCalendar> sList = sysCalendarMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(sList)) {
            if (sList.get(0).getStatus() != null && sList.get(0).getStatus() == 1) {
                return;
            }
        }
        String cTime = DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE);
        String yearMonths = DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE_YEAR_MONTH);
        TbSendMessage tbSendMess = new TbSendMessage();
        tbSendMess.setSendType(4);
        tbSendMess.setSendTime(cTime);
        TbSendMessage sendMessage = tbSendMessageMapper.getOneTbSendMessageList(tbSendMess);
        if (sendMessage != null) {
            tbSendMessageMapper.update(sendMessage);
        } else {
            sendMessage = new TbSendMessage();
            sendMessage.setSendType(4);
            sendMessage.setSendNum(1);
            sendMessage.setSendTime(cTime);
            tbSendMessageMapper.insert(sendMessage);
        }
        if (sendMessage.getSendNum() <= CommonConstant.INTEGER_3) {
            // 先更新同步下工时再筛选提醒谁
            devopsSyncService.recordHours(cTime, cTime);

            TbUserSendMessage req = new TbUserSendMessage();
            req.setCreateTime(cTime + " 00:00:00");
            req.setYearMonths(yearMonths);
            if (StringUtils.isNotBlank(excludeList)) {
                req.setExcludeList(List.of(excludeList.split(",")));
            }
            List<TbUserSendMessage> userSendMessages = tbUserSendMessageMapper.getUserMessageList(req);
            if (CollectionUtils.isNotEmpty(userSendMessages)) {
                userSendMessages.forEach(k -> {
                    k.setSendTime(cTime);
                });
                tbUserSendMessageMapper.insertBatch(userSendMessages);
                List<String> userList = userSendMessages.stream().map(TbUserSendMessage::getLoginAccount).collect(Collectors.toList());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("currentPage", 1);
                jsonObject.put("pageSize", 1000);
                jsonObject.put("loginNames", userList);
                BCResult<UserCenterInfo> userCenterList = userCenterFeignClient.queryUserCenterList(jsonObject);
                BCPage<UserCenterInfo> data = userCenterList.getData();
                if (data != null) {
                    List<UserCenterInfo> records = data.getRecords();
                    List<String> phoneList = records.stream().map(UserCenterInfo::getMobile).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(phoneList)) {
                        List<DingDingMsgBo> dingDingMsgBoList = sendDingMessage.getDingMsgBoList(phoneList);
                        sendDingMessage.pushMsg(dingDingMsgBoList);
                    }
                }
            }
        }
    }


    @Override
    public Integer getDingTalkLeave(String date) {

        // 查询钉钉请假起止日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_DATE);
        LocalDate localDate = LocalDate.parse(date, formatter);
        LocalDateTime startDate = localDate.atTime(8, 30, 0);
        LocalDateTime endDate = localDate.atTime(17, 30, 0);
        long startTimeStamp = startDate.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
        long endTimeStamp = endDate.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();

        // 查询日期是否为休息日，休息日不查询请假日期
        LambdaQueryWrapper<SysCalendar> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysCalendar::getYear, localDate.getYear());
        lambdaQueryWrapper.eq(SysCalendar::getMonth, localDate.getMonth());
        lambdaQueryWrapper.eq(SysCalendar::getDay, localDate.getDayOfMonth());
        List<SysCalendar> list = sysCalendarMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            if (list.get(0).getStatus() != null && list.get(0).getStatus() == 1) {
                return 0;
            }
        }

        // 查询钉钉请假时间
        List<SysTeamUser> userList = sysTeamUserMapper.getCurrentList();
        List<Map<String, BigDecimal>> userLeaveTime = sendDingMessage.getDingTalkLeave(startTimeStamp, endTimeStamp, userList);

        // 数据持久化, 旧数据先归零
        log.info("getDingTalkLeave, date:{}, persistence start!!", JSON.toJSONString(date));
        List<TbDdWork> oldList = new ArrayList<>();
        userList.forEach(user -> {
            TbDdWork work = new TbDdWork();
            work.setUserId(user.getLoginAccount());
            SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.FORMAT_DATE);
            try {
                work.setWorkDay(dateFormat.parse(date));
            } catch (ParseException e) {
                log.error("parse date error! input:{}", JsonUtil.toJsonString(date), e);
                throw new BizException("日期解析失败!");
            }
            work.setLeaveDurationHours(0d);
            oldList.add(work);
        });
        sysTeamUserMapper.batchUpdateLeave(oldList);

        if (userLeaveTime.isEmpty()) {
            return 0;
        } else {
            return batchUpdate(userList, userLeaveTime, date);
        }
    }

    /**
     * 批量更新
     *
     * @param userList      用户列表
     * @param userLeaveTime 用户休假时间
     * @param date          日期
     * @return {@link Integer}
     */
    private Integer batchUpdate(List<SysTeamUser> userList, List<Map<String, BigDecimal>> userLeaveTime, String date) {

        //idm账户与域账号转换
        Map<String, String> idmToCodeMap = userList.stream()
                .filter(user -> StringUtils.isNotBlank(user.getUserId()))
                .filter(user -> StringUtils.isNotBlank(user.getLoginAccount()))
                .collect(Collectors.toMap(
                        SysTeamUser::getUserId,
                        SysTeamUser::getLoginAccount,
                        (account1, account2) -> account2));
        List<TbDdWork> updatedList = new ArrayList<>();
        userLeaveTime.forEach(
                user -> {
                    for (Map.Entry<String, BigDecimal> entry : user.entrySet()) {
                        TbDdWork hour = new TbDdWork();
                        String userId = idmToCodeMap.get(entry.getKey());
                        if (StringUtils.isNotBlank(userId)) {
                            hour.setUserId(userId);
                            SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.FORMAT_DATE);
                            try {
                                hour.setWorkDay(dateFormat.parse(date));
                            } catch (ParseException e) {
                                log.error("parse date error! input:{}", JsonUtil.toJsonString(date), e);
                                throw new BizException("日期解析失败!");
                            }
                            hour.setLeaveDurationHours(entry.getValue().doubleValue());
                            updatedList.add(hour);
                        }
                    }
                });
        log.info("update leave info list:{}", updatedList);
        sysTeamUserMapper.batchUpdateLeave(updatedList);
        return updatedList.size();
    }


    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void createDingWeekly(PushDingDTO createDTO) {
        if (createDTO == null) {
            createDTO = new PushDingDTO();
        }
        String createDate = getCreateDate(createDTO.getDate());
        List<PushDingUserIdVO> userIdList = userIdListConfig.getUserIdList();
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        List<GroupPushLogPO> pushLogList = new ArrayList<>();
        for (PushDingUserIdVO vo : userIdList) {
            GroupPushLogPO pushLogPO = new GroupPushLogPO();
            pushLogPO.setUserId(vo.getId());
            pushLogPO.setUserName(vo.getName());
            pushLogPO.setPushRegionName(ucgConfig.getPushRegionName());
            pushLogPO.setPushTime("");
            pushLogPO.setCreateDate(createDate);
            pushLogPO.setStatus(0);
            String url = ucgConfig.getGroupUrl() + "?pushRegionName=" + ucgConfig.getPushRegionName() + "&createdDate=" + createDate + "&sign=" + getSign(ucgConfig.getPushRegionName(), createDate);

            pushLogPO.setUrl(url);
            pushLogPO.setFailReason("");
            pushLogPO.setCreateTime(new Date());
            pushLogList.add(pushLogPO);
        }
        groupPushLogMapper.insertBatch(pushLogList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void createDingWeeklyMinor(PushDingDTO createDTO) {
        if (createDTO == null) {
            createDTO = new PushDingDTO();
        }
        String createDate = getCreateDate(createDTO.getDate());
        List<PushDingUserIdVO> userIdList = userIdListMinorConfig.getUserIdListMinor();
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        List<GroupPushLogPO> pushLogList = new ArrayList<>();
        for (PushDingUserIdVO vo : userIdList) {
            GroupPushLogPO pushLogPO = new GroupPushLogPO();
            pushLogPO.setUserId(vo.getId());
            pushLogPO.setUserName(vo.getName());
            pushLogPO.setPushRegionName(ucgConfig.getPushRegionNameMinor());
            pushLogPO.setPushTime("");
            pushLogPO.setCreateDate(createDate);
            pushLogPO.setStatus(0);
            String url = ucgConfig.getGroupUrlMinor() + "?pushRegionName=" + ucgConfig.getPushRegionNameMinor() + "&createdDate=" + createDate + "&sign=" + getSign(ucgConfig.getPushRegionNameMinor(), createDate);

            pushLogPO.setUrl(url);
            pushLogPO.setFailReason("");
            pushLogPO.setCreateTime(new Date());
            pushLogList.add(pushLogPO);
        }
        groupPushLogMapper.insertBatch(pushLogList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void pushDingWeekly(PushDingDTO dto) {
        if (dto == null) {
            dto = new PushDingDTO();
        }
        String createDate = getCreateDate(dto.getDate());
        List<GroupPushLogPO> pushLogPOList = groupPushLogMapper.getListForUnPush(createDate, ucgConfig.getPushRegionName());
        if (org.springframework.util.CollectionUtils.isEmpty(pushLogPOList)) {
            return;
        }
        // 推送工作通知
        Integer status = 1;
        String failReason = "";
        List<String> userList = new ArrayList<>();
        for (GroupPushLogPO po : pushLogPOList) {
            userList.add(po.getUserId());
        }
        String url = pushLogPOList.get(0).getUrl();
        try {
            // 推送集团消息(第二种推送方式)
            // 构造钉钉数据，推送钉钉
            StringBuilder markdown = new StringBuilder();
            markdown.append("![](").append(ucgConfig.getGroupImg()).append(")  \n  ");
            markdown.append("##### ").append("**").append("IT工程师作业平台(旗效2.0)").append("**").append("  \n  ");
            markdown.append("###### ").append("请查看数字化部IT工程师角色的战队运营、角色运营、工时运营、业务单元运营等数据").append("  \n  ");
            markdown.append("###### 发布时间：").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));

            JSONObject templateData = new JSONObject();
            templateData.put("title", "IT工程师作业平台(旗效2.0)");
            templateData.put("markdown", markdown.toString());
            templateData.put("single_title", "点击查看详情");
            templateData.put("single_url", url);

            JSONObject params = new JSONObject();
            params.put("agent_id", ucgConfig.getAgentId());
            params.put("template_data", templateData);
            params.put("template_id", "201");
            params.put("to_user", userList);
            params.put("user_type", "IDM");
            log.info("请求参数：{}", params.toJSONString());
            JSONObject result = messagePushFeignClient.sendDingMsg(params);
            log.info("请求结果：{}", result.toJSONString());
            if (!"0".equals(result.getString("code"))) {
                status = 2;
                failReason = result.getString("msg");
            }

            // 推送集团统一平台消息(第一种推送方式)
//            List<Object> list = new ArrayList();
//            for (GroupPushLogPO po : pushLogPOList) {
//                JSONObject userList = new JSONObject();
//                userList.put("userCode", po.getUserId());
//                list.add(userList);
//            }
//
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("modCode", ucgConfig.getModCode());
//            JSONObject param = new JSONObject();
//            param.put("变量1",LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
//            jsonObject.put("param", param);
//            jsonObject.put("userList", list);
//            jsonObject.put("detailsTitle", "点击查看详情");
//            jsonObject.put("details", url);
//            jsonObject.put("detailsType", "2");
//            jsonObject.put("imgurl", ucgConfig.getGroupImg());
//
//            log.info("请求参数：{}", jsonObject.toJSONString());
//            JSONObject result = messagePushFeignClient.sendWorkMsg(jsonObject);
//            log.info("请求结果：{}", result.toJSONString());
//            if (!"200".equals(result.getString("code"))) {
//                status = 2;
//                failReason = result.getString("msg");
//            }

        } catch (Exception e) {
            log.error("推送消息错误!", e);
            status = 2;
            failReason = e.getMessage();
        }

        //更新推送状态
        for (GroupPushLogPO po : pushLogPOList) {
            GroupPushLogPO updatePO = new GroupPushLogPO();
            updatePO.setId(po.getId());
            updatePO.setPushTime(getNowStringDate());
            updatePO.setStatus(status);
            updatePO.setFailReason(failReason);
            groupPushLogMapper.updateByPrimaryKeySelective(updatePO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void pushDingWeeklyMinor(PushDingDTO dto) {
        if (dto == null) {
            dto = new PushDingDTO();
        }
        String createDate = getCreateDate(dto.getDate());
        List<GroupPushLogPO> pushLogPOList = groupPushLogMapper.getListForUnPush(createDate, ucgConfig.getPushRegionNameMinor());
        if (org.springframework.util.CollectionUtils.isEmpty(pushLogPOList)) {
            return;
        }
        // 推送工作通知
        Integer status = 1;
        String failReason = "";
        List<String> userList = new ArrayList<>();
        for (GroupPushLogPO po : pushLogPOList) {
            userList.add(po.getUserId());
        }
        String url = pushLogPOList.get(0).getUrl();
        try {
            // 推送集团消息(第二种推送方式)
            // 构造钉钉数据，推送钉钉
            StringBuilder markdown = new StringBuilder();
            markdown.append("![](").append(ucgConfig.getGroupImg()).append(")  \n  ");
//            markdown.append("##### ").append("**").append("IT工程师作业平台(旗效2.0)").append("**").append("  \n  ");
            markdown.append("##### ").append("**").append("旗效1.0").append("**").append("  \n  ");
            markdown.append("###### ").append("请查看数字化部非IT工程师角色的工时运营数据").append("  \n  ");
            markdown.append("###### 发布时间：").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));

            JSONObject templateData = new JSONObject();
//            templateData.put("title", "IT工程师作业平台(旗效2.0)");
            templateData.put("title", "旗效1.0");
            templateData.put("markdown", markdown.toString());
            templateData.put("single_title", "点击查看详情");
            templateData.put("single_url", url);

            JSONObject params = new JSONObject();
            params.put("agent_id", ucgConfig.getAgentId());
            params.put("template_data", templateData);
            params.put("template_id", "201");
            params.put("to_user", userList);
            params.put("user_type", "IDM");
            log.info("请求参数：{}", params.toJSONString());
            JSONObject result = messagePushFeignClient.sendDingMsg(params);
            log.info("请求结果：{}", result.toJSONString());
            if (!"0".equals(result.getString("code"))) {
                status = 2;
                failReason = result.getString("msg");
            }
        } catch (Exception e) {
            log.error("推送消息错误，{}", e.getMessage());
            status = 2;
            failReason = e.getMessage();
        }
        //更新推送状态
        for (GroupPushLogPO po : pushLogPOList) {
            GroupPushLogPO updatePO = new GroupPushLogPO();
            updatePO.setId(po.getId());
            updatePO.setPushTime(getNowStringDate());
            updatePO.setStatus(status);
            updatePO.setFailReason(failReason);
            groupPushLogMapper.updateByPrimaryKeySelective(updatePO);
        }
    }

    /**
     * 获取日期
     *
     * @param date 日期
     * @return {@link String }
     */
    private String getCreateDate(String date) {
        if (StringUtils.isBlank(date)) {
            return LocalDate.now().toString().substring(0, 10).replaceAll("-", "");
        }
        return date.substring(0, 10);
    }

    private String getNowStringDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }

    private String getSign(String code, String createDate) {
        String temp = code + ucgConfig.getUcgAppSecret() + createDate;
        return DigestUtils.md5Hex(DigestUtils.md5Hex(temp).toLowerCase()).toLowerCase();
    }
}




