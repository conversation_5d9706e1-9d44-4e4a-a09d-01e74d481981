package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.idempotent.IdempotentLock;
import com.faw.sa0214.tak.model.base.SafeResult;
import com.faw.sa0214.tak.model.request.*;
import com.faw.sa0214.tak.model.response.SafeRiskCloseApplicationDetailResponse;
import com.faw.sa0214.tak.model.response.SafeRiskDetailResponse;
import com.faw.sa0214.tak.model.response.SafeRiskResponse;
import com.faw.sa0214.tak.service.SafeRiskService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 安全风险任务控制器
 *
 * <AUTHOR>
 */
@Tag(name = "安全风险", description = "安全风险")
@RequestMapping("/safeRisk")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SafeRiskController {

    private final SafeRiskService safeRiskService;

    @PostMapping("/queryList")
    @Operation(summary = "风险列表查询",description = "[author:10200571]")
    public Result<PageInfo<SafeRiskResponse>> queryList(@Valid @RequestBody SafeRiskQueryRequest request) {
        return Result.success(safeRiskService.queryList(request));
    }

    @GetMapping("/queryDetail/{id}")
    @Operation(summary = "风险详情查询",description = "[author:10200571]")
    public Result<SafeRiskDetailResponse> queryDetail(@PathVariable String id) {
        return Result.success(safeRiskService.queryDetail(id));
    }

    @Operation(summary = "上传整改文件-返回的文件id放在data中", description = "[author:10200571]")
    @PostMapping(value = "/startUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> startUpload(@ModelAttribute SafeRiskSaveRequest request, @RequestPart("file") MultipartFile file) {
        return Result.success(safeRiskService.startUpload(request, file));
    }

    @Operation(summary = "删除整改文件", description = "[author:10200571]")
    @PostMapping(value = "/deleteFile")
    public Result<Boolean> deleteFile(@RequestBody SafeRiskBaseRequest request) {
        return Result.success(safeRiskService.deleteFile(request.getRiskId(), request.getId()));
    }

    @PostMapping("/saveOrSubmit")
    @Operation(summary = "保存或提交整改计划",description = "[author:10200571]")
    @IdempotentLock(expression = "#request.riskId")
    public Result<Boolean> saveOrSubmit(@Valid @RequestBody SafeRiskSaveRequest request) {
        return Result.success(safeRiskService.saveOrSubmit(request));
    }

    @PostMapping("/applyClose")
    @Operation(summary = "申请关闭风险单",description = "[author:10200571]")
    public Result<Boolean> applyClose(@RequestBody SafeRiskApplyCloseRequest request) {
        return Result.success(safeRiskService.applyClose(request));
    }

    @PostMapping("/receiveRisk")
    @Operation(summary = "前端不用-接收安全系统下发的风险单",description = "[author:10200571]")
    @IdempotentLock(prefix = "safe:", expression = "#request.issueId")
    public SafeResult<Boolean> receiveRisk(@RequestBody SafeRiskRequest request) {
        return SafeResult.success(safeRiskService.receiveRisk(request));
    }

    @PostMapping("/syncStatus")
    @Operation(summary = "前端不用-同步风险单状态",description = "[author:10200571]")
    public SafeResult<Boolean> syncStatus(@RequestBody SafeSyncStatusRequest request) {
        return SafeResult.success(safeRiskService.syncStatus(request));
    }

    @PostMapping("/closeApplicationDetail")
    @Operation(summary = "查看关闭申请详情",description = "[author:10200571]")
    public Result<SafeRiskCloseApplicationDetailResponse> closeApplicationDetail(@RequestBody SafeRiskBaseRequest request) {
        return Result.success(safeRiskService.getCloseApplicationDetail(request));
    }

    @Operation(summary = "安全文件下载",description = "[author:10200571]")
    @GetMapping("/proxyDownloadRestTemplate")
    public void proxyDownloadRestTemplate(@ModelAttribute SafeRiskBaseRequest req, HttpServletResponse response) {
        safeRiskService.proxyDownloadRestTemplate(req, response);
    }
}
