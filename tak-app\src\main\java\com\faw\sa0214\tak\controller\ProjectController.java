package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.user.UserCheck;
import com.faw.sa0214.tak.model.base.PageResult;
import com.faw.sa0214.tak.model.dto.SpecialProjectDTO;
import com.faw.sa0214.tak.model.request.SpecialProjectRequest;
import com.faw.sa0214.tak.po.SpecialProject;
import com.faw.sa0214.tak.service.SpecialProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 专项管理
 */
@Tag(name = "专项管理", description = "专项管理")
@RequestMapping("/requirement/special")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectController {

    private final SpecialProjectService specialProjectService;

    @Operation(summary = "新增专项", description = "新增专项 [author:50012536]")
    @PostMapping("/addSpecialProject")
    public Result<String> addSpecialProject(@RequestBody SpecialProject param) {
        return specialProjectService.addSpecialProject(param);
    }

    @UserCheck(interfaceRole = "takAdmin")
    @Operation(summary = "查询专项列表", description = "查询专项列表 [author:50012536]")
    @PostMapping("/getSpecialProjectList")
    public Result<PageResult<SpecialProjectDTO>> getSpecialProjectList(@RequestBody SpecialProjectRequest request) {
        return specialProjectService.getSpecialProjectList(request);
    }

    @Operation(summary = "修改专项状态", description = "修改专项状态(0:停用 1:启用)[author:50012536]")
    @PostMapping("/changeSpecialStatus")
    public Result<String> changeSpecialStatus(@RequestBody SpecialProject param) {
        return specialProjectService.changeSpecialStatus(param);
    }

    @Operation(summary = "查询专项信息", description = "查询专项信息[author:50012536]")
    @PostMapping("/getSpecialProjectById")
    public Result<SpecialProject> getSpecialProjectById(@RequestBody SpecialProjectDTO request) {
        return specialProjectService.getSpecialProjectById(request);
    }

    @Operation(summary = "修改专项信息", description = "修改专项信息[author:50012536]")
    @PostMapping("/updateSpecialProject")
    public Result<String> updateSpecialProject(@RequestBody SpecialProject param) {
        return specialProjectService.updateSpecialProject(param);
    }
}
