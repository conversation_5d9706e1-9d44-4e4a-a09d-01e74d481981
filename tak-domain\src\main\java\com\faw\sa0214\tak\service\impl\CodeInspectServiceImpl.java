package com.faw.sa0214.tak.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSON;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.CodeReviewClient;
import com.faw.sa0214.tak.client.GitLabClient;
import com.faw.sa0214.tak.client.dto.codeReview.*;
import com.faw.sa0214.tak.client.mq.RocketMqClient;
import com.faw.sa0214.tak.common.constant.CommonConstant;
import com.faw.sa0214.tak.common.constant.MessageConstant;
import com.faw.sa0214.tak.common.constant.enums.*;
import com.faw.sa0214.tak.common.constant.enums.code.CodeReviewLevelEnum;
import com.faw.sa0214.tak.common.constant.enums.code.CodeReviewProblemEnum;
import com.faw.sa0214.tak.common.constant.enums.code.CodeReviewScoreEnum;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.HttpUtil;
import com.faw.sa0214.tak.common.util.TextUtil;
import com.faw.sa0214.tak.config.AesEncryption;
import com.faw.sa0214.tak.config.RedisLock;
import com.faw.sa0214.tak.config.UcgConfig;
import com.faw.sa0214.tak.mapper.CodeReviewGitlabProjectMapper;
import com.faw.sa0214.tak.mapper.CodeReviewProjectProblemMapper;
import com.faw.sa0214.tak.mapper.TbDyProjectExtendMapper;
import com.faw.sa0214.tak.model.base.TakException;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.po.CodeReviewGitlabProjectPO;
import com.faw.sa0214.tak.po.CodeReviewProjectProblemPO;
import com.faw.sa0214.tak.po.TbDyProjectExtendPO;
import com.faw.sa0214.tak.service.AiScoreService;
import com.faw.sa0214.tak.service.CodeInspectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 代码检查服务
 *
 * <AUTHOR>
 * @since 2024/04/10
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CodeInspectServiceImpl implements CodeInspectService {
    @Value("${codeReview.key}")
    private String secretKey;

    @Value("${codeReview.rocketMq-topic}")
    private String codeReviewRocketMqTopic;

    @Value("${gitlab.token}")
    private String privateToken;


    private final AiScoreService aiScoreService;

    private final GitLabClient gitLabClient;

    private final CodeReviewClient codeReviewClient;

    private final RocketMqClient rocketMqClient;

    private final TbDyProjectExtendMapper tbDyProjectExtendMapper;

    private final CodeReviewGitlabProjectMapper codeReviewGitlabProjectMapper;

    private final CodeReviewProjectProblemMapper codeReviewProjectProblemMapper;

    private final UcgConfig ucgConfig;

    private final RedisLock redisLock;


    private static final List<Integer> DELIVERABLE_ITEM_ID = Arrays.asList(2, 15, 16, 22, 23, 49, 55, 56);

    private static final Pattern BIZ_ID_PATTERN = Pattern.compile("<(.*?)>");

    private static final String PR_COMMITS_PATTERN = "https://devops-gitlab.faw.cn/api/v4/projects/{0}/merge_requests/{1}/commits";


    @Override
    public Boolean handleWebHook(String payload) {
        Instant start = Instant.now();
        log.info("[CodeInspectService][handleWebHook][entrance] payload: {}.", payload);
        // 1 check param
        if (StringUtils.isBlank(payload)) {
            log.warn("[CodeInspectService][handleWebHook] 请求参数为空。");
            return Boolean.FALSE;
        }

        // 2 parse json to CodeMergeEventDTO
        CodeMergeEventDTO codeEvent = JSON.parseObject(payload, CodeMergeEventDTO.class);
        this.checkParams(codeEvent);

        // 3 check repeat operation
        String lockKey = String.format(
                CommonConstant.CODE_REVIEW_LOCK_KEY, codeEvent.getEventType(), codeEvent.getProject().getId(), codeEvent.getObjectAttributes().getIid());
        boolean lockFlag = redisLock.tryLock(lockKey, CommonConstant.CODE_REVIEW_LOCK_VALUE, CommonConstant.SECONDS_ONE_HOUR);
        if (!lockFlag) {
            log.info("[CodeInspectService][handleWebHook] repeat operation.");
            return Boolean.FALSE;
        }

        // 4 check that the project need to undergo code review
        CodeReviewGitlabProjectPO project = codeReviewGitlabProjectMapper.selectOne(codeEvent.getProject().getId());
        log.info("[CodeInspectService][handleWebHook] project: {}.", JSON.toJSONString(project));
        if (Objects.isNull(project) || StringUtils.isEmpty(project.getToken())
                || StringUtils.isEmpty(project.getScanBranch()) || StringUtils.isEmpty(project.getFileSuffix())) {
            boolean defaultScoreFlag =
                    this.defaultScoreHandler(codeEvent, privateToken, CodeReviewScoreEnum.NO_NEED_TO_REVIEW);
            log.info("[CodeInspectService][handleWebHook][before ai] defaultScoreFlag: {}.", defaultScoreFlag);
            return Boolean.TRUE;
        }
        // 4.1 decode token
        project.setToken(AesEncryption.decrypt(project.getToken(), secretKey));

        // 5 check whether the target branch is within the scope of the scan branch
        boolean scanBranchFlag =
                !JSON.parseArray(project.getScanBranch(), String.class).contains(codeEvent.getObjectAttributes().getTargetBranch());
        if (scanBranchFlag) {
            log.info("[CodeInspectService][handleWebHook] target branch is not in the scan branch.");
            return Boolean.TRUE;
        }

        // 6 build params of code review
        CodeReviewRequest codeReviewRequest = this.buildCodeReviewParams(codeEvent, project);
        String codeReviewRequestStr = JSON.toJSONString(codeReviewRequest);
        log.info("[CodeInspectService][handleWebHook] codeReviewRequest: {}.", codeReviewRequestStr);
        if (Objects.isNull(codeReviewRequest) || CollectionUtils.isEmpty(codeReviewRequest.getTaskList())) {
            return Boolean.TRUE;
        }

        // 7 send message to rocketMQ
        boolean sendFlag = rocketMqClient.sendMessage(codeReviewRocketMqTopic, project.getLanguageType(), codeReviewRequestStr);
        if (sendFlag) {
            log.info("[CodeInspectService][handleWebHook] send message to rocketMQ success.");
            return Boolean.TRUE;
        }

        // 8 send code review request
        Result<CodeReviewResponse> codeReviewResponse = codeReviewClient.codeReview(codeReviewRequest);
        log.info("[CodeInspectService][handleWebHook] codeReviewResponse: {}.", JSON.toJSONString(codeReviewResponse));
        if (Objects.isNull(codeReviewResponse)
                || Objects.isNull(codeReviewResponse.getData()) || CollectionUtils.isEmpty(codeReviewResponse.getData().getTaskList())) {

            boolean defaultScoreFlag =
                    this.defaultScoreHandler(codeEvent, project.getToken(), CodeReviewScoreEnum.CALL_BIG_MODEL_FAILED);
            log.info("[CodeInspectService][handleWebHook][after ai] defaultScoreFlag: {}.", defaultScoreFlag);

            return Boolean.TRUE;
        }

        // 9 compute and save score by taskId
        boolean operateFlag = this.computeAndSaveScore(codeReviewResponse.getData().getTaskList());
        log.info("[CodeInspectService][handleWebHook] operateFlag: {}.", operateFlag);

        log.info("[CodeInspectService][handleWebHook] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return Boolean.TRUE;
    }

    @Override
    public Boolean reviewResultCallback(CodeReviewRequest request) {
        Instant start = Instant.now();
        log.info("[CodeInspectService][reviewResultCallback][entrance] request: {}.", JSON.toJSONString(request));

        // 1 check params
        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getTaskList())) {
            log.warn("[CodeInspectService][reviewResultCallback] Request param is null.");
            throw new TakException(ErrorCodeEnum.REQUEST_PARAM_NULL);
        }

        // 2 compute and save score by taskId
        boolean operateFlag = this.computeAndSaveScore(request.getTaskList());
        log.info("[CodeInspectService][reviewResultCallback] operateFlag: {}.", operateFlag);

        log.info("[CodeInspectService][reviewResultCallback] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return Boolean.TRUE;
    }

    @Override
    public Boolean reviewProject(CodeReviewRequest request) {
        Instant start = Instant.now();
        log.info("[CodeInspectService][reviewProject][entrance] request: {}.", JSON.toJSONString(request));

        // 1 check params
        if (Objects.isNull(request)) {
            log.warn("[CodeInspectService][reviewProject] Request param is null.");
            throw new TakException(ErrorCodeEnum.REQUEST_PARAM_NULL);
        }

        // 2 select all projects
        List<CodeReviewGitlabProjectPO> projects = codeReviewGitlabProjectMapper.selectListByProjectIds(request.getProjectIdList());
        if (CollectionUtils.isEmpty(projects)) {
            log.warn("[CodeInspectService][reviewProject] Projects is empty.");
            return Boolean.FALSE;
        }

        // 3 review project
        for (CodeReviewGitlabProjectPO project : projects) {
            try {
                // 3.1 get scan branch
                String branch = JSON.parseArray(project.getScanBranch(), String.class).get(CommonConstant.FIRST_INDEX);
                // 3.2 build code review params
                CodeReviewProjectRequest projectRequest = CodeReviewProjectRequest.builder().remoteUrl(project.getGitUrl())
                        .branch(branch).projectId(String.valueOf(project.getProjectId())).scanType(JSON.parseArray(project.getScanType(), String.class))
                        .token(AesEncryption.decrypt(project.getToken(), secretKey)).minimumPriority(CodeReviewLevelEnum.BLOCKER_CRITICAL_MAJOR.getLevel()).build();
                // 3.3 code review
                Result<CodeReviewProjectResponse> projectResponse = codeReviewClient.reviewProject(projectRequest);
                if (Objects.isNull(projectResponse) || Objects.isNull(projectResponse.getData())
                        || CollectionUtils.isEmpty(projectResponse.getData().getProblemList())) {
                    continue;
                }
                // 3.4 save problems
                int affectRows = CommonConstant.INT_ZERO;
                CodeReviewProjectProblemPO po = CodeReviewProjectProblemPO.builder()
                        .projectId(project.getProjectId()).projectCode(project.getProjectCode()).branch(branch).build();

                for (CodeReviewProblemDTO problem : projectResponse.getData().getProblemList()) {
                    po.setFilePath(problem.getFilePath()).setLineNum(problem.getLineNum()).setRuleId(problem.getRuleId())
                            .setScanType(problem.getScanType()).setPriority(problem.getPriority())
                            .setDescription(
                                    Objects.nonNull(problem.getDescription()) && problem.getDescription().length() > CommonConstant.INTEGER_255
                                            ? problem.getDescription().substring(CommonConstant.FIRST_INDEX, CommonConstant.INTEGER_255) : problem.getDescription());

                    affectRows += codeReviewProjectProblemMapper.insert(po);
                }

                log.info("[CodeInspectService][reviewProject] projectCode: {}, problemNum: {}", project.getProjectCode(), affectRows);
            } catch (Exception e) {
                log.warn("[CodeInspectService][reviewProject] projectCode: {}, error: {}", project.getProjectCode(), e);
            }
        }

        log.info("[CodeInspectService][reviewProject] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return Boolean.TRUE;
    }

    @Override
    public Boolean reviewProblemCorrelateAuthor(CodeReviewRequest request) {
        Instant start = Instant.now();
        log.info("[CodeInspectService][reviewProblemCorrelateAuthor][entrance] request: {}.", JSON.toJSONString(request));

        // 1 check params
        if (Objects.isNull(request)) {
            log.warn("[CodeInspectService][reviewProblemCorrelateAuthor] Request param is null.");
            throw new TakException(ErrorCodeEnum.REQUEST_PARAM_NULL);
        }

        // 2 select all projects
        List<CodeReviewGitlabProjectPO> projects = codeReviewGitlabProjectMapper.selectListByProjectIds(request.getProjectIdList());
        if (CollectionUtils.isEmpty(projects)) {
            log.warn("[CodeInspectService][reviewProblemCorrelateAuthor] Projects is empty.");
            return Boolean.FALSE;
        }

        // 3 circulation
        log.info("[CodeInspectService][reviewProblemCorrelateAuthor][circulation] ----- start -----");
        for (CodeReviewGitlabProjectPO project : projects) {
            GitLabRequest gitLabRequest = GitLabRequest.builder()
                    .privateToken(AesEncryption.decrypt(project.getToken(), secretKey)).projectId(project.getProjectId()).build();

            try {
                this.reviewProblemHandler(project, gitLabRequest);
            } catch (Exception e) {
                log.warn("[CodeInspectService][reviewProblemCorrelateAuthor] projectCode: {}, error: {}", project.getProjectCode(), e);
            }
        }
        log.info("[CodeInspectService][reviewProblemCorrelateAuthor][circulation] -----  end  -----");

        log.info("[CodeInspectService][reviewProblemCorrelateAuthor] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return Boolean.TRUE;
    }

    /**
     * 代码审核问题负责人信息获取方法
     *
     * @param project 代码审核项目
     * @param gitLabRequest GitLab请求参数
     */
    private void reviewProblemHandler(CodeReviewGitlabProjectPO project, GitLabRequest gitLabRequest) {
        int affectRows = CommonConstant.INT_ZERO;

        int index = CommonConstant.FIRST_INDEX;
        int groupSize = CommonConstant.INTEGER_20;
        while (true) {
            List<CodeReviewProjectProblemPO> problems =
                    codeReviewProjectProblemMapper.selectByProjectId(project.getProjectId(), index, groupSize);
            if (CollectionUtils.isEmpty(problems)) {
                break;
            }

            for (CodeReviewProjectProblemPO problem : problems) {
                gitLabRequest.setBranch(problem.getBranch()).setStartNum(problem.getLineNum()).setEndNum(problem.getLineNum())
                        .setFilePath(TextUtil.extractAfterSlash(problem.getFilePath(), CommonConstant.INT_THREE));

                List<CodeRepositoryCommitDTO> commits = gitLabClient.getCommits(gitLabRequest);
                if (CollectionUtils.isEmpty(commits) || Objects.isNull(commits.get(CommonConstant.FIRST_INDEX).getCommit())) {
                    continue;
                }

                CommitDTO commit = commits.get(CommonConstant.FIRST_INDEX).getCommit();

                CodeReviewProjectProblemPO po = CodeReviewProjectProblemPO.builder().id(problem.getId())
                        .authoredDate(commit.getAuthoredDate()).authorName(commit.getAuthorName()).authorEmail(commit.getAuthorEmail()).build();

                affectRows += codeReviewProjectProblemMapper.update(po);
            }

            index += groupSize;
        }
        log.info("[CodeInspectService][reviewProblemCorrelateAuthor] projectCode: {}, affectRows: {}", project.getProjectCode(), affectRows);
    }

    /**
     * 参数校验
     *
     * @param codeEvent 代码合并事件
     */
    private void checkParams(CodeMergeEventDTO codeEvent) {
        if (Objects.isNull(codeEvent) || StringUtils.isBlank(codeEvent.getEventType())) {
            throw new TakException(ErrorCodeEnum.REQUEST_PARAM_NULL.getCode(), MessageConstant.EVENT_TYPE_EMPTY);
        }
        if (Objects.isNull(codeEvent.getProject()) || Objects.isNull(codeEvent.getProject().getId())) {
            throw new TakException(ErrorCodeEnum.REQUEST_PARAM_NULL.getCode(), MessageConstant.PROJECT_ID_EMPTY);
        }
        if (Objects.isNull(codeEvent.getObjectAttributes()) || Objects.isNull(codeEvent.getObjectAttributes().getIid())) {
            throw new TakException(ErrorCodeEnum.REQUEST_PARAM_NULL.getCode(), MessageConstant.MERGE_REQUEST_ID_EMPTY);
        }
        if (StringUtils.isBlank(codeEvent.getObjectAttributes().getSourceBranch())) {
            throw new TakException(ErrorCodeEnum.REQUEST_PARAM_NULL.getCode(), MessageConstant.SOURCE_BRANCH_EMPTY);
        }
    }

    /**
     * 代码审核默认分数处理方法
     *
     * @param codeEvent 代码合并事件
     * @param privateToken 秘钥
     * @param reviewType 默认分数处理类型
     * @return 操作成功与否
     */
    private boolean defaultScoreHandler(CodeMergeEventDTO codeEvent, String privateToken, CodeReviewScoreEnum reviewType) {
        Instant start = Instant.now();

        // 1 get all commits of merge request
        GitLabRequest gitLabRequest = GitLabRequest.builder().privateToken(privateToken)
                .projectId(codeEvent.getProject().getId()).iid(codeEvent.getObjectAttributes().getIid()).build();
        List<CodeCommitDTO> mergeRequestCommits = gitLabClient.getMergeRequestCommits(gitLabRequest);

        // 2 build params of calling DevOps
        List<AiCodeCallbackDTO> callbackParams = new ArrayList<>();
        for (CodeCommitDTO commit : mergeRequestCommits) {
            Matcher bizIdMatcher = BIZ_ID_PATTERN.matcher(commit.getTitle());
            if (!bizIdMatcher.find()) {
                continue;
            }

            String bizId = bizIdMatcher.group(CommonConstant.PATTERN_FIRST_INDEX);

            AiCodeCallbackDTO callbackParam = AiCodeCallbackDTO.builder().taskId(Long.valueOf(bizId)).score(reviewType.getDefaultScore())
                    .comment(reviewType.getDesc()).mergerId(commit.getId()).build();

            callbackParams.add(callbackParam);
        }

        // 3 callback to DevOps
        if (!callbackParams.isEmpty()) {
            String callDecOpsMessage = aiScoreService.codeCallback(callbackParams);
            log.info("[CodeInspectService][defaultScoreHandler] callback to DevOps: {}.", callDecOpsMessage);
        }

        log.info("[CodeInspectService][defaultScoreHandler] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return true;
    }

    /**
     * 构建代码审查请求参数
     *
     * @param codeEvent 代码合并事件
     * @param project 代码审核项目
     * @return 代码审查请求参数
     */
    private CodeReviewRequest buildCodeReviewParams(CodeMergeEventDTO codeEvent, CodeReviewGitlabProjectPO project) {
        Instant start = Instant.now();

        List<String> fileSuffixList = JSON.parseArray(project.getFileSuffix(), String.class);

        Map<String, List<CodeReviewFileDTO>> fileMap = new HashMap<>(CommonConstant.INTEGER_16);

        // 1 get all commits of merge request
        GitLabRequest gitLabRequest = GitLabRequest.builder().privateToken(project.getToken()).projectId(codeEvent.getProject().getId())
                .iid(codeEvent.getObjectAttributes().getIid()).branch(codeEvent.getObjectAttributes().getSourceBranch()).perPage(CommonConstant.INTEGER_20).build();
        List<CodeCommitDTO> mergeRequestCommits = gitLabClient.getMergeRequestCommits(gitLabRequest);
        for (CodeCommitDTO commit : mergeRequestCommits) {
            Matcher bizIdMatcher = BIZ_ID_PATTERN.matcher(commit.getMessage());
            if (!bizIdMatcher.find()) {
                continue;
            }

            String bizId = bizIdMatcher.group(CommonConstant.PATTERN_FIRST_INDEX);
            gitLabRequest.setCommitId(commit.getId());

            // 2 get all commit differences of commit
            List<CodeReviewFileDTO> fileList = this.buildCodeReviewFileList(gitLabRequest, fileSuffixList);

            if (fileMap.containsKey(bizId)) {
                fileMap.get(bizId).addAll(fileList);
            } else {
                fileMap.put(bizId, fileList);
            }
        }

        List<CodeReviewTaskDTO> taskList = new ArrayList<>();
        for (Map.Entry<String, List<CodeReviewFileDTO>> entry : fileMap.entrySet()) {
            taskList.add(CodeReviewTaskDTO.builder().bizId(entry.getKey()).fileList(entry.getValue()).build());
        }

        CodeReviewRequest codeReviewRequest = CodeReviewRequest.builder()
                .systemId(CommonConstant.SYSTEM_ID_KANBAN).languageType(project.getLanguageType())
                .minimumPriority(CodeReviewLevelEnum.BLOCKER_CRITICAL_MAJOR.getLevel())
                .scanType(JSON.parseArray(project.getScanType(), String.class)).taskList(taskList).filterProblem(Boolean.TRUE).build();

        log.info("[CodeInspectService][buildCodeReviewParams] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return codeReviewRequest;
    }

    /**
     * 构建同个commitID代码审查文件列表
     *
     * @param gitLabRequest GitLab请求参数
     * @param fileSuffixList 文件后缀白名单列表
     * @return 代码审查文件列表
     */
    private List<CodeReviewFileDTO> buildCodeReviewFileList(GitLabRequest gitLabRequest, List<String> fileSuffixList) {
        List<CodeReviewFileDTO> fileList = new ArrayList<>();

        int page = CommonConstant.INT_ONE;
        while (true) {
            List<CodeCommitDiffDTO> commitDiffs = gitLabClient.getRepositoryCommitDiffs(gitLabRequest.setPage(page++));
            if (CollectionUtils.isEmpty(commitDiffs)) {
                break;
            }

            for (CodeCommitDiffDTO diff : commitDiffs) {
                // 2.1 kill file by file suffix
                if (!fileSuffixList.contains(TextUtil.getFileExtension(diff.getNewPath()))) {
                    continue;
                }

                // 2.2 parse commit differences and get new line numbers
                List<Integer> newLineNumbers = this.parseCommitDiff(diff.getDiff());

                // 2.3 get content of differential file
                CodeRepositoryFileDTO repositoryFile = gitLabClient.getRepositoryFile(gitLabRequest.setFilePath(diff.getNewPath()));

                CodeReviewFileDTO file = CodeReviewFileDTO.builder().filePath(diff.getNewPath())
                        .content(repositoryFile.getContent()).newLineNum(newLineNumbers).diff(diff.getDiff()).build();
                fileList.add(file);
            }
        }

        return fileList;
    }

    /**
     * 解析commit差异，获取新增行号
     *
     * @param diffOutput commit差异
     * @return 新增行号列表
     */
    private List<Integer> parseCommitDiff(String diffOutput) {
        Instant start = Instant.now();

        List<Integer> newLineNumbers = new ArrayList<>();
        String[] lines = diffOutput.split(CommonConstant.SEPARATOR_LINE_FEED);

        int newLine = CommonConstant.FIRST_INDEX;

        for (String line : lines) {
            if (line.startsWith(CommonConstant.SEPARATOR_AT_AT)) {
                String[] parts = line.split(CommonConstant.SEPARATOR_SPACE);
                String newRange = parts[CommonConstant.THIRD_INDEX];

                String[] newParts = newRange.split(CommonConstant.SEPARATOR_COMMA);
                newLine = Integer.parseInt(newParts[CommonConstant.FIRST_INDEX].substring(CommonConstant.SECOND_INDEX)) - CommonConstant.INT_ONE;
                continue;
            }

            if (line.startsWith(CommonConstant.SEPARATOR_PLUS) && !line.startsWith(CommonConstant.SEPARATOR_PLUS_PLUS)) {
                newLineNumbers.add(++newLine);
            }
            else if (!line.startsWith(CommonConstant.SEPARATOR_MINUS) || line.startsWith(CommonConstant.SEPARATOR_MINUS_MINUS)) {
                newLine++;
            }
        }

        log.info("[CodeInspectService][parseCommitDiff] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return newLineNumbers;
    }

    /**
     * 计算代码审核分数及保存问题详情
     *
     * @param taskList 代码审核任务列表
     * @return 是否成功
     */
    private boolean computeAndSaveScore(List<CodeReviewTaskDTO> taskList) {
        Instant start = Instant.now();

        // 1 build params of calling DevOps
        List<AiCodeCallbackDTO> callbackParams = new ArrayList<>();

        for (CodeReviewTaskDTO task : taskList) {
            BigDecimal score = CodeReviewScoreEnum.CODE_REVIEW_SUCCESS.getDefaultScore();

            List<CodeReviewProblemDTO> problemList = CollectionUtils.isEmpty(task.getProblemList()) ? new ArrayList<>() : task.getProblemList();
            for (CodeReviewProblemDTO problem : problemList) {
                CodeReviewProblemEnum problemType = CodeReviewProblemEnum.getByLevel(problem.getPriority());
                score = score.subtract(problemType.getDeductScore());

                if (BigDecimal.ZERO.compareTo(score) >= 0) {
                    score = BigDecimal.ZERO;
                    break;
                }
            }

            AiCodeCallbackDTO callbackParam = AiCodeCallbackDTO.builder().taskId(Long.valueOf(task.getBizId())).score(score)
                    .comment(StringUtils.isEmpty(task.getDetailUrl()) ? CodeReviewScoreEnum.CODE_REVIEW_SUCCESS.getDesc() : task.getDetailUrl()).build();
            callbackParams.add(callbackParam);
        }

        // 2 callback to DevOps
        if (!callbackParams.isEmpty()) {
            String callDecOpsMessage = aiScoreService.codeCallback(callbackParams);
            log.info("[CodeInspectService][computeAndSaveScore] callback to DevOps: {}.", callDecOpsMessage);
        }

        log.info("[CodeInspectService][computeAndSaveScore] cost: {} ms.", Duration.between(start, Instant.now()).toMillis());
        return true;
    }

    @Override
    public List<CodeCommitDTO> getCommitInfo(String projectId, String iid) {

        // 获取gitlab根据项目及PR号查询推送的地址
        String prCommitsUrl = MessageFormat.format(PR_COMMITS_PATTERN, projectId, iid);

        // headers参数
        Map<String, String> headers = new HashMap<>();
        headers.put("PRIVATE-TOKEN", privateToken);

        // query参数
        Integer page = 1;
        Integer perPage = 100;
        Map<String, String> params = new HashMap<>();
        params.put("page", String.valueOf(page));
        params.put("per_page", String.valueOf(perPage));

        // 查询提交信息
        String response;
        List<CodeCommitDTO> codeCommitList = new ArrayList<>();
        List<CodeCommitDTO> codeCommits = Collections.emptyList();
        // TODO 需要加个次数的终止条件
        do {
            log.info("get commit info by iid, request url:{}, params:{}, headers:{}", prCommitsUrl, JSON.toJSONString(params), JSON.toJSON(headers));
            response = HttpUtil.sendGet(prCommitsUrl, params, headers);
            log.info("get commit info by iid, response:{}", response);
            if (StringUtils.isNotBlank(response)) {
                try {
                    codeCommits = JSON.parseArray(response, CodeCommitDTO.class);
                } catch (Exception e) {
                    // 一般是 响应报错问题，gitlab没有返回错误码，这里整体按没有数据处理 eg. {"message":"404 Project Not Found"}
                    log.warn("transfer error！", e);
                }
            }
            codeCommitList.addAll(codeCommits);
            page += 1;
            params.put("page", String.valueOf(page));
        } while (StringUtils.isNotBlank(response) && CollectionUtil.isNotEmpty(codeCommits));

        return codeCommitList;
    }

    @Override
    public List<CodeCommitDiffDTO> getCommitDiffInfo(String projectId, String id) {

        return null;
    }

    @Override
    public void encryption(List<Integer> ids) {

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        byte[] key = secretKey.getBytes();
        AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, key);

        List<CodeReviewGitlabProjectPO> projectList = codeReviewGitlabProjectMapper.selectByIds(ids);
        projectList.forEach(project -> {
            String originalToken = project.getToken();
            byte[] encrypted = aes.encrypt(originalToken);
            String encodedToken = Base64.encode(encrypted);
            codeReviewGitlabProjectMapper.updateTokenById(project.getId(), encodedToken, "运维");
        });

    }

    @Override
    public String decryption(String token) {

        byte[] key = secretKey.getBytes();
        AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, key);

        byte[] decrypted = aes.decrypt(token);
        return new String(decrypted);
    }

}
