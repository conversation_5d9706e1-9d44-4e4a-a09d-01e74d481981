package com.faw.sa0214.tak.model.dto.baseCenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "基础处用户中心返回职级信息")
public class PositionInfo {
    @Schema(description = "主键")
    private String id;
    @Schema(description = "租户 ID")
    private String tenantId;
    @Schema(description = "创建人")
    private String createdBy;
    @Schema(description = "创建时间")
    private String created;
    @Schema(description = "更新时间")
    private String updated;
    @Schema(description = "更新人")
    private String updatedBy;
    @Schema(description = "版本")
    private String version;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "码值")
    private String code;
    @Schema(description = "描述")
    private String discription;
    @Schema(description = "排序")
    private String sort;
    @Schema(description = "类型")
    private String type;
    @Schema(description = "1：删除; 0:未删除")
    private String deleted;
    @Schema(description = "1:启用; 0:弃用")
    private String enable;
}
