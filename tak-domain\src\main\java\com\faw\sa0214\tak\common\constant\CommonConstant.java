package com.faw.sa0214.tak.common.constant;

import java.util.*;

/**
 * 常量集合
 *
 * <AUTHOR>
 * @date 2023-09-18 18:29
 */
public interface CommonConstant {

    int FIRST_INDEX = 0;
    int SECOND_INDEX = 1;
    int THIRD_INDEX = 2;

    int PATTERN_FIRST_INDEX = 1;

    int FALSE = 0;
    int TRUE = 1;

    /**
     * 数字常量
     */
    int INT_ZERO = 0;
    int INT_ONE = 1;
    int INT_THREE = 3;
    Integer INTEGER_0 = 0;
    Integer INTEGER_1 = 1;
    Integer INTEGER_2 = 2;
    Integer INTEGER_3 = 3;
    Integer INTEGER_4 = 4;
    Integer INTEGER_5 = 5;
    Integer INTEGER_6 = 6;
    Integer INTEGER_7 = 7;
    Integer INTEGER_8 = 8;
    Integer INTEGER_9 = 9;
    Integer INTEGER_10 = 10;
    Integer INTEGER_16 = 16;
    Integer INTEGER_20 = 20;
    Integer INTEGER_50 = 50;
    Integer INTEGER_100 = 100;
    Integer INTEGER_255 = 255;
    Integer INTEGER_1000 = 1000;

    long SECONDS_ONE_HOUR = 3600L;

    Long LONG_3600L = 3600L;

    /**
     * redis key
     */
    String OPEN_API_TOKEN_REDIS_KEY = "tak:gatewayToken";
    String USER_CENTER_INFO_KEY = "tak:userCenter:info";

    String IT_BUSINESS_UNIT_REDIS_KEY_PREFIX = "tak:itBu:role:";
    String REPORT_REDIS_KEY_PREFIX = "tak:report:";
    String REQUIRE_USER_ROLE_REDIS_KEY_PREFIX = "tak:require:user:role:";
    String USER_CODE_REDIS_KEY_PREFIX = "tak:user:code:";
    String REQUIRE_TEAM_REDIS_KEY_PREFIX = "tak:require:team:";
    String REQUIRE_TEAM_USER_REDIS_KEY_PREFIX = "tak:require:team:user:";
    String PRODUCT_FLOW_REDIS_KEY_PREFIX = "tak:product:chain:flow:";
    String TASK_BASE_AMOUNT_KEY_PREFIX = "tak:ai:baseAmount:";
    String CODE_REVIEW_LOCK_KEY = "CodeReview:%s:%s:%s";
    String BASE_CENTER_POSITION_KEY = "tak:base:center:position";

    String IT_PM_METRIC_SCORE = "tak:metric:tripart:";

    /**
     * redis value
     */
    String CODE_REVIEW_LOCK_VALUE = "RepeatOperation";

    /**
     * 系统头信息
     */
    String TENANT_ID_HEADER_KEY = "QFCTid";
    String SYSTEM_ID_HEADER_KEY = "QFCSid";
    String DEFAULT_TENANT_ID = "YQJT";
    String DEFAULT_SYSTEM_ID = "SA-0214";

    /**
     * 日志相关
     */
    String TRACE_ID = "traceId";
    String SPAN_ID = "spanId";
    String IP = "ip";
    String URL = "url";

    /**
     * 周报周次
     * 周报总榜每月有五周
     */
    List<String> WEEK_ENUM = Arrays.asList("1", "2", "3", "4", "5");

    /**
     * 周报排序子角色
     */
    List<String> ROLE_ORDER = Arrays.asList("产品管理", "敏捷教练", "技术架构师", "前端", "后端", "测试", "UI", "项目管理",
            "业务架构", "数据管理", "数据科学", "运维", "平台", "基础设施", "安全技术", "安全管理");

    /**
     * 周报角色映射
     */
    Map<String, List<String>> ROLE_MAP = new LinkedHashMap<>(Map.ofEntries(
            Map.entry("产品管理工程师", Arrays.asList("产品管理", "UI")),
            Map.entry("前后端开发工程师", Arrays.asList("技术架构师", "前端", "后端")),
            Map.entry("软件质量管理工程师", Collections.singletonList("测试")),
            Map.entry("项目管理工程师", Collections.singletonList("敏捷教练"))
    ));
    /**
     * IT 工程师圈定角色
     */
    List<String> ROLE_ORDER_STATIC = Arrays.asList("产品管理", "UI", "前端", "后端", "技术架构师", "测试", "敏捷教练");
    List<String> SUB_ROLE = Arrays.asList("技术架构师", "后端", "测试", "产品管理", "UI", "敏捷教练", "前端");

    /**
     * 1.0 排除角色
     */
    List<String> APPLICATION_ROLE = Arrays.asList("产品管理工程师", "前后端开发工程师", "软件质量管理工程师", "UI设计师", "后端开发工程师");

    List<String> DOC_BUSINESS_UNIT = Arrays.asList("技术调研（架构）", "架构设计（架构）", "接口设计（架构）", "时序图设计（架构）", "数据库模型设计（架构）");

    /**
     * 同步大禹事项的类型
     */
    List<String> SCRUM_ITEM_TYPE = Arrays.asList("REQUIREMENT", "TASK", "DEFECT");

    List<String> REQUIREMENT_POOL_FLOW_LEVEL = Arrays.asList("L3", "L4", "L5");

    /**
     * 标准冲刺参考项目id
     */
    Long STANDARD_SPRINT_BUSINESS_ID = 4655970186L;

    /**
     * 安全问题生成需求固定参数：平台架构（单体平台、临时）、业务单元（临时任务）
     */
    String SINGLE_PLATFORM = "单体平台";
    String TEMP = "临时";
    String TEMP_TASK = "临时任务";

    /**
     * 需求池 - 需求研发 圈定操作角色
     */
    List<String> REQ_RELATED_ROLE = Arrays.asList("技术架构师", "测试", "敏捷教练");

    /**
     * 运维问题生成需求固定参数：标题前缀
     */
    String MAINTENANCE_TITLE_PREFIX = "【运维】";

    String ROLE_PRODUCT_MANAGER = "产品管理";
    String SCRUM_COACH = "敏捷教练";

    String SYSTEM_ID_KANBAN = "kanban";

    String SEPARATOR_LINE_FEED = "\n";
    String SEPARATOR_AT_AT = "@@";
    String SEPARATOR_SPACE = " ";
    String SEPARATOR_COMMA = ",";
    String SEPARATOR_PLUS = "+";
    String SEPARATOR_PLUS_PLUS = "++";
    String SEPARATOR_MINUS = "-";
    String SEPARATOR_MINUS_MINUS = "--";
    String ASTERISK_WILDCARD = "*";

    String PERMISSION_ALL = "all";

    String EMPTY_STRING = "";

    String SUCCESS_CODE_STR = "200";

    String INVOKER = "CSP";

    String SYSTEM = "SYSTEM";
}
