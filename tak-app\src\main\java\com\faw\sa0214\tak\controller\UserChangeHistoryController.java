package com.faw.sa0214.tak.controller;


import com.alibaba.fastjson.JSONObject;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.common.util.ApproveProcessService;
import com.faw.sa0214.tak.common.util.DateUtils;
import com.faw.sa0214.tak.common.util.UserThreadLocalUtil;
import com.faw.sa0214.tak.mapper.PersonalMapper;
import com.faw.sa0214.tak.model.base.TakException;
import com.faw.sa0214.tak.model.dto.SysTeamUserDTO;
import com.faw.sa0214.tak.po.UserChangeHistroy;
import com.faw.sa0214.tak.service.UserChangeHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 用户变更历史
 *
 * <AUTHOR>
 * @date 2024/1/23
 */
@Tag(name = "用户变更历史", description = "用户变更历史")
@RestController
@RequestMapping("/userChangeHistory")
@Slf4j
public class UserChangeHistoryController {
    @Autowired
    PersonalMapper personalMapper;
    @Autowired
    private UserChangeHistoryService userChangeHistoryService;
    @Autowired
    private ApproveProcessService approveProcessService;

    /**
     * 查询所有用户战队变更记录,由于战队信息变更不会太多,所以没有分页查询
     *
     * @return {@link Result }<{@link List }<{@link UserChangeHistroy }>>
     */
    @Operation(summary = "查询所有用户战队变更记录", description = "[author:********]")
    @PostMapping("/listAll")
    public Result<List<UserChangeHistroy>> listUerChangeHistory() {
        List<UserChangeHistroy> teamRoles = null;
        try {
            teamRoles = userChangeHistoryService.list();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failed("查询用户变更集合失败!!");
        }
        if (teamRoles != null && !teamRoles.isEmpty()) {
            return Result.success(teamRoles);
        }
        return null;
    }

    /**
     * 根据Id查询单个变更信息信息
     *
     * @param id 主键
     * @return {@link Result}<{@link UserChangeHistroy}>
     */
    @Operation(summary = "根据Id查询单个变更信息信息", description = "[author:********]")
    @PostMapping("/getUserChangeHistory")
    public Result<UserChangeHistroy> getUserChangeHistory(@RequestParam("id") String id) {
        if (id != null && !id.isEmpty()) {
            UserChangeHistroy teamInfo = null;
            try {
                teamInfo = userChangeHistoryService.getById(id);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("查询相关变更失败!!!");
            }
            if (teamInfo != null) {
                return Result.success(teamInfo);
            }
        }
        return null;
    }

    @Operation(summary = "根据Id查询单个变更信息信息", description = "[author:********]")
    @PostMapping("/getUserChangeHistoryById")
    public Result<UserChangeHistroy> getUserChangeHistoryById(@RequestParam("id") String id) {
        if (id != null && !id.isEmpty()) {
            UserChangeHistroy userChangeHistroy = null;
            try {
                userChangeHistroy = userChangeHistoryService.getById(id);
                if (userChangeHistroy != null && userChangeHistroy.getUserId() != null && !userChangeHistroy.getUserId().equals("")) {
                    SysTeamUserDTO sysTeamUserDTO = personalMapper.selectUserInfoByUserId(userChangeHistroy.getUserId());
                    userChangeHistroy.setUserName(sysTeamUserDTO.getUserName());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("根据Id查询相关变更失败!!!");
            }
            if (userChangeHistroy != null) {
                return Result.success(userChangeHistroy);
            }
        }
        return null;
    }

    /**
     * 根据用户id查询用户的变更记录
     *
     * @param userId 用户 ID
     * @return {@link Result}<{@link List}<{@link UserChangeHistroy}>>
     */
    @Operation(summary = "根据用户id查询用户的变更记录", description = "[author:********]")
    @PostMapping("/getUserChangeHistoryByUserId")
    public Result<List<UserChangeHistroy>> getUserChangeHistoryByUserId(@RequestParam("userId") String userId) {
        if (userId != null && !userId.isEmpty()) {
            List teamInfo = null;
            try {
                teamInfo = userChangeHistoryService.getUserChangeHistoryUserId(userId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("根据用户id查询相关变更失败!!!");
            }
            if (teamInfo != null) {
                return Result.success(teamInfo);
            }
        }
        return null;
    }

    /**
     * 保存队变更信息
     *
     * @param userChangeHistory 用户更改历史
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "保存队变更信息", description = "保存队变更信息[author:********]")
    @PostMapping("/addUserChangeHistroy")
    public Result<String> addUserChangeHistroy(@RequestBody UserChangeHistroy userChangeHistory) {
        if (userChangeHistory != null) {
            userChangeHistory.setCreatedBy(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            userChangeHistory.setCreatedBy(UserThreadLocalUtil.getCurrentUserCode());
            userChangeHistory.setUpdatedBy(UserThreadLocalUtil.getCurrentUserCode());
            boolean isSave = false;
            try {
                isSave = userChangeHistoryService.save(userChangeHistory);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("添加团队战队变更失败!");
            }
            if (isSave) {
                return Result.success(String.valueOf(isSave));
            } else {
                return Result.failed("添加团队战队变更失败!!!");
            }
        }
        return null;
    }

    /**
     * 根据团队id更新变更信息
     *
     * @param userChangeHistroy 用户更改历史
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "根据团队id更新变更信息", description = "[author:********]")
    @PostMapping("/updateUserChangeHistroy")
    public Result<String> updateUserChangeHistory(@RequestBody UserChangeHistroy userChangeHistroy) {
        if (userChangeHistroy != null && userChangeHistroy.getId() != null) {
            userChangeHistroy.setUpdatedTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
            String currentUserCode = UserThreadLocalUtil.getCurrentUserCode();
            if (StringUtils.isNotBlank(currentUserCode)) {
                userChangeHistroy.setUpdatedBy(currentUserCode);
            }
            boolean isSave;
            try {
                isSave = userChangeHistoryService.updateById(userChangeHistroy);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.failed("更新团队变更信息失败!");
            }
            if (isSave) {
                return Result.success(String.valueOf(true));
            }
        }
        return null;
    }

    /**
     * 提交审批流程
     *
     * @param userChangeHistory 用户更改历史
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "提交审批流程", description = "[author:********]")
    @PostMapping("/submitAdjustFlow")
    public Result<String> submitAdjustFlow(@RequestBody UserChangeHistroy userChangeHistory) {
        String loginAccount = userChangeHistory.getLoginAccount();
        if (StringUtils.isBlank(loginAccount)) {
            throw new TakException("申请人不在人员清单中，请先去人员清单添加人员！");
        }

        String currentUserCode = UserThreadLocalUtil.getCurrentUserCode();
        if (StringUtils.isNotBlank(currentUserCode)) {
            userChangeHistory.setCreatedBy(currentUserCode);
        }

        userChangeHistory.setCreatedTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
        String isSave = userChangeHistoryService.submitAdjustFlow(userChangeHistory);

        if (StringUtils.isNotBlank(isSave)) {
            return Result.success(isSave, "提交变更流程成功,请耐心等待。");
        } else {
            return Result.failed("提交流程变更失败!!");
        }
    }


    /**
     * 获取商家详细信息
     *
     * @param entityStr 实体
     * @return {@link String}
     */
    @Deprecated
    @Operation(summary = "获得代办信息接口", description = "[author:********]")
    @RequestMapping(value = "/getBusinessDetail", method = RequestMethod.POST)
    public String getBusinessDetail(@RequestBody String entityStr) {
        JSONObject entity = JSONObject.parseObject(entityStr);
        log.info("获得代办信息接口" + entity);
        Map map = new HashMap();
        String taskId = null;
        String folio = null;
        if (entity != null && entity.get("procInstId") != null && !entity.get("procInstId").equals("")) {
            taskId = entity.getString("procInstId");
        }
        if (entity != null && entity.get("folio") != null && !entity.get("folio").equals("")) {
            folio = entity.getString("folio");
        }
        if (folio == null || folio.equals("")) {
            return "业务id 为空!!";
        }
        if (StringUtils.isNotEmpty(folio)) {
            try {
                Integer.parseInt(folio);
            } catch (NumberFormatException e) {
                log.error(e.getMessage(), e);
                log.error("请求参数:taskId" + taskId + "业务busId:" + folio);
                return "业务id 不正确!!";
            }
        }
        map = userChangeHistoryService.getBusinessDetail(taskId, folio, JSONObject.toJSONString(entity));
        if (map != null && map.size() > 0) {
            log.error("获得代办信息接口返回信息" + JSONObject.toJSONString(map));
            return JSONObject.toJSONString(map);
        } else {
            return "查询信息为空!!请确认请求报文是否是json格式";
        }
    }

}
