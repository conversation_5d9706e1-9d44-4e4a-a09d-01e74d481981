package com.faw.sa0214.tak.model.dto.baseCenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "基础处用户中心返回分页结果类")
public class BCPage<T> {

    @Schema(description = "返回值")
    private List<T> records;
    @Schema(description = "总数")
    private int total;
    @Schema(description = "当前页数量")
    private int size;
    @Schema(description = "当前页")
    private int current;
    @Schema(description = "总数")
    private int totalAll;
    @Schema(description = "总数")
    private String summation;
    @Schema(description = "页数")
    private int pages;
}
