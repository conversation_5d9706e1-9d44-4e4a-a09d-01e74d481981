package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.model.request.*;
import com.faw.sa0214.tak.model.response.*;
import com.faw.sa0214.tak.service.RequirementDevelopService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 需求池-研发过程监控
 *
 * <AUTHOR>
 * @date 2024/02/28
 */
@Tag(name = "需求池-研发过程监控", description = "需求池-研发过程监控")
@RequestMapping("/requirement/develop")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RequirementDevelopController {

    private final RequirementDevelopService requirementDevelopService;

    @Operation(summary = "查看需求下故事【列表嵌套接口】", description = "[author:10027705]")
    @PostMapping("/storyList")
    public Result<List<StoryInfoResponse>> getStoryList(@Validated @RequestBody RequireStoryRequest request) {
        return Result.success(requirementDevelopService.getStoryList(request));
    }

    @Operation(summary = "新增及修改开口项", description = "[author:10027705]")
    @PostMapping("/saveOrUpdateOpenItem")
    public Result<Boolean> saveOrUpdateOpenItem(@Validated @RequestBody OpenItemsRequest request) {
        return Result.success(requirementDevelopService.saveOrUpdateOpenItem(request));
    }

    @Operation(summary = "提交确认开口项", description = "[author:10027705]")
    @PostMapping("/submitOpenItem")
    public Result<Boolean> submitOpenItem(@Validated @RequestBody OpenItemsRequest request) {
        Boolean result = requirementDevelopService.submitOpenItem(request);
        if (result) {
            return Result.success(true);
        } else {
            return Result.failed("提交确认开口项失败");
        }
    }

    @Operation(summary = "关闭开口项", description = "[author:10027705]")
    @PostMapping("/closeOpenItem")
    public Result<Integer> closeOpenItem(@RequestBody List<Integer> ids) {
        return Result.success(requirementDevelopService.closeOpenItem(ids));
    }

    @Operation(summary = "查看开口项", description = "[author:10027705]")
    @GetMapping("/getOpenItemListByScrumId")
    public Result<OpenItemResponse> getOpenItemListByScrumId(@RequestParam(value = "scrumId") Long scrumId) {
        return Result.success(requirementDevelopService.getOpenItemListByScrumId(scrumId));
    }

    @Deprecated
    @Operation(summary = "分页查询研发监控列表信息", description = "[author:10027705]")
    @PostMapping("/getOpenItemList")
    public Result<PageInfo<DevelopMonitorInfoResponse>> getOpenItemList(@RequestBody DevelopMonitorRequest request) {
        return Result.success(requirementDevelopService.getOpenItemList(request));
    }

    @Operation(summary = "填写或修改上线时间", description = "[author:10027705]")
    @PostMapping("/updateStoryOnlineDate")
    public Result<Boolean> updateStoryOnlineDate(@Valid @RequestBody StoryOnlineDateRequest request) {
        return Result.success(requirementDevelopService.updateStoryOnlineDate(request));
    }

    @Deprecated
    @Operation(summary = "需求研发查看", description = "需求研发查看[author:10027705]")
    @GetMapping("/developDetail")
    public Result<ProductAuditResponse> developDetail(@RequestParam Integer requirementId) {
        return Result.success(requirementDevelopService.developDetail(requirementId));
    }

    @Operation(summary = "提交需求研发", description = "提交需求研发[author:10027705]")
    @PostMapping("/developSubmit")
    public Result<Boolean> developSubmit(@Valid @RequestBody DevelopSubmitRequest request) {
        return Result.success(requirementDevelopService.developSubmit(request));
    }

    @Operation(summary = "查看故事准出条件", description = "查看故事准出条件[author:10027705]")
    @GetMapping("/getAcceptanceCriteria")
    public Result<String> getAcceptanceCriteria(@RequestParam(value = "scrumId") Long scrumId) {
        return Result.success(requirementDevelopService.getAcceptanceCriteria(scrumId));
    }

    @Operation(summary = "修改故事准出条件", description = "修改故事准出条件[author:10027705]")
    @PostMapping("/updateAcceptanceCriteria")
    public Result<Boolean> updateAcceptanceCriteria(@Valid @RequestBody AcceptanceCriteriaRequest request) {
        return Result.success(requirementDevelopService.updateAcceptanceCriteria(request));
    }

    @Operation(summary = "根据需求查询准出条件", description = "根据需求查询准出条件[author:10027705]")
    @GetMapping("/getAcceptance")
    public Result<List<AcceptanceResponse>> getAcceptance(@RequestParam(name = "requirementId") Integer requirementId) {
        return Result.success(requirementDevelopService.getAcceptance(requirementId));
    }

    @Operation(summary = "更新上线故事准出条件", description = "更新上线故事准出条件[author:10027705]")
    @PostMapping("/updateAcceptanceResult")
    public Result<Boolean> updateAcceptanceResult(@Valid @RequestBody List<AcceptanceCriteriaRequest> request) {
        return Result.success(requirementDevelopService.updateAcceptanceResult(request));
    }

}
