package com.faw.sa0214.tak.controller;

import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.aspect.token.TokenCheck;
import com.faw.sa0214.tak.model.dto.ReportOverallDTO;
import com.faw.sa0214.tak.model.vo.*;
import com.faw.sa0214.tak.service.PublicMinorReportService;
import com.faw.sa0214.tak.model.vo.BusinessReportVO;
import com.faw.sa0214.tak.service.ScheduleJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/28
 */
@Tag(name = "公网报告(副版周报)接口", description = "公网报告(副版周报)接口")
@RequestMapping("/public/minor")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PublicMinorReportController {

    private final ScheduleJobService scheduleJobService;

    private final PublicMinorReportService publicMinorReportService;

    /**
     * 钉钉周报-战队运营数据
     * 每周一8:00推送，数据范围历史30天
     */
    @Operation(summary = "钉钉周报-战队运营数据", description = "[author:10027705]")
    @GetMapping("/teamReport")
    @TokenCheck
    public Result<TeamReportVO> minorTeamOperationReport(@RequestParam(name = "sign", required = false) String sign,
                                                         @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                         @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(publicMinorReportService.minorTeamOperationReport(createdDate));
    }

    /**
     * 钉钉周报-战队运营数据 列表数据
     * 每周一8:00推送，数据范围历史30天
     */
    @Operation(summary = "钉钉周报-战队运营列表数据", description = "[author:50012536]")
    @GetMapping("/teamOperationList")
    @TokenCheck
    public Result<List<TeamOperateListVO>> minorTeamOperationList(@RequestParam(name = "sign", required = false) String sign,
                                                                  @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                                  @RequestParam(name = "createdDate") String createdDate) {
        List<TeamOperateListVO> result = publicMinorReportService.minorTeamOperationList(createdDate);
        return Result.success(result);
    }

    @Operation(summary = "旗效周报--工时运营--战队运营总体描述", description = "[author:10027705]")
    @GetMapping("/overall")
    @TokenCheck
    public Result<ReportOverallDTO> overall(@RequestParam(name = "sign", required = false) String sign,
                                            @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                            @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.minorOverall(createdDate));
    }

    /**
     * 旗效周报--工时运营--忙闲指数运营数据
     */
    @Operation(summary = "旗效周报--工时运营--忙闲指数运营数据", description = "[author:10027705]")
    @GetMapping("/busyAndIdleReport")
    @TokenCheck
    public Result<List<BusyAndIdleListVO>> busyAndIdleReportMinor(@RequestParam(name = "sign", required = false) String sign,
                                                             @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                             @RequestParam(name = "createdDate") String createdDate,
                                                             @RequestParam(name = "userType") Integer userType) {
        return Result.success(scheduleJobService.busyAndIdleReportMinor(createdDate, userType));
    }

    /**
     * 旗效周报--工时运营--忙闲指数运营描述
     */

    @Operation(summary = "旗效周报--工时运营--忙闲指数运营描述", description = "[author:10027705]")
    @GetMapping("/busyAndIdleDescribe")
    @TokenCheck
    public Result<String> busyAndIdleDescribeMinor(@RequestParam(name = "sign", required = false) String sign,
                                              @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                              @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.busyAndIdleDescribeMinor(createdDate));
    }

    /**
     * 旗效周报--工时运营--能力指数数据
     */

    @Operation(summary = "旗效周报--工时运营--能力指数数据", description = "[author:10027705]")
    @GetMapping("/capacityReport")
    @TokenCheck
    public Result<List<CapacityReportListVO>> capacityReportMinor(@RequestParam(name = "sign", required = false) String sign,
                                                             @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                             @RequestParam(name = "createdDate") String createdDate,
                                                             @RequestParam(name = "userType") Integer userType) {
        return Result.success(scheduleJobService.capacityReportMinor(createdDate, userType));
    }

    /**
     * 旗效周报--工时运营--工时矩阵数据
     */

    @Operation(summary = "旗效周报--工时运营--工时矩阵数据", description = "[author:10027705]")
    @GetMapping("/busyAndIdleMatrix")
    @TokenCheck
    public Result<List<BusyAndIdleMatrixVO>> busyAndIdleMatrixMinor(@RequestParam(name = "sign", required = false) String sign,
                                                               @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                               @RequestParam(name = "createdDate") String createdDate,
                                                               @RequestParam(name = "userType") Integer userType) {
        return Result.success(scheduleJobService.busyAndIdleMatrixMinor(createdDate, userType));
    }


    /**
     * 旗效周报--战队运营--查询业务单元运营数据
     */

    @Operation(summary = "旗效周报--业务单元运营--查询战队业务单元运营数据", description = "[author:10027705]")
    @GetMapping("/businessReport")
    @TokenCheck
    public Result<BusinessReportVO> businessReport(@RequestParam(name = "sign", required = false) String sign,
                                                   @RequestParam(name = "pushRegionName", required = false) String pushRegionName,
                                                   @RequestParam(name = "createdDate") String createdDate) {
        return Result.success(scheduleJobService.minorBusinessReport(createdDate));
    }
}
