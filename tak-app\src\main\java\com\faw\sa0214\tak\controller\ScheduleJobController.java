package com.faw.sa0214.tak.controller;

import com.alibaba.fastjson.JSONObject;
import com.dcp.common.rest.Result;
import com.faw.sa0214.tak.client.dto.devops.SmokeTestRequest;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.dto.operation.OperationSupportInfoDTO;
import com.faw.sa0214.tak.model.response.ProductManageResponse;
import com.faw.sa0214.tak.model.vo.*;
import com.faw.sa0214.tak.service.*;
import com.faw.sa0214.tak.service.operation.OperationSupportInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/05
 */
@Tag(name = "定时任务", description = "定时任务")
@Slf4j
@RestController
@RequestMapping("/schedule")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ScheduleJobController {

    private final AiTaskScoreEvaluationService aiTaskScoreEvaluationService;
    private final OperationSupportInfoService operationSupportInfoService;
    private final PlatformIssueManageService platformIssueManageService;
    private final AiProjectInfoService aiProjectInfoService;
    private final AiEvaluationService aiEvaluationService;
    private final PublicReportService publicReportService;
    private final RequirementService requirementService;
    private final ScheduleJobService scheduleJobService;
    private final ReqReportService reqReportService;
    private final PersonalService personalService;
    private final SafeRiskService safeRiskService;
    private final AiScoreService aiScoreService;
    private final MetricsService metricsService;

    /**
     * 发送钉钉工时录入提醒
     *
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "发送钉钉工时录入提醒", description = "[author:10027705]")
    @PostMapping("/sendDingTalkNotes")
    public Result<String> sendDingTalkNotes() {

        log.info("【schedule】-----sendDingTalkNotes task start！-----");

        scheduleJobService.sendDingTalkNotes();

        log.info("【schedule】-----sendDingTalkNotes task end!-----");

        return Result.success("sendDingTalkNotes-success");
    }

    /**
     * 获取钉钉考勤率
     *
     * @param attendanceDay 拉取日期 yyyy-MM-dd
     * @param flag          整月与单日的标识，默认0单日，1整月
     * @return {@link Result}<{@link String}>
     */

    @Operation(summary = "获取钉钉考勤率", description = "[author:10027705]")
    @PostMapping("/getDingTalkAttendance")
    public Result<String> getDingTalkAttendance(String attendanceDay, Integer flag) {

        log.info("【schedule】-----getDingTalkAttendance task start！-----");

        scheduleJobService.getDingTalkAttendance(attendanceDay, flag);

        log.info("【schedule】-----getDingTalkAttendance task end!-----");

        return Result.success("getDingTalkAttendance-success");
    }

    /**
     * 获取钉钉每日请假时长
     *
     * @param date 日期
     * @return {@link Result}<{@link Integer}>
     */

    @Operation(summary = "定时拉取钉钉每日请假时长", description = "[author:10027705]")
    @PostMapping("/getDingTalkLeave")
    public Result<Integer> getDingTalkLeave(String date) {

        log.info("【schedule】-----getDingTalkLeave task start！-----");

        Integer count = scheduleJobService.getDingTalkLeave(date);

        log.info("【schedule】-----getDingTalkLeave task end！");

        return Result.success(count);
    }

    /**
     * 同步大禹已删除项目
     * <p>
     * 在任务和工时同步前执行，避免早期删除后期恢复的任务被删掉
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link Result}<{@link String}>
     */
    @Operation(summary = "同步大禹删除事项", description = "[author:10027705]")
    @PostMapping("/syncDeletedItems")
    public Result<String> syncDeletedItems(String startDate, String endDate) {

        log.info("[syncDeletedItems] ----- start -----");

        scheduleJobService.syncDeletedItems(startDate, endDate);

        log.info("[syncDeletedItems] ----- end -----");

        return Result.success("syncDeletedItems-success");
    }

    /**
     * 获取大禹任务列表
     * （默认7天）
     * 计划完成时间，实际完成时间
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link Result}<{@link String}>
     */

    @Operation(summary = "获取大禹任务列表", description = "[author:10027705]")
    @PostMapping("/getIworkProject")
    public Result<String> getIworkProject(String startDate, String endDate) {

        log.info("【schedule】-----getIworkProject task start！-----");

        scheduleJobService.getIworkProject(startDate, endDate);

        log.info("【schedule】-----getIworkProject task end, asynchronous method!-----");

        return Result.success("getIworkProject-success");
    }

    /**
     * 获取大禹工时列表 默认7天）
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link Result}<{@link ?}>
     */

    @Operation(summary = "获取大禹工时列表", description = "[author:10027705]")
    @PostMapping("/getIworkHour")
    public Result<String> getIworkHour(String startDate, String endDate) {

        log.info("【schedule】-----getIworkHour task start！-----");

        scheduleJobService.getIworkHour(startDate, endDate);

        log.info("【schedule】-----getIworkHour task end, asynchronous method!-----");

        return Result.success("getIworkHour-success");
    }

    /**
     * 获取大禹迭代列表 默认7天）
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link Result }<{@link String }>
     */
    @Operation(summary = "获取大禹迭代信息", description = "[author:10027705]")
    @PostMapping("/getIworkSprint")
    public Result<String> getIworkSprint(String startDate, String endDate) {

        log.info("【schedule】-----getIworkSprint task start！-----");

        scheduleJobService.getIworkSprint(startDate, endDate);

        log.info("【schedule】-----getIworkSprint task end, asynchronous method!");

        return Result.success("getIworkSprint-success");
    }

    /**
     * 获取服务稳定性指标得分-前一天
     * @param date 统计日期
     * @return {@link Result }<{@link String }>
     */
    @Operation(summary = "获取服务稳定性指标得分", description = "[author:10027705]")
    @PostMapping("/getApdexScore")
    public Result<String> getApdexScore(String date) {

        log.info("【schedule】-----getApdexScore task start！-----");

        scheduleJobService.getApdexScore(date);

        log.info("【schedule】-----getApdexScore task end!");

        return Result.success("getApdexScore-success");
    }

    /**
     * 获取大禹自动化用例 （默认7天）
     * @param startDate 开始日期 yyyy-MM-dd HH:mm:ss
     * @param endDate   结束日期 yyyy-MM-dd HH:mm:ss
     * @return {@link Result }<{@link String }>
     */
    @Operation(summary = "获取大禹自动化用例", description = "[author:50012536]")
    @PostMapping("/getIworkAutoCase")
    public Result<String> getIworkAutoCase(String startDate, String endDate) {

        log.info("【schedule】-----getIworkAutoCase task start！-----");

        scheduleJobService.getIworkAutoCase(startDate, endDate);

        log.info("【schedule】-----getIworkAutoCase task end, asynchronous method!");

        return Result.success("getIworkAutoCase-success");
    }

    /**
     * 获取大禹测试用例 （默认7天）
     * @param startDate 开始日期 yyyy-MM-dd HH:mm:ss
     * @param endDate   结束日期 yyyy-MM-dd HH:mm:ss
     * @return {@link Result }<{@link String }>
     */
    @Operation(summary = "获取大禹测试用例", description = "[author:50012536]")
    @PostMapping("/getIworkTestCase")
    public Result<String> getIworkTestCase(String startDate, String endDate) {

        log.info("【schedule】-----getIworkTestCase task start！-----");

        scheduleJobService.getIworkTestCase(startDate, endDate);

        log.info("【schedule】-----getIworkTestCase task end, asynchronous method!");

        return Result.success("getIworkTestCase-success");
    }

    /**
     * 获取大禹冒烟测试用例 （默认7天）
     * 开始日期 yyyy-MM-dd HH:mm:ss
     * 结束日期 yyyy-MM-dd HH:mm:ss
     * @return {@link Result }<{@link String }>
     */
    @Operation(summary = "获取大禹冒烟测试用例", description = "[author:50012536]")
    @PostMapping("/getIworkSmokeTest")
    public Result<String> getIworkSmokeTest(@RequestBody(required = false) SmokeTestRequest request) {

        log.info("【schedule】-----getIworkSmokeTest task start！-----");

        scheduleJobService.getIworkSmokeTest(request);

        log.info("【schedule】-----getIworkSmokeTest task end, asynchronous method!");

        return Result.success("getIworkSmokeTest-success");
    }

    /**
     * 获取 AI 任务信息列表
     * （开始与结束存在空或null的时候默认赋值最近7天的起止日期 1：00：00）
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Operation(summary = "获取 AI 任务信息列表", description = "[author:10027705]")
    @PostMapping("/getAiTaskInfoList")
    public Result<Integer> getAiTaskInfoList(String startDate, String endDate) {

        log.info("【schedule】-----getAiTaskInfoList task start！-----");

        int count = scheduleJobService.getAiTaskInfoList(startDate, endDate);

        log.info("【schedule】-----getAiTaskInfoList task end，update data number {}-----", count);

        return Result.success(count);
    }

    /**
     * 获取平台下发整改问题
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return {@link Result }<{@link String }>
     */
    @Operation(summary = "获取平台下发整改问题", description = "[author:10027705]")
    @PostMapping("/getPlatformIssues")
    public Result<String> getPlatformIssues(String startDate, String endDate) {

        log.info("【schedule】-----getPlatformIssues task start！-----");

        int count = scheduleJobService.getPlatformIssues(startDate, endDate);

        log.info("【schedule】-----getPlatformIssues task end！update data number {}-----", count);

        return Result.success("getPlatformIssues-success");
    }

    @Operation(summary = "交付物评分补偿", description = "交付物评分补偿，每日执行2次[author:50012536]")
    @PostMapping(value = "/delAiCompensation")
    public Result<String> delAiCompensation(@RequestBody(required = false) AiCompensationDTO dto) {
        String msg = aiScoreService.delAiCompensation(dto);
        return Result.success(msg);
    }

    /**
     * 定时任务使用，定时打分；每两分钟运行一次
     */
    @Operation(summary = "定时任务使用，定时打分", description = "[author:10027705]")
    @PostMapping("goToScore")
    @Deprecated
    public Result<Boolean> goToScore() {
        return aiScoreService.goToScore();
    }

    /**
     * 定时任务使用，定时给已经打分但没有评价的文档进行回调；每天早晨5点运行一次
     */
    @Operation(summary = "定时任务补偿", description = "[author:10027705]")
    @PostMapping("goToCallbackDevops")
    @Deprecated
    public Result<Boolean> goToCallbackDevops() {
        return aiScoreService.goToCallbackDevops();
    }

    /**
     * 计算员工效能分数
     * 每日凌晨 5 点计算分数
     * 计算范围： 当月，全量
     */
    @Operation(summary = "计算员工效能分数", description = "[author:10027705]")
    @PostMapping("calcEvaluationScore")
    public Result<String> calcEvaluationScore(@RequestBody(required = false) AITaskScoreEvaluationDTO dto) {
        log.info("【schedule】----- calcEvaluationScore task start！-----");
        return Result.success(scheduleJobService.calcEvaluationScore(dto));
    }

    /**
     * 计算员工效能 - 运营结果数据
     * 每日凌晨 3点 执行计算前一天分数
     */
    @Operation(summary = "计算员工效能 - 运营结果数据", description = "[author:50012536]")
    @PostMapping("calcOperationalScore")
    public Result<String> calcOperationalScore(@RequestBody(required = false) CalcIndicatorDTO dto) {
        log.info("【schedule】----- calcOperationalScore task start！-----");
        return Result.success(scheduleJobService.calcOperationalScore(dto));
    }

    /**
     * 按迭代计算交付质量数据
     */
    @Operation(summary = "按迭代计算交付质量数据", description = "[author:50012536]")
    @PostMapping("calcDigitalIndicator")
    public Result<String> calcDigitalIndicator(@RequestBody(required = false) CalcIndicatorDTO dto) {
        log.info("【schedule】----- calcDigitalIndicator task start！-----");
        return Result.success(scheduleJobService.calcDigitalIndicator(dto));
    }

    /**
     * 定时拉取EaMap流程
     */
    @Operation(summary = "定时拉取EaMapL1流程", description = "[author:10027705]")
    @PostMapping("/insertL1FlowList")
    public Result<String> insertL1FlowList() {

        log.info("【schedule】-----insertL1FlowList task start！-----");
        String info = scheduleJobService.insertL1FlowList();
        log.info("【schedule】-----insertL1FlowList task end！");

        return Result.success(info);
    }

    /**
     * 定时拉取EaMap流程
     *
     * @return {@link Result}<{@link List}<{@link EaMapFlowDTO}>>
     */
    @Operation(summary = "定时拉取EaMap流程", description = "[author:10027705]")
    @PostMapping("/insertFlowList")
    public Result<String> insertFlowList() {

        log.info("【schedule】-----insertFlowList task start！-----");
        String info = scheduleJobService.insertFlowList();
        log.info("【schedule】-----insertFlowList task end！");

        return Result.success(info);
    }

    /**
     * 定时拉取EaMap业务单元
     *
     * @return {@link Result}<{@link List}<{@link EaMapBizUnitDTO}>>
     */
    @Operation(summary = "定时拉取EaMap业务单元", description = "[author:10027705]")
    @PostMapping("/insertBizUnitList")
    public Result<String> insertBizUnitList() {

        log.info("【schedule】-----insertBizUnitList task start！-----");
        String info = scheduleJobService.insertBizUnitList();
        log.info("【schedule】-----insertBizUnitList task end！");

        return Result.success(info);
    }

    /**
     * 定时更新三方人员信息
     *
     * @return {@link Result}<{@link Integer}>
     */
    @Operation(summary = "定时更新三方人员信息", description = "[author:10027705]")
    @PostMapping("/updateItPmUserInfo")
    public Result<Integer> updateItPmUserInfo() {

        log.info("【schedule】-----updateItPmUserInfo task start！-----");
        Integer info = scheduleJobService.updateItPmUserInfo();
        log.info("【schedule】-----updateItPmUserInfo task end！");

        return Result.success(info);
    }

    /**
     * 月度IT业务单元调用封档
     *
     * @param date 日期
     * @return {@link Result}<{@link Integer}>
     */

    @Operation(summary = "月度IT业务单元调用封档", description = "[author:10027705]")
    @PostMapping("/recordItBuDataMonthly")
    public Result<Integer> recordItBuDataMonthly(String date) {

        log.info("【schedule】-----recordItBuDataMonthly task start！-----");
        Integer info = scheduleJobService.recordItBuDataMonthly(date);
        log.info("【schedule】-----recordItBuDataMonthly task end！");

        return Result.success(info);
    }


    @Operation(summary = "旗效--角色运营分析", description = "[author:10027705]")
    @PostMapping("/roleAnalysis")
    public Result<Integer> roleAnalysis(String date) {

        log.info("【schedule】-----roleAnalysis task start！-----");
        Integer info = scheduleJobService.roleAnalysis(date);
        log.info("【schedule】-----roleAnalysis task end！");

        return Result.success(info);
    }

    @Operation(summary = "旗效--角色运营-角色大榜", description = "[author:10027705]")
    @PostMapping("/roleRankAnalysis")
    public Result<String> roleRankAnalysis(String date) {

        log.info("【schedule】-----roleRankAnalysis task start！-----");
        String info = scheduleJobService.roleRankAnalysis(date);
        log.info("【schedule】-----roleRankAnalysis task end！");

        return Result.success(info);
    }

    /**
     * 钉钉周报-战队运营数据
     * 每周一8:00推送，数据范围历史30天
     */
    @Operation(summary = "钉钉周报-战队运营数据", description = "[author:10027705]")
    @PostMapping("/teamOperationReport")
    public Result<TeamReportVO> teamOperationReport(String createdDate) {

        log.info("【schedule】-----teamOperationReport task start！-----");
        TeamReportVO result = scheduleJobService.teamOperationReport(createdDate);
        log.info("【schedule】-----teamOperationReport task end！-----");

        return Result.success(result);
    }

    /**
     * 钉钉周报-战队运营数据 列表数据
     * 每周一8:00推送，数据范围历史30天
     */
    @Operation(summary = "钉钉周报-战队运营列表数据", description = "[author:10027705]")
    @PostMapping("/teamOperationList")
    public Result<List<TeamOperateListVO>> teamOperationList(String createdDate) {

        log.info("【schedule】-----teamOperationList task start！-----");
        List<TeamOperateListVO> result = scheduleJobService.teamOperationList(createdDate);
        log.info("【schedule】-----teamOperationList task end！-----");

        return Result.success(result);
    }

    /**
     * 钉钉周报-战队运营-正态分布数据
     * 每周一8:00推送，数据范围历史30天
     */
    @Operation(summary = "钉钉周报-战队运营-正态分布数据", description = "[author:10027705]")
    @PostMapping("/teamOperationDistributionList")
    public Result<TeamOperateDistributionListVO> teamOperationDistributionList(String createdDate) {

        log.info("【schedule】-----teamOperationDistributionList task start！-----");
        TeamOperateDistributionListVO result = scheduleJobService.teamOperationDistributionList(createdDate);
        log.info("【schedule】-----teamOperationDistributionList task end！-----");

        return Result.success(result);
    }

    /**
     * 钉钉周报-业务单元运营数据
     * 每周一8:00推送，数据范围历史2周
     */
    @Operation(summary = "钉钉周报（正副版）-业务单元运营数据", description = "[author:10027705]")
    @PostMapping("/businessReport")
    public Result<BusinessReportVO> businessReport(String createdDate) {

        log.info("【schedule】----- businessReport task start！-----");
        BusinessReportVO result = scheduleJobService.businessReport(createdDate);
        scheduleJobService.minorBusinessReport(createdDate);
        log.info("【schedule】----- businessReport task end！-----");

        return Result.success(result);
    }

    /**
     * 钉钉周报-工时运营-忙闲指数数据
     * 每周一8:00推送
     * 数据范围：
     * 1、本月数据 就是 8号以后
     * 2、上月数据  1-7号推送
     */
    @Operation(summary = "钉钉周报-工时运营-忙闲指数数据", description = "[author:10027705]")
    @PostMapping("/busyAndIdleReport")
    public Result<List<BusyAndIdleListVO>> busyAndIdleReport(String createdDate) {

        log.info("【schedule】-----busyAndIdleReport task start！-----");
        List<BusyAndIdleListVO> result = scheduleJobService.busyAndIdleReport(createdDate, -200);
        log.info("【schedule】-----busyAndIdleReport task end！-----");

        return Result.success(result);
    }

    @Operation(summary = "钉钉周报-副版-工时运营-忙闲指数数据", description = "[author:10027705]")
    @PostMapping("/busyAndIdleReportMinor")
    public Result<List<BusyAndIdleListVO>> busyAndIdleReportMinor(String createdDate) {

        log.info("【schedule】-----busyAndIdleReportMinor task start！-----");
        List<BusyAndIdleListVO> result = scheduleJobService.busyAndIdleReportMinor(createdDate, -200);
        log.info("【schedule】-----busyAndIdleReportMinor task end！-----");

        return Result.success(result);
    }

    /**
     * 忙闲指数描述
     */
    @Operation(summary = "钉钉周报-工时运营-忙闲指数描述", description = "根据createdDate获取描述[author:10027705]")
    @PostMapping("/busyAndIdleDescribe")
    public Result<String> busyAndIdleDescribe(String createdDate) {
        log.info("【schedule】-----busyAndIdleDescribe task start！-----");
        String des = scheduleJobService.busyAndIdleDescribe(createdDate);
        log.info("【schedule】-----busyAndIdleDescribe task end！-----");
        return Result.success(des);
    }

    @Operation(summary = "钉钉周报-副版-工时运营-忙闲指数描述", description = "[author:10027705]")
    @PostMapping("/busyAndIdleDescribeMinor")
    public Result<String> busyAndIdleDescribeMinor(String createdDate) {
        log.info("【schedule】-----busyAndIdleDescribeMinor task start！-----");
        String des = scheduleJobService.busyAndIdleDescribeMinor(createdDate);
        log.info("【schedule】-----busyAndIdleDescribeMinor task end！-----");
        return Result.success(des);
    }

    /**
     * 钉钉周报-工时运营-能力指数数据
     * 每周一8:00推送
     * 数据范围：
     * 1、本月数据 就是 8号以后
     * 2、上月数据  1-7号推送
     */
    @Operation(summary = "钉钉周报-工时运营-能力指数数据", description = "[author:10027705]")
    @PostMapping("/capacityReport")
    public Result<List<CapacityReportListVO>> capacityReport(String createdDate) {

        log.info("【schedule】-----capacityReport task start！-----");
        List<CapacityReportListVO> result = scheduleJobService.capacityReport(createdDate, -200);
        log.info("【schedule】-----capacityReport task end！-----");

        return Result.success(result);
    }

    @Operation(summary = "钉钉周报-副版-工时运营-能力指数数据", description = "[author:10027705]")
    @PostMapping("/capacityReportMinor")
    public Result<List<CapacityReportListVO>> capacityReportMinor(String createdDate) {

        log.info("【schedule】-----capacityReportMinor task start！-----");
        List<CapacityReportListVO> result = scheduleJobService.capacityReportMinor(createdDate, -200);
        log.info("【schedule】-----capacityReportMinor task end！-----");

        return Result.success(result);
    }

    /**
     * 钉钉周报-工时运营-工时矩阵数据
     * 每周一8:00推送
     * 数据范围：
     * 1、本月数据 就是 8号以后
     * 2、上月数据  1-7号推送
     */
    @Operation(summary = "钉钉周报-工时运营-工时矩阵数据", description = "[author:10027705]")
    @PostMapping("/busyAndIdleMatrix")
    public Result<List<BusyAndIdleMatrixVO>> busyAndIdleMatrix(String createdDate) {

        log.info("【schedule】-----busyAndIdleMatrix task start！-----");
        List<BusyAndIdleMatrixVO> result = scheduleJobService.busyAndIdleMatrix(createdDate, -200);
        log.info("【schedule】-----busyAndIdleMatrix task end！-----");

        return Result.success(result);
    }

    @Operation(summary = "钉钉周报-副版-工时运营-工时矩阵数据", description = "[author:10027705]")
    @PostMapping("/busyAndIdleMatrixMinor")
    public Result<List<BusyAndIdleMatrixVO>> busyAndIdleMatrixMinor(String createdDate) {

        log.info("【schedule】-----busyAndIdleMatrixMinor task start！-----");
        List<BusyAndIdleMatrixVO> result = scheduleJobService.busyAndIdleMatrixMinor(createdDate, -200);
        log.info("【schedule】-----busyAndIdleMatrixMinor task end！-----");

        return Result.success(result);
    }

    /**
     * 钉钉周报--角色运营--查询角色运营总览
     */
    @Operation(summary = "钉钉周报-查询角色运营总览", description = "[author:10027705]")
    @PostMapping("/getRoleReportOverall")
    public Result<List<RoleReportDTO>> getRoleReportOverall(String createdDate) {

        log.info("【schedule】-----getRoleReportOverall task start！-----");
        List<RoleReportDTO> result = publicReportService.getRoleReportOverall(createdDate);
        log.info("【schedule】-----getRoleReportOverall task end！-----");

        return Result.success(result);
    }

    /**
     * 钉钉周报--查询角色运营总榜
     */
    @Operation(summary = "钉钉周报-查询角色运营总榜", description = "[author:10027705]")
    @PostMapping("/getRoleReportOverallRankData")
    public Result<RoleOverallRankDTO> getRoleReportOverallRankData(String createdDate) {

        log.info("【schedule】-----getRoleReportOverallRankData task start！-----");
        RoleOverallRankDTO result = publicReportService.getRoleReportOverallRankData(createdDate);
        log.info("【schedule】-----getRoleReportOverallRankData task end！-----");

        return Result.success(result);
    }

    @Operation(summary = "创建钉钉周报", description = "[author:10027705]")
    @PostMapping("/createDingWeekly")
    public Result<String> createDingWeekly(@RequestBody(required = false) PushDingDTO dto) {
        log.info("创建钉钉周报开始：{}", JSONObject.toJSONString(dto));
        scheduleJobService.createDingWeekly(dto);
        log.info("创建钉钉周报结束：{}", JSONObject.toJSONString(dto));
        return Result.success("createDingWeekly-success");
    }

    @Operation(summary = "创建钉钉周报-副版", description = "[author:10027705]")
    @PostMapping("/createDingWeeklyMinor")
    public Result<String> createDingWeeklyMinor(@RequestBody(required = false) PushDingDTO dto) {
        log.info("创建钉钉周报-副版开始：{}", JSONObject.toJSONString(dto));
        scheduleJobService.createDingWeeklyMinor(dto);
        log.info("创建钉钉周报-副版结束：{}", JSONObject.toJSONString(dto));
        return Result.success("createDingWeeklyMinor-success");
    }

    /**
     * 推送钉钉周报
     *
     * @param dto 推送参数
     * @return {@link Result}<{@link Integer}>
     */
    @Operation(summary = "推送钉钉周报", description = "[author:10027705]")
    @PostMapping("/pushDingWeekly")
    public Result<String> pushDingWeekly(@RequestBody(required = false) PushDingDTO dto) {

        log.info("【schedule】-----pushDingWeekly task start！-----");
        scheduleJobService.pushDingWeekly(dto);
        log.info("【schedule】-----pushDingWeekly task end！");

        return Result.success("pushDingWeekly-success");
    }

    /**
     * 推送钉钉周报-副版
     *
     * @param dto 推送参数
     * @return {@link Result}<{@link Integer}>
     */
    @Operation(summary = "推送钉钉周报-副版", description = "[author:10027705]")
    @PostMapping("/pushDingWeeklyMinor")
    public Result<String> pushDingWeeklyMinor(@RequestBody(required = false) PushDingDTO dto) {
        log.info("【schedule】-----pushDingWeeklyMinor task start！-----");
        scheduleJobService.pushDingWeeklyMinor(dto);
        log.info("【schedule】-----pushDingWeeklyMinor task end！");
        return Result.success("pushDingWeeklyMinor-success");
    }

    @Operation(summary = "定时任务完成异常情况钉钉监控", description = "[author:10027705]")
    @PostMapping(value = "/dingNotes")
    public Result<String> dingNotes() {
        log.info("[dingNotes] ----- start -----");
        String msg = scheduleJobService.dingNotes();
        log.info("[dingNotes] ----- end -----");
        return Result.success(msg);
    }

    @Operation(summary = "旗效周报(正副版)--工时运营--战队运营总体描述", description = "[author:10027705]")
    @PostMapping("/teamOverall")
    public Result<ReportOverallDTO> teamOverall(String createdDate) {

        log.info("【schedule】-----teamOverall task start！-----");
        ReportOverallDTO overall = scheduleJobService.overall(createdDate);
        scheduleJobService.minorOverall(createdDate);
        log.info("【schedule】-----teamOverall task end！-----");

        return Result.success(overall);
    }


    @Operation(summary = "定时清理ai_score表", description = "[author:10027705]")
    @PostMapping(value = "/delAiScorePaper")
    @Deprecated
    public Result<String> delAiScorePaper() {
        log.info("[delAiScore] ----- start -----");
        String msg = scheduleJobService.delAiScorePaper();
        log.info("[delAiScore] ----- end -----");
        return Result.success(msg);
    }

    @Operation(summary = "定时任务更新项目相关信息", description = "[author:10027705]")
    @PostMapping(value = "/updateTakBusiness")
    public Result<String> updateTakBusiness() {
        log.info("[updateTakBusiness] ----- start -----");
        scheduleJobService.updateTakBusiness();
        log.info("[updateTakBusiness] ----- end -----");
        return Result.success("updateTakBusiness-success");
    }

    /**
     * 需求周报-饼图数据
     * 每周五凌晨统计，本周一到周四的数据
     */
    @Operation(summary = "需求周报-饼图数据", description = "[author:10027705]")
    @PostMapping("/getRequirePieReport")
    public Result<RequirementPieReportDTO> getRequirePieReport() {

        log.info("【schedule】-----getRequirePieReport task start！-----");
        Result<RequirementPieReportDTO> result = reqReportService.getRequirePieReport(null);
        log.info("【schedule】-----getRequirePieReport task end！-----");

        return result;
    }

    /**
     * 需求周报-用户列表数据
     * 每周五凌晨统计，本周一到周四的数据
     */
    @Operation(summary = "需求周报-用户列表数据", description = "[author:10027705]")
    @PostMapping("/getRequireUserReport")
    public Result<List<RequirementUserReportDTO>> getRequireUserReport() {

        log.info("【schedule】-----getRequireUserReport task start！-----");
        Result<List<RequirementUserReportDTO>> result = reqReportService.getRequireUserReport(null);
        log.info("【schedule】-----getRequireUserReport task end！-----");

        return result;
    }

    /**
     * 同步用户中心用户数据
     */
    @Operation(summary = "同步用户中心数据", description = "[author:10027705]")
    @PostMapping("/asyncUserCenterData")
    public Result<String> asyncUserCenterData() {

        log.info("【schedule】-----asyncUserCenterData task start！-----");
        Result<String> result = personalService.asyncUserCenterData();
        log.info("【schedule】-----asyncUserCenterData task end！-----");

        return result;
    }

    /**
     * 定时任务-需求池需求亮灯逻辑
     */
    @Operation(summary = "定时任务-需求池需求亮灯逻辑", description = "[author:50012536]")
    @PostMapping("/dealRequirementLight")
    @Deprecated
    public Result<String> dealRequirementLight() {
        return requirementService.dealRequirementLight();
    }

    /**
     * 需求池周报(新)-产品运营封板
     *
     * @param freshDate 刷新日期
     * @return {@link Result }<{@link List }<{@link ProductManageResponse }>>
     */
    @Operation(summary = "需求池周报(新)-产品运营封板", description = "[author:10027705]")
    @PostMapping("/getRequirementProductManageReport")
    public Result<List<ProductManageResponse>> getRequirementProductManageReport(String freshDate) {

        log.info("【schedule】-----getRequirementProductManageReport task start！-----");

        return Result.success(scheduleJobService.getRequirementProductManageReport(freshDate));
    }

    /**
     * 需求池--安全--更新异常状态（灯）
     *
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/updateExceptionStatus")
    @Operation(summary = "前端不用-更新风险单异常状态", description = "[author:10200571]")
    public Result<Boolean> updateExceptionStatus() {
        return Result.success(safeRiskService.updateExceptionStatus());
    }

    /**
     * 需求池--运维--全局扫描未完结任务-未关闭的-自动亮灯1
     *
     * @param request 请求
     * @return {@link Result }<{@link Integer }>
     */
    @Operation(summary = "全局扫描未完结任务-未关闭的-自动亮灯1", description = "[author:10236535]")
    @PostMapping("/overallJob")
    public Result<Integer> overallJob(@RequestBody OperationSupportInfoDTO request) {
        // 超时亮灯预警
        operationSupportInfoService.overallJob(request);
        // 到期自动关闭
        operationSupportInfoService.businessPersonnelJob(request);
        return Result.success(1);
    }

    /**
     * 需求池--业务需求--更新逾期状态
     *
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/updateRequirementOverdueStatus")
    @Operation(summary = "需求池--业务需求--更新逾期状态", description = "[author:10027705]")
    public Result<Boolean> updateRequirementOverdueStatus() {
        return Result.success(requirementService.updateRequirementOverdueStatus());
    }

    /**
     * 更新平台问题状态
     *
     * @return {@link Result }<{@link Integer }>
     */
    @PostMapping("/updatePlatformIssueStatus")
    @Operation(summary = "需求池--技术需求--更新状态预警", description = "[author:10027705]")
    public Result<Integer> updatePlatformIssueStatus() {
        platformIssueManageService.updatePlatformIssueStatus();
        return Result.success(1);
    }

    /**
     * 更新所有AI项目数据状态
     */
    @PostMapping("/updateAiProjectStatus")
    @Operation(summary = "更新所有AI项目数据状态--定时任务使用", description = "[author:10027705]")
    public Result<String> updateAllStatus() {
        aiProjectInfoService.updateAllStatus();
        return Result.success(null);
    }

    /**
     * Sla预警机器人钉钉-推送
     * @return
     */

    @Operation(summary = "Sla预警机器人钉钉-推送")
    @PostMapping(value = "/slaDingNotes")
    public Result<String> slaDingNotes() {
        log.info("[slaDingNotes] ----- start -----");
        String msg = scheduleJobService.slaDingNotes();
        log.info("[slaDingNotes] ----- end -----");
        return Result.success(msg);
    }

}
