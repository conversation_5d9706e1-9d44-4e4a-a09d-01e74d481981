package com.faw.sa0214.tak.controller;

import com.faw.sa0214.tak.model.base.BaseResult;
import com.faw.sa0214.tak.model.dto.*;
import com.faw.sa0214.tak.model.vo.*;
import com.faw.sa0214.tak.service.*;
import com.faw.sa0214.tak.service.operation.OperationMsgNoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/03/07
 */
@Tag(name = "定时扫描推送日志表", description = "定时扫描推送日志表")
@Slf4j
@RestController
@RequestMapping("/noticeLog")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NoticeLogJobController {

    private final OperationMsgNoticeService operationMsgNoticeService;

    /**
     * 推送消息
     * @param msg 消息体
     * @param userCode 人员
     */
    @Operation(summary = "推送消息", description = "[author:10027705]")
    @PostMapping("/noticeMsg")
    public BaseResult<String> noticeMsg(@Param("msg") String msg, @Param("userCode") String userCode) {
        return operationMsgNoticeService.noticeMsg(msg, userCode);
    }

    /**
     * 按日期扫描日志失败重新推送
     * @param date 刷新日期
     */
    @Operation(summary = "按日期扫描日志失败重新推送", description = "[author:10027705]")
    @PostMapping("/rePush")
    public BaseResult<String> rePush(@Param("date") String date) {
        return BaseResult.success(operationMsgNoticeService.rePush(date));
    }
}
